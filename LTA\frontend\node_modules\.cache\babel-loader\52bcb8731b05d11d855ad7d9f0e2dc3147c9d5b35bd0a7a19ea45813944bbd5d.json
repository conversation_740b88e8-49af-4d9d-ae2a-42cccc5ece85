{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\DefectMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';\nimport axios from 'axios';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\n/**\r\n * Enhanced Image Display Component for Map Popups\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EnhancedMapImageDisplay = ({\n  defect\n}) => {\n  _s();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n\n  // Generate comprehensive image URL with multiple fallback options\n  const generateImageUrl = defectData => {\n    if (!defectData) return null;\n    console.log('🔍 Generating map image URL for:', defectData.image_id);\n\n    // Priority 1: Try pre-signed URL (most secure and reliable)\n    if (defectData.original_image_presigned_url) {\n      console.log('🔗 Using pre-signed URL for map image');\n      return defectData.original_image_presigned_url;\n    }\n\n    // Priority 2: Try S3 full URL with proxy\n    if (defectData.original_image_full_url) {\n      console.log('🔗 Using full URL field');\n      // Extract S3 key from full URL and use proxy endpoint\n      const urlParts = defectData.original_image_full_url.split('/');\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n        const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n        console.log('✅ Generated proxy URL from full URL:', proxyUrl);\n        return proxyUrl;\n      }\n    }\n\n    // Priority 3: Try S3 key with proxy endpoint\n    if (defectData.original_image_s3_url) {\n      console.log('🔗 Using S3 key field');\n      const s3Key = defectData.original_image_s3_url;\n      const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\n      console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\n      return proxyUrl;\n    }\n\n    // Priority 4: Try direct S3 URL (fallback)\n    if (defectData.original_image_full_url) {\n      console.log('🔗 Using direct S3 URL as fallback');\n      return defectData.original_image_full_url;\n    }\n\n    // Priority 5: Try GridFS endpoint\n    if (defectData.original_image_id) {\n      console.log('🗄️ Using GridFS endpoint');\n      return `/api/pavement/get-image/${defectData.original_image_id}`;\n    }\n    console.warn('❌ No valid image URL found for defect:', defectData.image_id);\n    return null;\n  };\n\n  // Initialize image URL\n  useEffect(() => {\n    const url = generateImageUrl(defect);\n    console.log('🖼️ Setting initial map image URL:', url);\n    setCurrentImageUrl(url);\n    setHasError(false);\n    setIsLoading(true);\n    setFallbackAttempts(0);\n  }, [defect]);\n\n  // Handle image load error with fallback attempts\n  const handleImageError = e => {\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\n    if (fallbackAttempts === 0) {\n      // First fallback: Try GridFS if we haven't already\n      const gridfsId = defect.original_image_id;\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\n        setCurrentImageUrl(gridfsUrl);\n        setFallbackAttempts(1);\n        return;\n      }\n    }\n\n    // All fallbacks exhausted\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\n    setHasError(true);\n    setIsLoading(false);\n  };\n  const handleImageLoad = () => {\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\n    setIsLoading(false);\n    setHasError(false);\n  };\n\n  // Don't render anything if no URL available\n  if (!currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), \" Image not available\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          minHeight: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          variant: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2 small\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render error state\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small p-2 border rounded bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), \" Image unavailable\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render successful image\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-3 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: \"Defect image\",\n      className: \"img-fluid border rounded\",\n      style: {\n        maxHeight: '150px',\n        maxWidth: '100%'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-primary fw-bold\",\n        children: \"\\uD83D\\uDCF7 Original Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), fallbackAttempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-warning\",\n          children: \"(Fallback source)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n\n// Fix for the Leaflet default icon issue\n_s(EnhancedMapImageDisplay, \"PL1c4k1ZSbH8d+OQcD09kxd231s=\");\n_c = EnhancedMapImageDisplay;\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Custom marker icons for different defect types using location icons\nconst createCustomIcon = (color, isVideo = false) => {\n  const iconSize = isVideo ? [36, 36] : [32, 32];\n\n  // Create different SVG content for video vs image markers\n  const svgContent = isVideo ?\n  // Video marker with camera icon made from SVG shapes (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\n    </svg>` :\n  // Image marker with standard location pin (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>`;\n  return L.icon({\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\n    iconSize: iconSize,\n    iconAnchor: [iconSize[0] / 2, iconSize[1]],\n    popupAnchor: [0, -iconSize[1]]\n  });\n};\nconst icons = {\n  pothole: createCustomIcon('#FF0000'),\n  // Red for potholes\n  crack: createCustomIcon('#FFCC00'),\n  // Yellow for cracks (alligator cracks)\n  kerb: createCustomIcon('#0066FF'),\n  // Blue for kerb defects\n  // Video variants\n  'pothole-video': createCustomIcon('#FF0000', true),\n  // Red for pothole videos\n  'crack-video': createCustomIcon('#FFCC00', true),\n  // Yellow for crack videos\n  'kerb-video': createCustomIcon('#0066FF', true) // Blue for kerb videos\n};\nfunction DefectMap({\n  user\n}) {\n  _s2();\n  const [defects, setDefects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [center] = useState([20.5937, 78.9629]); // India center\n  const [zoom] = useState(6); // Country-wide zoom for India\n\n  // Filters for the map\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [usersList, setUsersList] = useState([]);\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\n    pothole: true,\n    crack: true,\n    kerb: true\n  });\n\n  // Fetch all images with defects and coordinates\n  const fetchDefectData = async (forceRefresh = false) => {\n    try {\n      var _response$data$images;\n      setLoading(true);\n      setError(null); // Clear any previous errors\n\n      // Prepare query parameters\n      let params = {};\n      if (startDate) params.start_date = startDate;\n      if (endDate) params.end_date = endDate;\n      if (selectedUser) params.username = selectedUser;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Always add cache-busting parameter to ensure latest data\n      params._t = Date.now();\n\n      // Add additional cache-busting for force refresh\n      if (forceRefresh) {\n        params._force = 'true';\n        params._refresh = Math.random().toString(36).substring(7);\n      }\n      console.log('🔄 Fetching defect data with params:', params);\n      const response = await axios.get('/api/dashboard/image-stats', {\n        params,\n        // Disable axios caching\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      console.log('📊 API response received:', {\n        success: response.data.success,\n        totalImages: response.data.total_images,\n        imageCount: (_response$data$images = response.data.images) === null || _response$data$images === void 0 ? void 0 : _response$data$images.length\n      });\n      if (response.data.success) {\n        // Process the data to extract defect locations with type information\n        const processedDefects = [];\n        response.data.images.forEach(image => {\n          try {\n            // Only process images with valid coordinates\n            if (image.coordinates && image.coordinates !== 'Not Available') {\n              var _image$exif_data;\n              let lat, lng;\n\n              // First, try to use EXIF GPS coordinates if available (most accurate)\n              if ((_image$exif_data = image.exif_data) !== null && _image$exif_data !== void 0 && _image$exif_data.gps_coordinates) {\n                lat = image.exif_data.gps_coordinates.latitude;\n                lng = image.exif_data.gps_coordinates.longitude;\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.image_id}: [${lat}, ${lng}]`);\n              } else {\n                // Fallback to stored coordinates\n                // Handle different coordinate formats\n                if (typeof image.coordinates === 'string') {\n                  // Parse coordinates (expected format: \"lat,lng\")\n                  const coords = image.coordinates.split(',');\n                  if (coords.length === 2) {\n                    lat = parseFloat(coords[0].trim());\n                    lng = parseFloat(coords[1].trim());\n                  }\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\n                  // Handle array format [lat, lng]\n                  lat = parseFloat(image.coordinates[0]);\n                  lng = parseFloat(image.coordinates[1]);\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\n                  // Handle object format {lat: x, lng: y}\n                  lat = parseFloat(image.coordinates.lat);\n                  lng = parseFloat(image.coordinates.lng);\n                }\n                console.log(`📍 Using stored coordinates for ${image.image_id}: [${lat}, ${lng}]`);\n              }\n\n              // Validate coordinates are within reasonable bounds\n              if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                console.log(`✅ Valid coordinates for image ${image.id}: [${lat}, ${lng}]`);\n                processedDefects.push({\n                  id: image.id,\n                  image_id: image.image_id,\n                  type: image.type,\n                  position: [lat, lng],\n                  defect_count: image.defect_count,\n                  timestamp: new Date(image.timestamp).toLocaleString(),\n                  username: image.username,\n                  original_image_id: image.original_image_id,\n                  // For cracks, include type information if available\n                  type_counts: image.type_counts,\n                  // For kerbs, include condition information if available\n                  condition_counts: image.condition_counts,\n                  // EXIF and metadata information\n                  exif_data: image.exif_data || {},\n                  metadata: image.metadata || {},\n                  media_type: image.media_type || 'image',\n                  original_image_full_url: image.original_image_full_url\n                });\n              } else {\n                console.warn(`❌ Invalid coordinates for image ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\n              }\n            } else {\n              console.log(`⚠️ Skipping image ${image.id}: coordinates=${image.coordinates}`);\n            }\n          } catch (coordError) {\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\n          }\n        });\n        console.log('Processed defects:', processedDefects.length);\n        setDefects(processedDefects);\n\n        // If no defects were processed, show a helpful message\n        if (processedDefects.length === 0) {\n          setError('No defects found with valid coordinates for the selected date range');\n        }\n      } else {\n        console.error('API returned success: false', response.data);\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\n      }\n      setLoading(false);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error fetching defect data:', err);\n      setError('Failed to load defect data: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n      setLoading(false);\n    }\n  };\n\n  // Fetch the list of users for the filter dropdown\n  const fetchUsers = async () => {\n    try {\n      const params = {};\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n      const response = await axios.get('/api/users/all', {\n        params\n      });\n      if (response.data.success) {\n        setUsersList(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  // Initialize default dates for the last 30 days\n  useEffect(() => {\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    console.log('DefectMap component initialized');\n  }, []);\n\n  // Fetch data when component mounts and when filters change\n  useEffect(() => {\n    fetchDefectData();\n    fetchUsers();\n  }, [user]);\n\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\n  useEffect(() => {\n    const interval = setInterval(() => {\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\n      fetchDefectData(true); // Force refresh to get latest EXIF data\n    }, 30000); // 30 seconds\n\n    return () => {\n      console.log('🛑 Clearing auto-refresh interval');\n      clearInterval(interval);\n    };\n  }, [startDate, endDate, selectedUser, user === null || user === void 0 ? void 0 : user.role]);\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    fetchDefectData(true);\n  };\n\n  // Handle filter application\n  const handleApplyFilters = () => {\n    fetchDefectData();\n  };\n\n  // Handle resetting filters\n  const handleResetFilters = () => {\n    // Reset date filters to last 30 days\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    setSelectedUser('');\n    setDefectTypeFilters({\n      pothole: true,\n      crack: true,\n      kerb: true\n    });\n\n    // Refetch data with reset filters\n    fetchDefectData();\n  };\n\n  // Handle defect type filter changes\n  const handleDefectTypeFilterChange = type => {\n    setDefectTypeFilters(prevFilters => ({\n      ...prevFilters,\n      [type]: !prevFilters[type]\n    }));\n  };\n\n  // Filter defects based on applied filters\n  const filteredDefects = defects.filter(defect => defectTypeFilters[defect.type]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"shadow-sm dashboard-card mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-0\",\n        children: \"Defect Map View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Date Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: startDate,\n                    onChange: e => setStartDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: endDate,\n                    onChange: e => setEndDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"User Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Select User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedUser,\n                    onChange: e => setSelectedUser(e.target.value),\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 23\n                    }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: user.username,\n                      children: [user.username, \" (\", user.role, \")\"]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Defect Type Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls defect-type-filters\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"defect-type-filter-options\",\n              style: {\n                marginTop: \"0\",\n                paddingLeft: \"0\",\n                width: \"100%\",\n                minWidth: \"150px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"pothole-filter\",\n                label: \"Potholes\",\n                checked: defectTypeFilters.pothole,\n                onChange: () => handleDefectTypeFilterChange('pothole'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"crack-filter\",\n                label: \"Cracks\",\n                checked: defectTypeFilters.crack,\n                onChange: () => handleDefectTypeFilterChange('crack'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"kerb-filter\",\n                label: \"Kerbs\",\n                checked: defectTypeFilters.kerb,\n                onChange: () => handleDefectTypeFilterChange('kerb'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-actions-container\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              onClick: handleApplyFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Apply Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: handleResetFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              onClick: handleRefresh,\n              disabled: loading,\n              className: \"filter-btn\",\n              children: loading ? '🔄 Refreshing...' : '🔄 Refresh Map'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF7 Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Defects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF9 Videos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pothole Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Crack Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"map-container\",\n        style: {\n          height: '500px',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(MapContainer, {\n          center: center,\n          zoom: zoom,\n          style: {\n            height: '100%',\n            width: '100%'\n          },\n          scrollWheelZoom: true,\n          children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n            attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n            url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this), filteredDefects.map(defect => {\n            var _defect$exif_data, _defect$exif_data$gps, _defect$exif_data$gps2, _defect$exif_data2, _defect$exif_data3, _defect$exif_data4, _defect$metadata, _defect$metadata$form, _defect$metadata2, _defect$metadata2$bas, _defect$metadata3, _defect$metadata3$bas, _defect$metadata4, _defect$metadata4$for;\n            // Determine icon based on media type and defect type\n            const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\n            const selectedIcon = icons[iconKey] || icons[defect.type];\n            return /*#__PURE__*/_jsxDEV(Marker, {\n              position: defect.position,\n              icon: selectedIcon,\n              children: /*#__PURE__*/_jsxDEV(Popup, {\n                maxWidth: 400,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-popup\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: [defect.type.charAt(0).toUpperCase() + defect.type.slice(1), \" Defect\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3 text-center\",\n                    children: [defect.representative_frame ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: `data:image/jpeg;base64,${defect.representative_frame}`,\n                        alt: \"Video thumbnail\",\n                        className: \"img-fluid border rounded shadow-sm\",\n                        style: {\n                          maxHeight: '150px',\n                          maxWidth: '100%',\n                          objectFit: 'cover'\n                        },\n                        onError: e => {\n                          console.warn(`Failed to load representative frame for video ${defect.image_id}`);\n                          e.target.style.display = 'none';\n                          // Show fallback message\n                          const fallback = e.target.nextElementSibling;\n                          if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\n                            fallback.style.display = 'block';\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-video\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 667,\n                          columnNumber: 33\n                        }, this), \" Video thumbnail unavailable\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-muted small p-3 border rounded bg-light\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-video fa-2x mb-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: \"Video thumbnail not available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-info fw-bold\",\n                        children: \"\\uD83D\\uDCF9 Video Thumbnail\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 29\n                      }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted d-block\",\n                          children: [\"Video ID: \", defect.video_id]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 680,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 25\n                  }, this), defect.media_type !== 'video' && /*#__PURE__*/_jsxDEV(EnhancedMapImageDisplay, {\n                    defect: defect\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Basic Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 694,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Count:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 31\n                        }, this), \" \", defect.defect_count]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 696,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Date:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 697,\n                          columnNumber: 31\n                        }, this), \" \", defect.timestamp]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 697,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Reported by:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 698,\n                          columnNumber: 31\n                        }, this), \" \", defect.username]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Media Type:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 699,\n                          columnNumber: 31\n                        }, this), \" \", defect.media_type]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 699,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"GPS:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 700,\n                          columnNumber: 31\n                        }, this), \" \", defect.position[0].toFixed(6), \", \", defect.position[1].toFixed(6)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 700,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 23\n                  }, this), defect.type === 'crack' && defect.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Crack Types\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: Object.entries(defect.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [type, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 710,\n                          columnNumber: 46\n                        }, this), \" \", count]\n                      }, type, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 25\n                  }, this), defect.type === 'kerb' && defect.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Kerb Conditions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: Object.entries(defect.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [condition, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 721,\n                          columnNumber: 51\n                        }, this), \" \", count]\n                      }, condition, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 721,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 25\n                  }, this), (defect.exif_data || defect.metadata) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"\\uD83D\\uDCCA Media Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small\",\n                      children: [((_defect$exif_data = defect.exif_data) === null || _defect$exif_data === void 0 ? void 0 : _defect$exif_data.gps_coordinates) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2 p-2 bg-light rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          className: \"text-success\",\n                          children: \"\\uD83C\\uDF0D GPS (EXIF):\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 735,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Lat: \", (_defect$exif_data$gps = defect.exif_data.gps_coordinates.latitude) === null || _defect$exif_data$gps === void 0 ? void 0 : _defect$exif_data$gps.toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Lng: \", (_defect$exif_data$gps2 = defect.exif_data.gps_coordinates.longitude) === null || _defect$exif_data$gps2 === void 0 ? void 0 : _defect$exif_data$gps2.toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data2 = defect.exif_data) === null || _defect$exif_data2 === void 0 ? void 0 : _defect$exif_data2.camera_info) && Object.keys(defect.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\uD83D\\uDCF7 Camera:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 744,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled ms-2\",\n                          children: [defect.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Make: \", defect.exif_data.camera_info.camera_make]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 747,\n                            columnNumber: 37\n                          }, this), defect.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Model: \", defect.exif_data.camera_info.camera_model]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 750,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 745,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data3 = defect.exif_data) === null || _defect$exif_data3 === void 0 ? void 0 : _defect$exif_data3.technical_info) && Object.keys(defect.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u2699\\uFE0F Technical:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 759,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled ms-2\",\n                          children: [defect.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"ISO: \", defect.exif_data.technical_info.iso]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 762,\n                            columnNumber: 37\n                          }, this), defect.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Exposure: \", defect.exif_data.technical_info.exposure_time]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 765,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 760,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 758,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data4 = defect.exif_data) === null || _defect$exif_data4 === void 0 ? void 0 : _defect$exif_data4.basic_info) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\uD83D\\uDCD0 Dimensions:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 774,\n                          columnNumber: 33\n                        }, this), \" \", defect.exif_data.basic_info.width, \" \\xD7 \", defect.exif_data.basic_info.height, defect.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [\" (\", defect.exif_data.basic_info.format, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 773,\n                        columnNumber: 31\n                      }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"text-info\",\n                          children: \"\\uD83D\\uDCF9 Video Information\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 784,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled small\",\n                          children: [((_defect$metadata = defect.metadata) === null || _defect$metadata === void 0 ? void 0 : (_defect$metadata$form = _defect$metadata.format_info) === null || _defect$metadata$form === void 0 ? void 0 : _defect$metadata$form.duration) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Duration:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 787,\n                              columnNumber: 41\n                            }, this), \" \", Math.round(defect.metadata.format_info.duration), \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 787,\n                            columnNumber: 37\n                          }, this), ((_defect$metadata2 = defect.metadata) === null || _defect$metadata2 === void 0 ? void 0 : (_defect$metadata2$bas = _defect$metadata2.basic_info) === null || _defect$metadata2$bas === void 0 ? void 0 : _defect$metadata2$bas.width) && ((_defect$metadata3 = defect.metadata) === null || _defect$metadata3 === void 0 ? void 0 : (_defect$metadata3$bas = _defect$metadata3.basic_info) === null || _defect$metadata3$bas === void 0 ? void 0 : _defect$metadata3$bas.height) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Resolution:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 790,\n                              columnNumber: 41\n                            }, this), \" \", defect.metadata.basic_info.width, \"x\", defect.metadata.basic_info.height]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 790,\n                            columnNumber: 37\n                          }, this), ((_defect$metadata4 = defect.metadata) === null || _defect$metadata4 === void 0 ? void 0 : (_defect$metadata4$for = _defect$metadata4.format_info) === null || _defect$metadata4$for === void 0 ? void 0 : _defect$metadata4$for.format_name) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Format:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 793,\n                              columnNumber: 41\n                            }, this), \" \", defect.metadata.format_info.format_name.toUpperCase()]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 793,\n                            columnNumber: 37\n                          }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Video ID:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 796,\n                              columnNumber: 41\n                            }, this), \" \", defect.video_id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 796,\n                            columnNumber: 37\n                          }, this), defect.model_outputs && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Potholes Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 802,\n                                columnNumber: 45\n                              }, this), \" \", defect.model_outputs.potholes.length]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 802,\n                              columnNumber: 41\n                            }, this), defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Cracks Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 805,\n                                columnNumber: 45\n                              }, this), \" \", defect.model_outputs.cracks.length]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 805,\n                              columnNumber: 41\n                            }, this), defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Kerbs Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 808,\n                                columnNumber: 45\n                              }, this), \" \", defect.model_outputs.kerbs.length]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 808,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 785,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: `/view/${defect.image_id}`,\n                      className: \"btn btn-sm btn-primary\",\n                      onClick: e => e.stopPropagation(),\n                      children: \"View Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 821,\n                      columnNumber: 25\n                    }, this), defect.media_type !== 'video' && defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: defect.original_image_full_url,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"btn btn-sm btn-outline-secondary\",\n                      onClick: e => e.stopPropagation(),\n                      children: \"View Original\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this)\n            }, defect.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 449,\n    columnNumber: 5\n  }, this);\n}\n_s2(DefectMap, \"sRYxfvG8F3dXQVEP4Qy79z2muvg=\");\n_c2 = DefectMap;\nexport default DefectMap;\nvar _c, _c2;\n$RefreshReg$(_c, \"EnhancedMapImageDisplay\");\n$RefreshReg$(_c2, \"DefectMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "axios", "L", "Card", "Row", "Col", "Form", "<PERSON><PERSON>", "Spinner", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EnhancedMapImageDisplay", "defect", "_s", "currentImageUrl", "setCurrentImageUrl", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "isLoading", "setIsLoading", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "generateImageUrl", "defectData", "console", "log", "image_id", "original_image_presigned_url", "original_image_full_url", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "length", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "original_image_s3_url", "<PERSON><PERSON><PERSON>", "map", "original_image_id", "warn", "url", "handleImageError", "e", "gridfsId", "gridfsUrl", "error", "handleImageLoad", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "minHeight", "animation", "size", "variant", "src", "alt", "maxHeight", "max<PERSON><PERSON><PERSON>", "onError", "onLoad", "_c", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createCustomIcon", "color", "isVideo", "iconSize", "svgContent", "icon", "iconAnchor", "popupAnchor", "icons", "pothole", "crack", "kerb", "DefectMap", "user", "_s2", "defects", "setDefects", "loading", "setLoading", "setError", "center", "zoom", "startDate", "setStartDate", "endDate", "setEndDate", "selected<PERSON>ser", "setSelectedUser", "usersList", "setUsersList", "defectTypeFilters", "setDefectTypeFilters", "fetchDefectData", "forceRefresh", "_response$data$images", "params", "start_date", "end_date", "username", "role", "user_role", "_t", "Date", "now", "_force", "_refresh", "Math", "random", "toString", "substring", "response", "get", "headers", "success", "data", "totalImages", "total_images", "imageCount", "images", "processedDefects", "for<PERSON>ach", "image", "coordinates", "_image$exif_data", "lat", "lng", "exif_data", "gps_coordinates", "latitude", "longitude", "coords", "parseFloat", "trim", "Array", "isArray", "isNaN", "id", "push", "type", "position", "defect_count", "timestamp", "toLocaleString", "type_counts", "condition_counts", "metadata", "media_type", "coordError", "message", "err", "_err$response", "_err$response$data", "fetchUsers", "users", "currentDate", "thirtyDaysAgo", "setDate", "getDate", "toISOString", "interval", "setInterval", "clearInterval", "handleRefresh", "handleApplyFilters", "handleResetFilters", "handleDefectTypeFilterChange", "prevFilters", "filteredDefects", "filter", "Header", "Body", "lg", "Group", "Label", "Control", "value", "onChange", "target", "Select", "index", "marginTop", "paddingLeft", "width", "min<PERSON><PERSON><PERSON>", "Check", "label", "checked", "onClick", "disabled", "backgroundColor", "height", "scrollWheelZoom", "attribution", "_defect$exif_data", "_defect$exif_data$gps", "_defect$exif_data$gps2", "_defect$exif_data2", "_defect$exif_data3", "_defect$exif_data4", "_defect$metadata", "_defect$metadata$form", "_defect$metadata2", "_defect$metadata2$bas", "_defect$metadata3", "_defect$metadata3$bas", "_defect$metadata4", "_defect$metadata4$for", "<PERSON><PERSON><PERSON>", "selectedIcon", "char<PERSON>t", "toUpperCase", "representative_frame", "objectFit", "display", "fallback", "nextElement<PERSON><PERSON>ling", "classList", "contains", "video_id", "toFixed", "Object", "entries", "count", "condition", "camera_info", "keys", "camera_make", "camera_model", "technical_info", "iso", "exposure_time", "basic_info", "format", "format_info", "duration", "round", "format_name", "model_outputs", "potholes", "cracks", "kerbs", "to", "stopPropagation", "href", "rel", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/DefectMap.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';\r\nimport axios from 'axios';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport { Card, Row, Col, Form, But<PERSON>, Spinner } from 'react-bootstrap';\r\nimport { Link } from 'react-router-dom';\r\n\r\n/**\r\n * Enhanced Image Display Component for Map Popups\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\r\nconst EnhancedMapImageDisplay = ({ defect }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n\r\n  // Generate comprehensive image URL with multiple fallback options\r\n  const generateImageUrl = (defectData) => {\r\n    if (!defectData) return null;\r\n\r\n    console.log('🔍 Generating map image URL for:', defectData.image_id);\r\n\r\n    // Priority 1: Try pre-signed URL (most secure and reliable)\r\n    if (defectData.original_image_presigned_url) {\r\n      console.log('🔗 Using pre-signed URL for map image');\r\n      return defectData.original_image_presigned_url;\r\n    }\r\n\r\n    // Priority 2: Try S3 full URL with proxy\r\n    if (defectData.original_image_full_url) {\r\n      console.log('🔗 Using full URL field');\r\n      // Extract S3 key from full URL and use proxy endpoint\r\n      const urlParts = defectData.original_image_full_url.split('/');\r\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n        const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n        console.log('✅ Generated proxy URL from full URL:', proxyUrl);\r\n        return proxyUrl;\r\n      }\r\n    }\r\n\r\n    // Priority 3: Try S3 key with proxy endpoint\r\n    if (defectData.original_image_s3_url) {\r\n      console.log('🔗 Using S3 key field');\r\n      const s3Key = defectData.original_image_s3_url;\r\n      const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\r\n      console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n\r\n    // Priority 4: Try direct S3 URL (fallback)\r\n    if (defectData.original_image_full_url) {\r\n      console.log('🔗 Using direct S3 URL as fallback');\r\n      return defectData.original_image_full_url;\r\n    }\r\n\r\n    // Priority 5: Try GridFS endpoint\r\n    if (defectData.original_image_id) {\r\n      console.log('🗄️ Using GridFS endpoint');\r\n      return `/api/pavement/get-image/${defectData.original_image_id}`;\r\n    }\r\n\r\n    console.warn('❌ No valid image URL found for defect:', defectData.image_id);\r\n    return null;\r\n  };\r\n\r\n  // Initialize image URL\r\n  useEffect(() => {\r\n    const url = generateImageUrl(defect);\r\n    console.log('🖼️ Setting initial map image URL:', url);\r\n    setCurrentImageUrl(url);\r\n    setHasError(false);\r\n    setIsLoading(true);\r\n    setFallbackAttempts(0);\r\n  }, [defect]);\r\n\r\n  // Handle image load error with fallback attempts\r\n  const handleImageError = (e) => {\r\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\r\n\r\n    if (fallbackAttempts === 0) {\r\n      // First fallback: Try GridFS if we haven't already\r\n      const gridfsId = defect.original_image_id;\r\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\r\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\r\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\r\n        setCurrentImageUrl(gridfsUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks exhausted\r\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\r\n    setHasError(true);\r\n    setIsLoading(false);\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\r\n    setIsLoading(false);\r\n    setHasError(false);\r\n  };\r\n\r\n  // Don't render anything if no URL available\r\n  if (!currentImageUrl) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small\">\r\n          <i className=\"fas fa-image\"></i> Image not available\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '100px' }}>\r\n          <Spinner animation=\"border\" size=\"sm\" variant=\"primary\" />\r\n          <span className=\"ms-2 small\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (hasError) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small p-2 border rounded bg-light\">\r\n          <i className=\"fas fa-exclamation-triangle\"></i> Image unavailable\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render successful image\r\n  return (\r\n    <div className=\"mb-3 text-center\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt=\"Defect image\"\r\n        className=\"img-fluid border rounded\"\r\n        style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n        onError={handleImageError}\r\n        onLoad={handleImageLoad}\r\n      />\r\n      <div className=\"mt-1\">\r\n        <small className=\"text-primary fw-bold\">📷 Original Image</small>\r\n        {fallbackAttempts > 0 && (\r\n          <div>\r\n            <small className=\"text-warning\">(Fallback source)</small>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Fix for the Leaflet default icon issue\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\r\n});\r\n\r\n// Custom marker icons for different defect types using location icons\r\nconst createCustomIcon = (color, isVideo = false) => {\r\n  const iconSize = isVideo ? [36, 36] : [32, 32];\r\n\r\n  // Create different SVG content for video vs image markers\r\n  const svgContent = isVideo ?\r\n    // Video marker with camera icon made from SVG shapes (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\r\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\r\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\r\n    </svg>` :\r\n    // Image marker with standard location pin (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n    </svg>`;\r\n\r\n  return L.icon({\r\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\r\n    iconSize: iconSize,\r\n    iconAnchor: [iconSize[0]/2, iconSize[1]],\r\n    popupAnchor: [0, -iconSize[1]],\r\n  });\r\n};\r\n\r\nconst icons = {\r\n  pothole: createCustomIcon('#FF0000'), // Red for potholes\r\n  crack: createCustomIcon('#FFCC00'),   // Yellow for cracks (alligator cracks)\r\n  kerb: createCustomIcon('#0066FF'),    // Blue for kerb defects\r\n  // Video variants\r\n  'pothole-video': createCustomIcon('#FF0000', true), // Red for pothole videos\r\n  'crack-video': createCustomIcon('#FFCC00', true),   // Yellow for crack videos\r\n  'kerb-video': createCustomIcon('#0066FF', true),    // Blue for kerb videos\r\n};\r\n\r\nfunction DefectMap({ user }) {\r\n  const [defects, setDefects] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [center] = useState([20.5937, 78.9629]); // India center\r\n  const [zoom] = useState(6); // Country-wide zoom for India\r\n  \r\n  // Filters for the map\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\r\n    pothole: true,\r\n    crack: true,\r\n    kerb: true,\r\n  });\r\n\r\n  // Fetch all images with defects and coordinates\r\n  const fetchDefectData = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Prepare query parameters\r\n      let params = {};\r\n      if (startDate) params.start_date = startDate;\r\n      if (endDate) params.end_date = endDate;\r\n      if (selectedUser) params.username = selectedUser;\r\n      if (user?.role) params.user_role = user.role;\r\n\r\n      // Always add cache-busting parameter to ensure latest data\r\n      params._t = Date.now();\r\n\r\n      // Add additional cache-busting for force refresh\r\n      if (forceRefresh) {\r\n        params._force = 'true';\r\n        params._refresh = Math.random().toString(36).substring(7);\r\n      }\r\n\r\n      console.log('🔄 Fetching defect data with params:', params);\r\n      const response = await axios.get('/api/dashboard/image-stats', {\r\n        params,\r\n        // Disable axios caching\r\n        headers: {\r\n          'Cache-Control': 'no-cache',\r\n          'Pragma': 'no-cache'\r\n        }\r\n      });\r\n      console.log('📊 API response received:', {\r\n        success: response.data.success,\r\n        totalImages: response.data.total_images,\r\n        imageCount: response.data.images?.length\r\n      });\r\n\r\n      if (response.data.success) {\r\n        // Process the data to extract defect locations with type information\r\n        const processedDefects = [];\r\n        \r\n        response.data.images.forEach(image => {\r\n          try {\r\n            // Only process images with valid coordinates\r\n            if (image.coordinates && image.coordinates !== 'Not Available') {\r\n              let lat, lng;\r\n\r\n              // First, try to use EXIF GPS coordinates if available (most accurate)\r\n              if (image.exif_data?.gps_coordinates) {\r\n                lat = image.exif_data.gps_coordinates.latitude;\r\n                lng = image.exif_data.gps_coordinates.longitude;\r\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.image_id}: [${lat}, ${lng}]`);\r\n              } else {\r\n                // Fallback to stored coordinates\r\n                // Handle different coordinate formats\r\n                if (typeof image.coordinates === 'string') {\r\n                  // Parse coordinates (expected format: \"lat,lng\")\r\n                  const coords = image.coordinates.split(',');\r\n                  if (coords.length === 2) {\r\n                    lat = parseFloat(coords[0].trim());\r\n                    lng = parseFloat(coords[1].trim());\r\n                  }\r\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\r\n                  // Handle array format [lat, lng]\r\n                  lat = parseFloat(image.coordinates[0]);\r\n                  lng = parseFloat(image.coordinates[1]);\r\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\r\n                  // Handle object format {lat: x, lng: y}\r\n                  lat = parseFloat(image.coordinates.lat);\r\n                  lng = parseFloat(image.coordinates.lng);\r\n                }\r\n                console.log(`📍 Using stored coordinates for ${image.image_id}: [${lat}, ${lng}]`);\r\n              }\r\n\r\n            // Validate coordinates are within reasonable bounds\r\n            if (!isNaN(lat) && !isNaN(lng) &&\r\n                lat >= -90 && lat <= 90 &&\r\n                lng >= -180 && lng <= 180) {\r\n              console.log(`✅ Valid coordinates for image ${image.id}: [${lat}, ${lng}]`);\r\n              processedDefects.push({\r\n                id: image.id,\r\n                image_id: image.image_id,\r\n                type: image.type,\r\n                position: [lat, lng],\r\n                defect_count: image.defect_count,\r\n                timestamp: new Date(image.timestamp).toLocaleString(),\r\n                username: image.username,\r\n                original_image_id: image.original_image_id,\r\n                // For cracks, include type information if available\r\n                type_counts: image.type_counts,\r\n                // For kerbs, include condition information if available\r\n                condition_counts: image.condition_counts,\r\n                // EXIF and metadata information\r\n                exif_data: image.exif_data || {},\r\n                metadata: image.metadata || {},\r\n                media_type: image.media_type || 'image',\r\n                original_image_full_url: image.original_image_full_url\r\n              });\r\n            } else {\r\n              console.warn(`❌ Invalid coordinates for image ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Skipping image ${image.id}: coordinates=${image.coordinates}`);\r\n          }\r\n          } catch (coordError) {\r\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\r\n          }\r\n        });\r\n\r\n        console.log('Processed defects:', processedDefects.length);\r\n        setDefects(processedDefects);\r\n\r\n        // If no defects were processed, show a helpful message\r\n        if (processedDefects.length === 0) {\r\n          setError('No defects found with valid coordinates for the selected date range');\r\n        }\r\n      } else {\r\n        console.error('API returned success: false', response.data);\r\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching defect data:', err);\r\n      setError('Failed to load defect data: ' + (err.response?.data?.message || err.message));\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Fetch the list of users for the filter dropdown\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const params = {};\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      const response = await axios.get('/api/users/all', { params });\r\n      if (response.data.success) {\r\n        setUsersList(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  };\r\n\r\n  // Initialize default dates for the last 30 days\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n\r\n    console.log('DefectMap component initialized');\r\n  }, []);\r\n\r\n  // Fetch data when component mounts and when filters change\r\n  useEffect(() => {\r\n    fetchDefectData();\r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\r\n      fetchDefectData(true); // Force refresh to get latest EXIF data\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => {\r\n      console.log('🛑 Clearing auto-refresh interval');\r\n      clearInterval(interval);\r\n    };\r\n  }, [startDate, endDate, selectedUser, user?.role]);\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    fetchDefectData(true);\r\n  };\r\n\r\n  // Handle filter application\r\n  const handleApplyFilters = () => {\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle resetting filters\r\n  const handleResetFilters = () => {\r\n    // Reset date filters to last 30 days\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n    \r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n    setSelectedUser('');\r\n    setDefectTypeFilters({\r\n      pothole: true,\r\n      crack: true,\r\n      kerb: true,\r\n    });\r\n    \r\n    // Refetch data with reset filters\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle defect type filter changes\r\n  const handleDefectTypeFilterChange = (type) => {\r\n    setDefectTypeFilters(prevFilters => ({\r\n      ...prevFilters,\r\n      [type]: !prevFilters[type]\r\n    }));\r\n  };\r\n\r\n  // Filter defects based on applied filters\r\n  const filteredDefects = defects.filter(defect => \r\n    defectTypeFilters[defect.type]\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-sm dashboard-card mb-4\">\r\n      <Card.Header className=\"bg-primary text-white\">\r\n        <h5 className=\"mb-0\">Defect Map View</h5>\r\n      </Card.Header>\r\n      <Card.Body>\r\n        <Row className=\"mb-3\">\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Date Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Start Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={startDate}\r\n                      onChange={(e) => setStartDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">End Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={endDate}\r\n                      onChange={(e) => setEndDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>User Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Select User</Form.Label>\r\n                    <Form.Select\r\n                      value={selectedUser}\r\n                      onChange={(e) => setSelectedUser(e.target.value)}\r\n                      size=\"sm\"\r\n                    >\r\n                      <option value=\"\">All Users</option>\r\n                      {usersList.map((user, index) => (\r\n                        <option key={index} value={user.username}>\r\n                          {user.username} ({user.role})\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Defect Type Filter</h6>\r\n            <div className=\"filter-controls defect-type-filters\">\r\n              <div className=\"defect-type-filter-options\" style={{ marginTop: \"0\", paddingLeft: \"0\", width: \"100%\", minWidth: \"150px\" }}>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"pothole-filter\"\r\n                  label=\"Potholes\"\r\n                  checked={defectTypeFilters.pothole}\r\n                  onChange={() => handleDefectTypeFilterChange('pothole')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"crack-filter\"\r\n                  label=\"Cracks\"\r\n                  checked={defectTypeFilters.crack}\r\n                  onChange={() => handleDefectTypeFilterChange('crack')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"kerb-filter\"\r\n                  label=\"Kerbs\"\r\n                  checked={defectTypeFilters.kerb}\r\n                  onChange={() => handleDefectTypeFilterChange('kerb')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        <Row className=\"mb-3\">\r\n          <Col className=\"text-center\">\r\n            <div className=\"filter-actions-container\">\r\n              <Button \r\n                variant=\"primary\" \r\n                size=\"sm\" \r\n                onClick={handleApplyFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Apply Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={handleResetFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Reset Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading}\r\n                className=\"filter-btn\"\r\n              >\r\n                {loading ? '🔄 Refreshing...' : '🔄 Refresh Map'}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Row>\r\n          <Col>\r\n            <div className=\"map-legend mb-3\">\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📷 Images</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FF0000' }}></div>\r\n                  <span>Potholes</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FFCC00' }}></div>\r\n                  <span>Cracks</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#0066FF' }}></div>\r\n                  <span>Kerb Defects</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📹 Videos</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FF0000' }}>📹</div>\r\n                  <span>Pothole Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FFCC00' }}>📹</div>\r\n                  <span>Crack Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#0066FF' }}>📹</div>\r\n                  <span>Kerb Videos</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center p-5\">\r\n            <div className=\"spinner-border text-primary\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"alert alert-danger\">{error}</div>\r\n        ) : (\r\n          <div className=\"map-container\" style={{ height: '500px', width: '100%' }}>\r\n            <MapContainer \r\n              center={center} \r\n              zoom={zoom} \r\n              style={{ height: '100%', width: '100%' }}\r\n              scrollWheelZoom={true}\r\n            >\r\n              <TileLayer\r\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              />\r\n              \r\n              {filteredDefects.map((defect) => {\r\n                // Determine icon based on media type and defect type\r\n                const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\r\n                const selectedIcon = icons[iconKey] || icons[defect.type];\r\n\r\n                return (\r\n                <Marker\r\n                  key={defect.id}\r\n                  position={defect.position}\r\n                  icon={selectedIcon}\r\n                >\r\n                  <Popup maxWidth={400}>\r\n                    <div className=\"defect-popup\">\r\n                      <h6>{defect.type.charAt(0).toUpperCase() + defect.type.slice(1)} Defect</h6>\r\n\r\n                      {/* Video Thumbnail */}\r\n                      {defect.media_type === 'video' && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          {defect.representative_frame ? (\r\n                            <>\r\n                              <img\r\n                                src={`data:image/jpeg;base64,${defect.representative_frame}`}\r\n                                alt=\"Video thumbnail\"\r\n                                className=\"img-fluid border rounded shadow-sm\"\r\n                                style={{ maxHeight: '150px', maxWidth: '100%', objectFit: 'cover' }}\r\n                                onError={(e) => {\r\n                                  console.warn(`Failed to load representative frame for video ${defect.image_id}`);\r\n                                  e.target.style.display = 'none';\r\n                                  // Show fallback message\r\n                                  const fallback = e.target.nextElementSibling;\r\n                                  if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\r\n                                    fallback.style.display = 'block';\r\n                                  }\r\n                                }}\r\n                              />\r\n                              <div className=\"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\" style={{ display: 'none' }}>\r\n                                <i className=\"fas fa-video\"></i> Video thumbnail unavailable\r\n                              </div>\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"text-muted small p-3 border rounded bg-light\">\r\n                              <i className=\"fas fa-video fa-2x mb-2\"></i>\r\n                              <div>Video thumbnail not available</div>\r\n                            </div>\r\n                          )}\r\n                          <div className=\"mt-2\">\r\n                            <small className=\"text-info fw-bold\">📹 Video Thumbnail</small>\r\n                            {defect.video_id && (\r\n                              <div>\r\n                                <small className=\"text-muted d-block\">Video ID: {defect.video_id}</small>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Enhanced Image Display with Fallbacks */}\r\n                      {defect.media_type !== 'video' && (\r\n                        <EnhancedMapImageDisplay defect={defect} />\r\n                      )}\r\n\r\n                      {/* Basic Information */}\r\n                      <div className=\"mb-3\">\r\n                        <h6 className=\"text-primary\">Basic Information</h6>\r\n                        <ul className=\"list-unstyled small\">\r\n                          <li><strong>Count:</strong> {defect.defect_count}</li>\r\n                          <li><strong>Date:</strong> {defect.timestamp}</li>\r\n                          <li><strong>Reported by:</strong> {defect.username}</li>\r\n                          <li><strong>Media Type:</strong> {defect.media_type}</li>\r\n                          <li><strong>GPS:</strong> {defect.position[0].toFixed(6)}, {defect.position[1].toFixed(6)}</li>\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Defect-specific Information */}\r\n                      {defect.type === 'crack' && defect.type_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Crack Types</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.type_counts).map(([type, count]) => (\r\n                              <li key={type}><strong>{type}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {defect.type === 'kerb' && defect.condition_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Kerb Conditions</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.condition_counts).map(([condition, count]) => (\r\n                              <li key={condition}><strong>{condition}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* EXIF/Metadata Information */}\r\n                      {(defect.exif_data || defect.metadata) && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">📊 Media Information</h6>\r\n                          <div className=\"small\">\r\n                            {/* GPS Information - Show first as it's most important for mapping */}\r\n                            {defect.exif_data?.gps_coordinates && (\r\n                              <div className=\"mb-2 p-2 bg-light rounded\">\r\n                                <strong className=\"text-success\">🌍 GPS (EXIF):</strong>\r\n                                <div>Lat: {defect.exif_data.gps_coordinates.latitude?.toFixed(6)}</div>\r\n                                <div>Lng: {defect.exif_data.gps_coordinates.longitude?.toFixed(6)}</div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Camera Information */}\r\n                            {defect.exif_data?.camera_info && Object.keys(defect.exif_data.camera_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📷 Camera:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.camera_info.camera_make && (\r\n                                    <li>Make: {defect.exif_data.camera_info.camera_make}</li>\r\n                                  )}\r\n                                  {defect.exif_data.camera_info.camera_model && (\r\n                                    <li>Model: {defect.exif_data.camera_info.camera_model}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Technical Information */}\r\n                            {defect.exif_data?.technical_info && Object.keys(defect.exif_data.technical_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>⚙️ Technical:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.technical_info.iso && (\r\n                                    <li>ISO: {defect.exif_data.technical_info.iso}</li>\r\n                                  )}\r\n                                  {defect.exif_data.technical_info.exposure_time && (\r\n                                    <li>Exposure: {defect.exif_data.technical_info.exposure_time}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Basic Media Info */}\r\n                            {defect.exif_data?.basic_info && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📐 Dimensions:</strong> {defect.exif_data.basic_info.width} × {defect.exif_data.basic_info.height}\r\n                                {defect.exif_data.basic_info.format && (\r\n                                  <span> ({defect.exif_data.basic_info.format})</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Video-specific metadata */}\r\n                            {defect.media_type === 'video' && (\r\n                              <div className=\"mb-3\">\r\n                                <h6 className=\"text-info\">📹 Video Information</h6>\r\n                                <ul className=\"list-unstyled small\">\r\n                                  {defect.metadata?.format_info?.duration && (\r\n                                    <li><strong>Duration:</strong> {Math.round(defect.metadata.format_info.duration)}s</li>\r\n                                  )}\r\n                                  {defect.metadata?.basic_info?.width && defect.metadata?.basic_info?.height && (\r\n                                    <li><strong>Resolution:</strong> {defect.metadata.basic_info.width}x{defect.metadata.basic_info.height}</li>\r\n                                  )}\r\n                                  {defect.metadata?.format_info?.format_name && (\r\n                                    <li><strong>Format:</strong> {defect.metadata.format_info.format_name.toUpperCase()}</li>\r\n                                  )}\r\n                                  {defect.video_id && (\r\n                                    <li><strong>Video ID:</strong> {defect.video_id}</li>\r\n                                  )}\r\n                                  {/* Show detection counts for videos */}\r\n                                  {defect.model_outputs && (\r\n                                    <>\r\n                                      {defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && (\r\n                                        <li><strong>Potholes Detected:</strong> {defect.model_outputs.potholes.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && (\r\n                                        <li><strong>Cracks Detected:</strong> {defect.model_outputs.cracks.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && (\r\n                                        <li><strong>Kerbs Detected:</strong> {defect.model_outputs.kerbs.length}</li>\r\n                                      )}\r\n                                    </>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Action Buttons */}\r\n                      <div className=\"d-flex gap-2\">\r\n                        <Link\r\n                          to={`/view/${defect.image_id}`}\r\n                          className=\"btn btn-sm btn-primary\"\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                        {/* Only show 'View Original' button for non-video entries */}\r\n                        {defect.media_type !== 'video' && defect.original_image_full_url && (\r\n                          <a\r\n                            href={defect.original_image_full_url}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"btn btn-sm btn-outline-secondary\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            View Original\r\n                          </a>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Popup>\r\n                </Marker>\r\n                );\r\n              })}\r\n            </MapContainer>\r\n          </div>\r\n        )}\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DefectMap; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACtE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACvE,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIA,MAAMC,uBAAuB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAM6B,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAE5BC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEF,UAAU,CAACG,QAAQ,CAAC;;IAEpE;IACA,IAAIH,UAAU,CAACI,4BAA4B,EAAE;MAC3CH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,OAAOF,UAAU,CAACI,4BAA4B;IAChD;;IAEA;IACA,IAAIJ,UAAU,CAACK,uBAAuB,EAAE;MACtCJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC;MACA,MAAMI,QAAQ,GAAGN,UAAU,CAACK,uBAAuB,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9D,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;MACrE,IAAIH,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAE;QAC3D,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,KAAK,CAACN,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;QACvD,MAAMC,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;QAC1EZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEc,QAAQ,CAAC;QAC7D,OAAOA,QAAQ;MACjB;IACF;;IAEA;IACA,IAAIhB,UAAU,CAACkB,qBAAqB,EAAE;MACpCjB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,MAAMW,KAAK,GAAGb,UAAU,CAACkB,qBAAqB;MAC9C,MAAMC,UAAU,GAAGN,KAAK,CAACN,KAAK,CAAC,GAAG,CAAC,CAACa,GAAG,CAACV,IAAI,IAAIO,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;MACnF,MAAMC,QAAQ,GAAG,8BAA8BG,UAAU,EAAE;MAC3DlB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEc,QAAQ,CAAC;MAC3D,OAAOA,QAAQ;IACjB;;IAEA;IACA,IAAIhB,UAAU,CAACK,uBAAuB,EAAE;MACtCJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAOF,UAAU,CAACK,uBAAuB;IAC3C;;IAEA;IACA,IAAIL,UAAU,CAACqB,iBAAiB,EAAE;MAChCpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,OAAO,2BAA2BF,UAAU,CAACqB,iBAAiB,EAAE;IAClE;IAEApB,OAAO,CAACqB,IAAI,CAAC,wCAAwC,EAAEtB,UAAU,CAACG,QAAQ,CAAC;IAC3E,OAAO,IAAI;EACb,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,MAAMsD,GAAG,GAAGxB,gBAAgB,CAACV,MAAM,CAAC;IACpCY,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqB,GAAG,CAAC;IACtD/B,kBAAkB,CAAC+B,GAAG,CAAC;IACvB7B,WAAW,CAAC,KAAK,CAAC;IAClBE,YAAY,CAAC,IAAI,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMmC,gBAAgB,GAAIC,CAAC,IAAK;IAC9BxB,OAAO,CAACqB,IAAI,CAAC,oCAAoCzB,gBAAgB,GAAG,CAAC,IAAI,EAAEN,eAAe,CAAC;IAE3F,IAAIM,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAM6B,QAAQ,GAAGrC,MAAM,CAACgC,iBAAiB;MACzC,IAAIK,QAAQ,IAAI,CAACnC,eAAe,CAACoB,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtD,MAAMgB,SAAS,GAAG,2BAA2BD,QAAQ,EAAE;QACvDzB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyB,SAAS,CAAC;QACpDnC,kBAAkB,CAACmC,SAAS,CAAC;QAC7B7B,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA;IACAG,OAAO,CAAC2B,KAAK,CAAC,0CAA0C,EAAEvC,MAAM,CAACc,QAAQ,CAAC;IAC1ET,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5B5B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEX,eAAe,CAAC;IAChEK,YAAY,CAAC,KAAK,CAAC;IACnBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAI,CAACH,eAAe,EAAE;IACpB,oBACEN,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9C,OAAA;QAAK6C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9C,OAAA;UAAG6C,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,wBAClC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIxC,SAAS,EAAE;IACb,oBACEV,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9C,OAAA;QAAK6C,SAAS,EAAC,kDAAkD;QAACM,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAN,QAAA,gBAC9F9C,OAAA,CAACH,OAAO;UAACwD,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,IAAI;UAACC,OAAO,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DlD,OAAA;UAAM6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI1C,QAAQ,EAAE;IACZ,oBACER,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9C,OAAA;QAAK6C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3D9C,OAAA;UAAG6C,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sBACjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACElD,OAAA;IAAK6C,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B9C,OAAA;MACEwD,GAAG,EAAElD,eAAgB;MACrBmD,GAAG,EAAC,cAAc;MAClBZ,SAAS,EAAC,0BAA0B;MACpCM,KAAK,EAAE;QAAEO,SAAS,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAChDC,OAAO,EAAErB,gBAAiB;MAC1BsB,MAAM,EAAEjB;IAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFlD,OAAA;MAAK6C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9C,OAAA;QAAO6C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEtC,gBAAgB,GAAG,CAAC,iBACnBZ,OAAA;QAAA8C,QAAA,eACE9C,OAAA;UAAO6C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA7C,EAAA,CAzJMF,uBAAuB;AAAA2D,EAAA,GAAvB3D,uBAAuB;AA0J7B,OAAOZ,CAAC,CAACwE,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3C3E,CAAC,CAACwE,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,KAAK;EACnD,MAAMC,QAAQ,GAAGD,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAME,UAAU,GAAGF,OAAO;EACxB;EACA,qEAAqED,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA,8CAA8CF,KAAK;AACnD,sDAAsDA,KAAK;AAC3D,WAAW;EACP;EACA,qEAAqEA,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA,WAAW;EAET,OAAOnF,CAAC,CAACqF,IAAI,CAAC;IACZP,OAAO,EAAE,oCAAoCrC,kBAAkB,CAAC2C,UAAU,CAAC,EAAE;IAC7ED,QAAQ,EAAEA,QAAQ;IAClBG,UAAU,EAAE,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxCI,WAAW,EAAE,CAAC,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,KAAK,GAAG;EACZC,OAAO,EAAET,gBAAgB,CAAC,SAAS,CAAC;EAAE;EACtCU,KAAK,EAAEV,gBAAgB,CAAC,SAAS,CAAC;EAAI;EACtCW,IAAI,EAAEX,gBAAgB,CAAC,SAAS,CAAC;EAAK;EACtC;EACA,eAAe,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAE;EACpD,aAAa,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAI;EACpD,YAAY,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAK;AACtD,CAAC;AAED,SAASY,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,GAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuG,OAAO,EAAEC,UAAU,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0D,KAAK,EAAE+C,QAAQ,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0G,MAAM,CAAC,GAAG1G,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2G,IAAI,CAAC,GAAG3G,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B;EACA,MAAM,CAAC4G,SAAS,EAAEC,YAAY,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8G,OAAO,EAAEC,UAAU,CAAC,GAAG/G,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgH,YAAY,EAAEC,eAAe,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkH,SAAS,EAAEC,YAAY,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrH,QAAQ,CAAC;IACzD+F,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMqB,eAAe,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACtD,IAAI;MAAA,IAAAC,qBAAA;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIgB,MAAM,GAAG,CAAC,CAAC;MACf,IAAIb,SAAS,EAAEa,MAAM,CAACC,UAAU,GAAGd,SAAS;MAC5C,IAAIE,OAAO,EAAEW,MAAM,CAACE,QAAQ,GAAGb,OAAO;MACtC,IAAIE,YAAY,EAAES,MAAM,CAACG,QAAQ,GAAGZ,YAAY;MAChD,IAAIb,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;;MAE5C;MACAJ,MAAM,CAACM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIV,YAAY,EAAE;QAChBE,MAAM,CAACS,MAAM,GAAG,MAAM;QACtBT,MAAM,CAACU,QAAQ,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3D;MAEAxG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEyF,MAAM,CAAC;MAC3D,MAAMe,QAAQ,GAAG,MAAMnI,KAAK,CAACoI,GAAG,CAAC,4BAA4B,EAAE;QAC7DhB,MAAM;QACN;QACAiB,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACF3G,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC2G,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;QAC9BE,WAAW,EAAEL,QAAQ,CAACI,IAAI,CAACE,YAAY;QACvCC,UAAU,GAAAvB,qBAAA,GAAEgB,QAAQ,CAACI,IAAI,CAACI,MAAM,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsB9E;MACpC,CAAC,CAAC;MAEF,IAAI8F,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzB;QACA,MAAMM,gBAAgB,GAAG,EAAE;QAE3BT,QAAQ,CAACI,IAAI,CAACI,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;UACpC,IAAI;YACF;YACA,IAAIA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACC,WAAW,KAAK,eAAe,EAAE;cAAA,IAAAC,gBAAA;cAC9D,IAAIC,GAAG,EAAEC,GAAG;;cAEZ;cACA,KAAAF,gBAAA,GAAIF,KAAK,CAACK,SAAS,cAAAH,gBAAA,eAAfA,gBAAA,CAAiBI,eAAe,EAAE;gBACpCH,GAAG,GAAGH,KAAK,CAACK,SAAS,CAACC,eAAe,CAACC,QAAQ;gBAC9CH,GAAG,GAAGJ,KAAK,CAACK,SAAS,CAACC,eAAe,CAACE,SAAS;gBAC/C5H,OAAO,CAACC,GAAG,CAAC,qCAAqCmH,KAAK,CAAClH,QAAQ,MAAMqH,GAAG,KAAKC,GAAG,GAAG,CAAC;cACtF,CAAC,MAAM;gBACL;gBACA;gBACA,IAAI,OAAOJ,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;kBACzC;kBACA,MAAMQ,MAAM,GAAGT,KAAK,CAACC,WAAW,CAAC/G,KAAK,CAAC,GAAG,CAAC;kBAC3C,IAAIuH,MAAM,CAAClH,MAAM,KAAK,CAAC,EAAE;oBACvB4G,GAAG,GAAGO,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;oBAClCP,GAAG,GAAGM,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;kBACpC;gBACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACb,KAAK,CAACC,WAAW,CAAC,IAAID,KAAK,CAACC,WAAW,CAAC1G,MAAM,KAAK,CAAC,EAAE;kBAC7E;kBACA4G,GAAG,GAAGO,UAAU,CAACV,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;kBACtCG,GAAG,GAAGM,UAAU,CAACV,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,MAAM,IAAI,OAAOD,KAAK,CAACC,WAAW,KAAK,QAAQ,IAAID,KAAK,CAACC,WAAW,CAACE,GAAG,IAAIH,KAAK,CAACC,WAAW,CAACG,GAAG,EAAE;kBAClG;kBACAD,GAAG,GAAGO,UAAU,CAACV,KAAK,CAACC,WAAW,CAACE,GAAG,CAAC;kBACvCC,GAAG,GAAGM,UAAU,CAACV,KAAK,CAACC,WAAW,CAACG,GAAG,CAAC;gBACzC;gBACAxH,OAAO,CAACC,GAAG,CAAC,mCAAmCmH,KAAK,CAAClH,QAAQ,MAAMqH,GAAG,KAAKC,GAAG,GAAG,CAAC;cACpF;;cAEF;cACA,IAAI,CAACU,KAAK,CAACX,GAAG,CAAC,IAAI,CAACW,KAAK,CAACV,GAAG,CAAC,IAC1BD,GAAG,IAAI,CAAC,EAAE,IAAIA,GAAG,IAAI,EAAE,IACvBC,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;gBAC7BxH,OAAO,CAACC,GAAG,CAAC,iCAAiCmH,KAAK,CAACe,EAAE,MAAMZ,GAAG,KAAKC,GAAG,GAAG,CAAC;gBAC1EN,gBAAgB,CAACkB,IAAI,CAAC;kBACpBD,EAAE,EAAEf,KAAK,CAACe,EAAE;kBACZjI,QAAQ,EAAEkH,KAAK,CAAClH,QAAQ;kBACxBmI,IAAI,EAAEjB,KAAK,CAACiB,IAAI;kBAChBC,QAAQ,EAAE,CAACf,GAAG,EAAEC,GAAG,CAAC;kBACpBe,YAAY,EAAEnB,KAAK,CAACmB,YAAY;kBAChCC,SAAS,EAAE,IAAIvC,IAAI,CAACmB,KAAK,CAACoB,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;kBACrD5C,QAAQ,EAAEuB,KAAK,CAACvB,QAAQ;kBACxBzE,iBAAiB,EAAEgG,KAAK,CAAChG,iBAAiB;kBAC1C;kBACAsH,WAAW,EAAEtB,KAAK,CAACsB,WAAW;kBAC9B;kBACAC,gBAAgB,EAAEvB,KAAK,CAACuB,gBAAgB;kBACxC;kBACAlB,SAAS,EAAEL,KAAK,CAACK,SAAS,IAAI,CAAC,CAAC;kBAChCmB,QAAQ,EAAExB,KAAK,CAACwB,QAAQ,IAAI,CAAC,CAAC;kBAC9BC,UAAU,EAAEzB,KAAK,CAACyB,UAAU,IAAI,OAAO;kBACvCzI,uBAAuB,EAAEgH,KAAK,CAAChH;gBACjC,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLJ,OAAO,CAACqB,IAAI,CAAC,mCAAmC+F,KAAK,CAACe,EAAE,GAAG,EAAEf,KAAK,CAACC,WAAW,EAAE,eAAeE,GAAG,SAASC,GAAG,EAAE,CAAC;cACnH;YACF,CAAC,MAAM;cACLxH,OAAO,CAACC,GAAG,CAAC,qBAAqBmH,KAAK,CAACe,EAAE,iBAAiBf,KAAK,CAACC,WAAW,EAAE,CAAC;YAChF;UACA,CAAC,CAAC,OAAOyB,UAAU,EAAE;YACnB9I,OAAO,CAAC2B,KAAK,CAAC,4CAA4CyF,KAAK,CAACe,EAAE,GAAG,EAAEW,UAAU,EAAE1B,KAAK,CAACC,WAAW,CAAC;UACvG;QACF,CAAC,CAAC;QAEFrH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiH,gBAAgB,CAACvG,MAAM,CAAC;QAC1D4D,UAAU,CAAC2C,gBAAgB,CAAC;;QAE5B;QACA,IAAIA,gBAAgB,CAACvG,MAAM,KAAK,CAAC,EAAE;UACjC+D,QAAQ,CAAC,qEAAqE,CAAC;QACjF;MACF,CAAC,MAAM;QACL1E,OAAO,CAAC2B,KAAK,CAAC,6BAA6B,EAAE8E,QAAQ,CAACI,IAAI,CAAC;QAC3DnC,QAAQ,CAAC,8BAA8B,IAAI+B,QAAQ,CAACI,IAAI,CAACkC,OAAO,IAAI,eAAe,CAAC,CAAC;MACvF;MAEAtE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOuE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZlJ,OAAO,CAAC2B,KAAK,CAAC,6BAA6B,EAAEqH,GAAG,CAAC;MACjDtE,QAAQ,CAAC,8BAA8B,IAAI,EAAAuE,aAAA,GAAAD,GAAG,CAACvC,QAAQ,cAAAwC,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcpC,IAAI,cAAAqC,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;MACvFtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0E,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMzD,MAAM,GAAG,CAAC,CAAC;MACjB,IAAItB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;MAE5C,MAAMW,QAAQ,GAAG,MAAMnI,KAAK,CAACoI,GAAG,CAAC,gBAAgB,EAAE;QAAEhB;MAAO,CAAC,CAAC;MAC9D,IAAIe,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzBxB,YAAY,CAACqB,QAAQ,CAACI,IAAI,CAACuC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC,OAAOzH,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA3D,SAAS,CAAC,MAAM;IACd,MAAMqL,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDwE,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvDN,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjC,SAAS,CAAC,MAAM;IACduH,eAAe,CAAC,CAAC;IACjB4D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC/E,IAAI,CAAC,CAAC;;EAEV;EACApG,SAAS,CAAC,MAAM;IACd,MAAM0L,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC3J,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5EsF,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM;MACXvF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD2J,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAAC7E,SAAS,EAAEE,OAAO,EAAEE,YAAY,EAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC,CAAC;;EAElD;EACA,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvE,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMV,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDwE,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD4E,eAAe,CAAC,EAAE,CAAC;IACnBI,oBAAoB,CAAC;MACnBtB,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACAqB,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyE,4BAA4B,GAAI3B,IAAI,IAAK;IAC7C/C,oBAAoB,CAAC2E,WAAW,KAAK;MACnC,GAAGA,WAAW;MACd,CAAC5B,IAAI,GAAG,CAAC4B,WAAW,CAAC5B,IAAI;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM6B,eAAe,GAAG5F,OAAO,CAAC6F,MAAM,CAAC/K,MAAM,IAC3CiG,iBAAiB,CAACjG,MAAM,CAACiJ,IAAI,CAC/B,CAAC;EAED,oBACErJ,OAAA,CAACR,IAAI;IAACqD,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC7C9C,OAAA,CAACR,IAAI,CAAC4L,MAAM;MAACvI,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5C9C,OAAA;QAAI6C,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdlD,OAAA,CAACR,IAAI,CAAC6L,IAAI;MAAAvI,QAAA,gBACR9C,OAAA,CAACP,GAAG;QAACoD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB9C,OAAA,CAACN,GAAG;UAAC4L,EAAE,EAAE,CAAE;UAACzI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpC9C,OAAA;YAAA8C,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBlD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B9C,OAAA;cAAK6C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC9C,OAAA;gBAAK6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B9C,OAAA,CAACL,IAAI,CAAC4L,KAAK;kBAAAzI,QAAA,gBACT9C,OAAA,CAACL,IAAI,CAAC6L,KAAK;oBAAC3I,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrDlD,OAAA,CAACL,IAAI,CAAC8L,OAAO;oBACXpC,IAAI,EAAC,MAAM;oBACXqC,KAAK,EAAE7F,SAAU;oBACjB8F,QAAQ,EAAGnJ,CAAC,IAAKsD,YAAY,CAACtD,CAAC,CAACoJ,MAAM,CAACF,KAAK,CAAE;oBAC9CpI,IAAI,EAAC;kBAAI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B9C,OAAA,CAACL,IAAI,CAAC4L,KAAK;kBAAAzI,QAAA,gBACT9C,OAAA,CAACL,IAAI,CAAC6L,KAAK;oBAAC3I,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnDlD,OAAA,CAACL,IAAI,CAAC8L,OAAO;oBACXpC,IAAI,EAAC,MAAM;oBACXqC,KAAK,EAAE3F,OAAQ;oBACf4F,QAAQ,EAAGnJ,CAAC,IAAKwD,UAAU,CAACxD,CAAC,CAACoJ,MAAM,CAACF,KAAK,CAAE;oBAC5CpI,IAAI,EAAC;kBAAI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA,CAACN,GAAG;UAAC4L,EAAE,EAAE,CAAE;UAACzI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpC9C,OAAA;YAAA8C,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBlD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B9C,OAAA;cAAK6C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrC9C,OAAA;gBAAK6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B9C,OAAA,CAACL,IAAI,CAAC4L,KAAK;kBAAAzI,QAAA,gBACT9C,OAAA,CAACL,IAAI,CAAC6L,KAAK;oBAAC3I,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDlD,OAAA,CAACL,IAAI,CAACkM,MAAM;oBACVH,KAAK,EAAEzF,YAAa;oBACpB0F,QAAQ,EAAGnJ,CAAC,IAAK0D,eAAe,CAAC1D,CAAC,CAACoJ,MAAM,CAACF,KAAK,CAAE;oBACjDpI,IAAI,EAAC,IAAI;oBAAAR,QAAA,gBAET9C,OAAA;sBAAQ0L,KAAK,EAAC,EAAE;sBAAA5I,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAClCiD,SAAS,CAAChE,GAAG,CAAC,CAACiD,IAAI,EAAE0G,KAAK,kBACzB9L,OAAA;sBAAoB0L,KAAK,EAAEtG,IAAI,CAACyB,QAAS;sBAAA/D,QAAA,GACtCsC,IAAI,CAACyB,QAAQ,EAAC,IAAE,EAACzB,IAAI,CAAC0B,IAAI,EAAC,GAC9B;oBAAA,GAFagF,KAAK;sBAAA/I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA,CAACN,GAAG;UAAC4L,EAAE,EAAE,CAAE;UAACzI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpC9C,OAAA;YAAA8C,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BlD,OAAA;YAAK6C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD9C,OAAA;cAAK6C,SAAS,EAAC,4BAA4B;cAACM,KAAK,EAAE;gBAAE4I,SAAS,EAAE,GAAG;gBAAEC,WAAW,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAApJ,QAAA,gBACxH9C,OAAA,CAACL,IAAI,CAACwM,KAAK;gBACT9C,IAAI,EAAC,UAAU;gBACfF,EAAE,EAAC,gBAAgB;gBACnBiD,KAAK,EAAC,UAAU;gBAChBC,OAAO,EAAEhG,iBAAiB,CAACrB,OAAQ;gBACnC2G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,SAAS,CAAE;gBACxDnI,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFlD,OAAA,CAACL,IAAI,CAACwM,KAAK;gBACT9C,IAAI,EAAC,UAAU;gBACfF,EAAE,EAAC,cAAc;gBACjBiD,KAAK,EAAC,QAAQ;gBACdC,OAAO,EAAEhG,iBAAiB,CAACpB,KAAM;gBACjC0G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,OAAO,CAAE;gBACtDnI,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFlD,OAAA,CAACL,IAAI,CAACwM,KAAK;gBACT9C,IAAI,EAAC,UAAU;gBACfF,EAAE,EAAC,aAAa;gBAChBiD,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAEhG,iBAAiB,CAACnB,IAAK;gBAChCyG,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,MAAM,CAAE;gBACrDnI,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA,CAACP,GAAG;QAACoD,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9C,OAAA,CAACN,GAAG;UAACmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B9C,OAAA;YAAK6C,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC9C,OAAA,CAACJ,MAAM;cACL2D,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTgJ,OAAO,EAAExB,kBAAmB;cAC5BjI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA,CAACJ,MAAM;cACL2D,OAAO,EAAC,mBAAmB;cAC3BD,IAAI,EAAC,IAAI;cACTgJ,OAAO,EAAEvB,kBAAmB;cAC5BlI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA,CAACJ,MAAM;cACL2D,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTgJ,OAAO,EAAEzB,aAAc;cACvB0B,QAAQ,EAAE/G,OAAQ;cAClB3C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAErB0C,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA,CAACP,GAAG;QAAAqD,QAAA,eACF9C,OAAA,CAACN,GAAG;UAAAoD,QAAA,eACF9C,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B9C,OAAA;cAAK6C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9C,OAAA;gBAAI6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ClD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAACM,KAAK,EAAE;oBAAEqJ,eAAe,EAAE;kBAAU;gBAAE;kBAAAzJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5ElD,OAAA;kBAAA8C,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAACM,KAAK,EAAE;oBAAEqJ,eAAe,EAAE;kBAAU;gBAAE;kBAAAzJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5ElD,OAAA;kBAAA8C,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAACM,KAAK,EAAE;oBAAEqJ,eAAe,EAAE;kBAAU;gBAAE;kBAAAzJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5ElD,OAAA;kBAAA8C,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAK6C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9C,OAAA;gBAAI6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ClD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9C,OAAA;kBAAK6C,SAAS,EAAC,4BAA4B;kBAACM,KAAK,EAAE;oBAAEqJ,eAAe,EAAE;kBAAU,CAAE;kBAAA1J,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9C,OAAA;kBAAK6C,SAAS,EAAC,4BAA4B;kBAACM,KAAK,EAAE;oBAAEqJ,eAAe,EAAE;kBAAU,CAAE;kBAAA1J,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNlD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9C,OAAA;kBAAK6C,SAAS,EAAC,4BAA4B;kBAACM,KAAK,EAAE;oBAAEqJ,eAAe,EAAE;kBAAU,CAAE;kBAAA1J,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FlD,OAAA;kBAAA8C,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELsC,OAAO,gBACNxF,OAAA;QAAK6C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9C,OAAA;UAAK6C,SAAS,EAAC,6BAA6B;UAACiE,IAAI,EAAC,QAAQ;UAAAhE,QAAA,eACxD9C,OAAA;YAAM6C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJP,KAAK,gBACP3C,OAAA;QAAK6C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEjDlD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAACM,KAAK,EAAE;UAAEsJ,MAAM,EAAE,OAAO;UAAER,KAAK,EAAE;QAAO,CAAE;QAAAnJ,QAAA,eACvE9C,OAAA,CAACd,YAAY;UACXyG,MAAM,EAAEA,MAAO;UACfC,IAAI,EAAEA,IAAK;UACXzC,KAAK,EAAE;YAAEsJ,MAAM,EAAE,MAAM;YAAER,KAAK,EAAE;UAAO,CAAE;UACzCS,eAAe,EAAE,IAAK;UAAA5J,QAAA,gBAEtB9C,OAAA,CAACb,SAAS;YACRwN,WAAW,EAAC,yFAAyF;YACrGrK,GAAG,EAAC;UAAoD;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,EAEDgI,eAAe,CAAC/I,GAAG,CAAE/B,MAAM,IAAK;YAAA,IAAAwM,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;YAC/B;YACA,MAAMC,OAAO,GAAGtN,MAAM,CAACyJ,UAAU,KAAK,OAAO,GAAG,GAAGzJ,MAAM,CAACiJ,IAAI,QAAQ,GAAGjJ,MAAM,CAACiJ,IAAI;YACpF,MAAMsE,YAAY,GAAG5I,KAAK,CAAC2I,OAAO,CAAC,IAAI3I,KAAK,CAAC3E,MAAM,CAACiJ,IAAI,CAAC;YAEzD,oBACArJ,OAAA,CAACZ,MAAM;cAELkK,QAAQ,EAAElJ,MAAM,CAACkJ,QAAS;cAC1B1E,IAAI,EAAE+I,YAAa;cAAA7K,QAAA,eAEnB9C,OAAA,CAACX,KAAK;gBAACsE,QAAQ,EAAE,GAAI;gBAAAb,QAAA,eACnB9C,OAAA;kBAAK6C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B9C,OAAA;oBAAA8C,QAAA,GAAK1C,MAAM,CAACiJ,IAAI,CAACuE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGzN,MAAM,CAACiJ,IAAI,CAACxH,KAAK,CAAC,CAAC,CAAC,EAAC,SAAO;kBAAA;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAG3E9C,MAAM,CAACyJ,UAAU,KAAK,OAAO,iBAC5B7J,OAAA;oBAAK6C,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAC9B1C,MAAM,CAAC0N,oBAAoB,gBAC1B9N,OAAA,CAAAE,SAAA;sBAAA4C,QAAA,gBACE9C,OAAA;wBACEwD,GAAG,EAAE,0BAA0BpD,MAAM,CAAC0N,oBAAoB,EAAG;wBAC7DrK,GAAG,EAAC,iBAAiB;wBACrBZ,SAAS,EAAC,oCAAoC;wBAC9CM,KAAK,EAAE;0BAAEO,SAAS,EAAE,OAAO;0BAAEC,QAAQ,EAAE,MAAM;0BAAEoK,SAAS,EAAE;wBAAQ,CAAE;wBACpEnK,OAAO,EAAGpB,CAAC,IAAK;0BACdxB,OAAO,CAACqB,IAAI,CAAC,iDAAiDjC,MAAM,CAACc,QAAQ,EAAE,CAAC;0BAChFsB,CAAC,CAACoJ,MAAM,CAACzI,KAAK,CAAC6K,OAAO,GAAG,MAAM;0BAC/B;0BACA,MAAMC,QAAQ,GAAGzL,CAAC,CAACoJ,MAAM,CAACsC,kBAAkB;0BAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,CAACC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;4BACvEH,QAAQ,CAAC9K,KAAK,CAAC6K,OAAO,GAAG,OAAO;0BAClC;wBACF;sBAAE;wBAAAjL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlD,OAAA;wBAAK6C,SAAS,EAAC,uEAAuE;wBAACM,KAAK,EAAE;0BAAE6K,OAAO,EAAE;wBAAO,CAAE;wBAAAlL,QAAA,gBAChH9C,OAAA;0BAAG6C,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,gCAClC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,eACN,CAAC,gBAEHlD,OAAA;sBAAK6C,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,gBAC3D9C,OAAA;wBAAG6C,SAAS,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3ClD,OAAA;wBAAA8C,QAAA,EAAK;sBAA6B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CACN,eACDlD,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnB9C,OAAA;wBAAO6C,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EAC9D9C,MAAM,CAACiO,QAAQ,iBACdrO,OAAA;wBAAA8C,QAAA,eACE9C,OAAA;0BAAO6C,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAAC,YAAU,EAAC1C,MAAM,CAACiO,QAAQ;wBAAA;0BAAAtL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA9C,MAAM,CAACyJ,UAAU,KAAK,OAAO,iBAC5B7J,OAAA,CAACG,uBAAuB;oBAACC,MAAM,EAAEA;kBAAO;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC3C,eAGDlD,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB9C,OAAA;sBAAI6C,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnDlD,OAAA;sBAAI6C,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBACjC9C,OAAA;wBAAA8C,QAAA,gBAAI9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACmJ,YAAY;sBAAA;wBAAAxG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtDlD,OAAA;wBAAA8C,QAAA,gBAAI9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACoJ,SAAS;sBAAA;wBAAAzG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClDlD,OAAA;wBAAA8C,QAAA,gBAAI9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACyG,QAAQ;sBAAA;wBAAA9D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxDlD,OAAA;wBAAA8C,QAAA,gBAAI9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAW;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACyJ,UAAU;sBAAA;wBAAA9G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzDlD,OAAA;wBAAA8C,QAAA,gBAAI9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACkJ,QAAQ,CAAC,CAAC,CAAC,CAACgF,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAClO,MAAM,CAACkJ,QAAQ,CAAC,CAAC,CAAC,CAACgF,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAvL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EAGL9C,MAAM,CAACiJ,IAAI,KAAK,OAAO,IAAIjJ,MAAM,CAACsJ,WAAW,iBAC5C1J,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB9C,OAAA;sBAAI6C,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7ClD,OAAA;sBAAI6C,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAChCyL,MAAM,CAACC,OAAO,CAACpO,MAAM,CAACsJ,WAAW,CAAC,CAACvH,GAAG,CAAC,CAAC,CAACkH,IAAI,EAAEoF,KAAK,CAAC,kBACpDzO,OAAA;wBAAA8C,QAAA,gBAAe9C,OAAA;0BAAA8C,QAAA,GAASuG,IAAI,EAAC,GAAC;wBAAA;0BAAAtG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACuL,KAAK;sBAAA,GAArCpF,IAAI;wBAAAtG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAsC,CACpD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,EAEA9C,MAAM,CAACiJ,IAAI,KAAK,MAAM,IAAIjJ,MAAM,CAACuJ,gBAAgB,iBAChD3J,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB9C,OAAA;sBAAI6C,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDlD,OAAA;sBAAI6C,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAChCyL,MAAM,CAACC,OAAO,CAACpO,MAAM,CAACuJ,gBAAgB,CAAC,CAACxH,GAAG,CAAC,CAAC,CAACuM,SAAS,EAAED,KAAK,CAAC,kBAC9DzO,OAAA;wBAAA8C,QAAA,gBAAoB9C,OAAA;0BAAA8C,QAAA,GAAS4L,SAAS,EAAC,GAAC;wBAAA;0BAAA3L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACuL,KAAK;sBAAA,GAA/CC,SAAS;wBAAA3L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAA2C,CAC9D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,EAGA,CAAC9C,MAAM,CAACqI,SAAS,IAAIrI,MAAM,CAACwJ,QAAQ,kBACnC5J,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB9C,OAAA;sBAAI6C,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtDlD,OAAA;sBAAK6C,SAAS,EAAC,OAAO;sBAAAC,QAAA,GAEnB,EAAA8J,iBAAA,GAAAxM,MAAM,CAACqI,SAAS,cAAAmE,iBAAA,uBAAhBA,iBAAA,CAAkBlE,eAAe,kBAChC1I,OAAA;wBAAK6C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBACxC9C,OAAA;0BAAQ6C,SAAS,EAAC,cAAc;0BAAAC,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxDlD,OAAA;0BAAA8C,QAAA,GAAK,OAAK,GAAA+J,qBAAA,GAACzM,MAAM,CAACqI,SAAS,CAACC,eAAe,CAACC,QAAQ,cAAAkE,qBAAA,uBAAzCA,qBAAA,CAA2CyB,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAvL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvElD,OAAA;0BAAA8C,QAAA,GAAK,OAAK,GAAAgK,sBAAA,GAAC1M,MAAM,CAACqI,SAAS,CAACC,eAAe,CAACE,SAAS,cAAAkE,sBAAA,uBAA1CA,sBAAA,CAA4CwB,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAvL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CACN,EAGA,EAAA6J,kBAAA,GAAA3M,MAAM,CAACqI,SAAS,cAAAsE,kBAAA,uBAAhBA,kBAAA,CAAkB4B,WAAW,KAAIJ,MAAM,CAACK,IAAI,CAACxO,MAAM,CAACqI,SAAS,CAACkG,WAAW,CAAC,CAAChN,MAAM,GAAG,CAAC,iBACpF3B,OAAA;wBAAK6C,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC3BlD,OAAA;0BAAI6C,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAC/B1C,MAAM,CAACqI,SAAS,CAACkG,WAAW,CAACE,WAAW,iBACvC7O,OAAA;4BAAA8C,QAAA,GAAI,QAAM,EAAC1C,MAAM,CAACqI,SAAS,CAACkG,WAAW,CAACE,WAAW;0BAAA;4BAAA9L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACzD,EACA9C,MAAM,CAACqI,SAAS,CAACkG,WAAW,CAACG,YAAY,iBACxC9O,OAAA;4BAAA8C,QAAA,GAAI,SAAO,EAAC1C,MAAM,CAACqI,SAAS,CAACkG,WAAW,CAACG,YAAY;0BAAA;4BAAA/L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAC3D;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN,EAGA,EAAA8J,kBAAA,GAAA5M,MAAM,CAACqI,SAAS,cAAAuE,kBAAA,uBAAhBA,kBAAA,CAAkB+B,cAAc,KAAIR,MAAM,CAACK,IAAI,CAACxO,MAAM,CAACqI,SAAS,CAACsG,cAAc,CAAC,CAACpN,MAAM,GAAG,CAAC,iBAC1F3B,OAAA;wBAAK6C,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC9BlD,OAAA;0BAAI6C,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAC/B1C,MAAM,CAACqI,SAAS,CAACsG,cAAc,CAACC,GAAG,iBAClChP,OAAA;4BAAA8C,QAAA,GAAI,OAAK,EAAC1C,MAAM,CAACqI,SAAS,CAACsG,cAAc,CAACC,GAAG;0BAAA;4BAAAjM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACnD,EACA9C,MAAM,CAACqI,SAAS,CAACsG,cAAc,CAACE,aAAa,iBAC5CjP,OAAA;4BAAA8C,QAAA,GAAI,YAAU,EAAC1C,MAAM,CAACqI,SAAS,CAACsG,cAAc,CAACE,aAAa;0BAAA;4BAAAlM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAClE;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN,EAGA,EAAA+J,kBAAA,GAAA7M,MAAM,CAACqI,SAAS,cAAAwE,kBAAA,uBAAhBA,kBAAA,CAAkBiC,UAAU,kBAC3BlP,OAAA;wBAAK6C,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB9C,OAAA;0BAAA8C,QAAA,EAAQ;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACqI,SAAS,CAACyG,UAAU,CAACjD,KAAK,EAAC,QAAG,EAAC7L,MAAM,CAACqI,SAAS,CAACyG,UAAU,CAACzC,MAAM,EACxGrM,MAAM,CAACqI,SAAS,CAACyG,UAAU,CAACC,MAAM,iBACjCnP,OAAA;0BAAA8C,QAAA,GAAM,IAAE,EAAC1C,MAAM,CAACqI,SAAS,CAACyG,UAAU,CAACC,MAAM,EAAC,GAAC;wBAAA;0BAAApM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACpD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CACN,EAGA9C,MAAM,CAACyJ,UAAU,KAAK,OAAO,iBAC5B7J,OAAA;wBAAK6C,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB9C,OAAA;0BAAI6C,SAAS,EAAC,WAAW;0BAAAC,QAAA,EAAC;wBAAoB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnDlD,OAAA;0BAAI6C,SAAS,EAAC,qBAAqB;0BAAAC,QAAA,GAChC,EAAAoK,gBAAA,GAAA9M,MAAM,CAACwJ,QAAQ,cAAAsD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBkC,WAAW,cAAAjC,qBAAA,uBAA5BA,qBAAA,CAA8BkC,QAAQ,kBACrCrP,OAAA;4BAAA8C,QAAA,gBAAI9C,OAAA;8BAAA8C,QAAA,EAAQ;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACmE,IAAI,CAACiI,KAAK,CAAClP,MAAM,CAACwJ,QAAQ,CAACwF,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;0BAAA;4BAAAtM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACvF,EACA,EAAAkK,iBAAA,GAAAhN,MAAM,CAACwJ,QAAQ,cAAAwD,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB8B,UAAU,cAAA7B,qBAAA,uBAA3BA,qBAAA,CAA6BpB,KAAK,OAAAqB,iBAAA,GAAIlN,MAAM,CAACwJ,QAAQ,cAAA0D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB4B,UAAU,cAAA3B,qBAAA,uBAA3BA,qBAAA,CAA6Bd,MAAM,kBACxEzM,OAAA;4BAAA8C,QAAA,gBAAI9C,OAAA;8BAAA8C,QAAA,EAAQ;4BAAW;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACwJ,QAAQ,CAACsF,UAAU,CAACjD,KAAK,EAAC,GAAC,EAAC7L,MAAM,CAACwJ,QAAQ,CAACsF,UAAU,CAACzC,MAAM;0BAAA;4BAAA1J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAC5G,EACA,EAAAsK,iBAAA,GAAApN,MAAM,CAACwJ,QAAQ,cAAA4D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB4B,WAAW,cAAA3B,qBAAA,uBAA5BA,qBAAA,CAA8B8B,WAAW,kBACxCvP,OAAA;4BAAA8C,QAAA,gBAAI9C,OAAA;8BAAA8C,QAAA,EAAQ;4BAAO;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACwJ,QAAQ,CAACwF,WAAW,CAACG,WAAW,CAAC1B,WAAW,CAAC,CAAC;0BAAA;4BAAA9K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACzF,EACA9C,MAAM,CAACiO,QAAQ,iBACdrO,OAAA;4BAAA8C,QAAA,gBAAI9C,OAAA;8BAAA8C,QAAA,EAAQ;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACiO,QAAQ;0BAAA;4BAAAtL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACrD,EAEA9C,MAAM,CAACoP,aAAa,iBACnBxP,OAAA,CAAAE,SAAA;4BAAA4C,QAAA,GACG1C,MAAM,CAACoP,aAAa,CAACC,QAAQ,IAAIrP,MAAM,CAACoP,aAAa,CAACC,QAAQ,CAAC9N,MAAM,GAAG,CAAC,iBACxE3B,OAAA;8BAAA8C,QAAA,gBAAI9C,OAAA;gCAAA8C,QAAA,EAAQ;8BAAkB;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACoP,aAAa,CAACC,QAAQ,CAAC9N,MAAM;4BAAA;8BAAAoB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACnF,EACA9C,MAAM,CAACoP,aAAa,CAACE,MAAM,IAAItP,MAAM,CAACoP,aAAa,CAACE,MAAM,CAAC/N,MAAM,GAAG,CAAC,iBACpE3B,OAAA;8BAAA8C,QAAA,gBAAI9C,OAAA;gCAAA8C,QAAA,EAAQ;8BAAgB;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACoP,aAAa,CAACE,MAAM,CAAC/N,MAAM;4BAAA;8BAAAoB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC/E,EACA9C,MAAM,CAACoP,aAAa,CAACG,KAAK,IAAIvP,MAAM,CAACoP,aAAa,CAACG,KAAK,CAAChO,MAAM,GAAG,CAAC,iBAClE3B,OAAA;8BAAA8C,QAAA,gBAAI9C,OAAA;gCAAA8C,QAAA,EAAQ;8BAAe;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC9C,MAAM,CAACoP,aAAa,CAACG,KAAK,CAAChO,MAAM;4BAAA;8BAAAoB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC7E;0BAAA,eACD,CACH;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDlD,OAAA;oBAAK6C,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B9C,OAAA,CAACF,IAAI;sBACH8P,EAAE,EAAE,SAASxP,MAAM,CAACc,QAAQ,EAAG;sBAC/B2B,SAAS,EAAC,wBAAwB;sBAClCyJ,OAAO,EAAG9J,CAAC,IAAKA,CAAC,CAACqN,eAAe,CAAC,CAAE;sBAAA/M,QAAA,EACrC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAEN9C,MAAM,CAACyJ,UAAU,KAAK,OAAO,IAAIzJ,MAAM,CAACgB,uBAAuB,iBAC9DpB,OAAA;sBACE8P,IAAI,EAAE1P,MAAM,CAACgB,uBAAwB;sBACrCwK,MAAM,EAAC,QAAQ;sBACfmE,GAAG,EAAC,qBAAqB;sBACzBlN,SAAS,EAAC,kCAAkC;sBAC5CyJ,OAAO,EAAG9J,CAAC,IAAKA,CAAC,CAACqN,eAAe,CAAC,CAAE;sBAAA/M,QAAA,EACrC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GA5MH9C,MAAM,CAAC+I,EAAE;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6MR,CAAC;UAEX,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX;AAACmC,GAAA,CAhoBQF,SAAS;AAAA6K,GAAA,GAAT7K,SAAS;AAkoBlB,eAAeA,SAAS;AAAC,IAAArB,EAAA,EAAAkM,GAAA;AAAAC,YAAA,CAAAnM,EAAA;AAAAmM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}