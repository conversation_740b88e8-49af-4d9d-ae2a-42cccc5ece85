{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\DefectMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, useMap } from 'react-leaflet';\nimport axios from 'axios';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\n// Error Boundary for Map Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass MapErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('Map Error Boundary caught an error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Map Loading Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"There was an issue loading the map. Please refresh the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm\",\n          onClick: () => window.location.reload(),\n          children: \"Refresh Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// Helper component to invalidate map size after mount and on tile load\nfunction MapSizeFix({\n  mapRef\n}) {\n  _s();\n  const map = useMap();\n  useEffect(() => {\n    // Store map reference for parent component\n    if (mapRef) {\n      mapRef.current = map;\n    }\n\n    // Invalidate size shortly after mount with better error handling\n    const t1 = setTimeout(() => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map invalidateSize error (t1):', error);\n      }\n    }, 100);\n    const t2 = setTimeout(() => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map invalidateSize error (t2):', error);\n      }\n    }, 500);\n\n    // Also on window resize with error handling\n    const onResize = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map resize error:', error);\n      }\n    };\n    window.addEventListener('resize', onResize);\n\n    // Invalidate after zoom animations end with error handling\n    const onZoomEnd = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map zoom end error:', error);\n      }\n    };\n    const onMoveEnd = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map move end error:', error);\n      }\n    };\n    if (map) {\n      map.on('zoomend', onZoomEnd);\n      map.on('moveend', onMoveEnd);\n    }\n    return () => {\n      clearTimeout(t1);\n      clearTimeout(t2);\n      window.removeEventListener('resize', onResize);\n      if (map) {\n        try {\n          map.off('zoomend', onZoomEnd);\n          map.off('moveend', onMoveEnd);\n        } catch (error) {\n          console.warn('Map cleanup error:', error);\n        }\n      }\n    };\n  }, [map, mapRef]);\n  return null;\n}\n\n/**\r\n * Image URL Resolution Logic - Same as Dashboard\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n */\n_s(MapSizeFix, \"IoceErwr5KVGS9kN4RQ1bOkYMAg=\", false, function () {\n  return [useMap];\n});\n_c = MapSizeFix;\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\n  console.log('🔍 Map getImageUrlForDisplay called:', {\n    imageData,\n    imageType\n  });\n  if (!imageData) {\n    console.log('❌ No imageData provided');\n    return null;\n  }\n\n  // Check if this is video data with representative frame\n  if (imageData.media_type === 'video' && imageData.representative_frame) {\n    console.log('📹 Using representative frame for video data');\n    return `data:image/jpeg;base64,${imageData.representative_frame}`;\n  }\n\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\n  const fullUrlField = `${imageType}_image_full_url`;\n  if (imageData[fullUrlField]) {\n    console.log('🔗 Using full URL field:', fullUrlField, imageData[fullUrlField]);\n    // Extract S3 key from full URL and use proxy endpoint\n    const urlParts = imageData[fullUrlField].split('/');\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n      console.log('✅ Generated proxy URL from full URL:', proxyUrl);\n      return proxyUrl;\n    }\n  }\n\n  // Try S3 key with proxy endpoint (new images)\n  const s3KeyField = `${imageType}_image_s3_url`;\n  if (imageData[s3KeyField]) {\n    console.log('🔗 Using S3 key field:', s3KeyField, imageData[s3KeyField]);\n    const s3Key = imageData[s3KeyField];\n    const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n    console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\n    return proxyUrl;\n  }\n\n  // Try GridFS endpoint (legacy images)\n  const gridfsIdField = `${imageType}_image_id`;\n  if (imageData[gridfsIdField]) {\n    console.log('🗄️ Using GridFS endpoint:', gridfsIdField, imageData[gridfsIdField]);\n    return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\n  }\n  console.warn('❌ No valid image URL found for:', imageType, imageData.image_id);\n  return null;\n};\n\n/**\r\n * Enhanced Image Display Component - Same as Dashboard\r\n * Supports both original and processed images with toggle\r\n */\nconst EnhancedMapImageDisplay = ({\n  defect\n}) => {\n  _s2();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n  const [isOriginal, setIsOriginal] = useState(false); // Start with processed image\n\n  // Initialize image URL\n  useEffect(() => {\n    const url = generateImageUrl(defect);\n    console.log('🖼️ Setting initial map image URL:', url);\n    setCurrentImageUrl(url);\n    setHasError(false);\n    setIsLoading(true);\n    setFallbackAttempts(0);\n  }, [defect]);\n\n  // Handle image load error with fallback attempts\n  const handleImageError = e => {\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\n    if (fallbackAttempts === 0) {\n      // First fallback: Try GridFS if we haven't already\n      const gridfsId = defect.original_image_id;\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\n        setCurrentImageUrl(gridfsUrl);\n        setFallbackAttempts(1);\n        return;\n      }\n    }\n\n    // All fallbacks exhausted\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\n    setHasError(true);\n    setIsLoading(false);\n  };\n  const handleImageLoad = () => {\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\n    setIsLoading(false);\n    setHasError(false);\n  };\n\n  // Don't render anything if no URL available\n  if (!currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), \" Image not available\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          minHeight: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          variant: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2 small\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render error state\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small p-2 border rounded bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), \" Image unavailable\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render successful image\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-3 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: \"Defect image\",\n      className: \"img-fluid border rounded\",\n      style: {\n        maxHeight: '150px',\n        maxWidth: '100%'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-primary fw-bold\",\n        children: \"\\uD83D\\uDCF7 Original Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), fallbackAttempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-warning\",\n          children: \"(Fallback source)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n};\n\n// Fix for the Leaflet default icon issue\n_s2(EnhancedMapImageDisplay, \"8olssMCAXG7H0c82dX7Ck1C5W44=\");\n_c2 = EnhancedMapImageDisplay;\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Custom marker icons for different defect types using location icons\nconst createCustomIcon = (color, isVideo = false) => {\n  const iconSize = isVideo ? [36, 36] : [32, 32];\n\n  // Create different SVG content for video vs image markers\n  const svgContent = isVideo ?\n  // Video marker with camera icon made from SVG shapes (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\n    </svg>` :\n  // Image marker with standard location pin (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>`;\n  return L.icon({\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\n    iconSize: iconSize,\n    iconAnchor: [iconSize[0] / 2, iconSize[1]],\n    popupAnchor: [0, -iconSize[1]]\n  });\n};\nconst icons = {\n  pothole: createCustomIcon('#FF0000'),\n  // Red for potholes\n  crack: createCustomIcon('#FFCC00'),\n  // Yellow for cracks (alligator cracks)\n  kerb: createCustomIcon('#0066FF'),\n  // Blue for kerb defects\n  // Video variants\n  'pothole-video': createCustomIcon('#FF0000', true),\n  // Red for pothole videos\n  'crack-video': createCustomIcon('#FFCC00', true),\n  // Yellow for crack videos\n  'kerb-video': createCustomIcon('#0066FF', true) // Blue for kerb videos\n};\nfunction DefectMap({\n  user\n}) {\n  _s3();\n  const [defects, setDefects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [center] = useState([20.5937, 78.9629]); // India center\n  const [zoom] = useState(6); // Country-wide zoom for India\n  const mapRef = useRef(null);\n\n  // Filters for the map\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [usersList, setUsersList] = useState([]);\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\n    pothole: true,\n    crack: true,\n    kerb: true\n  });\n\n  // Fetch all images with defects and coordinates\n  const fetchDefectData = async (forceRefresh = false) => {\n    try {\n      var _response$data$images;\n      setLoading(true);\n      setError(null); // Clear any previous errors\n\n      // Prepare query parameters\n      let params = {};\n      if (startDate) params.start_date = startDate;\n      if (endDate) params.end_date = endDate;\n      if (selectedUser) params.username = selectedUser;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Always add cache-busting parameter to ensure latest data\n      params._t = Date.now();\n\n      // Add additional cache-busting for force refresh\n      if (forceRefresh) {\n        params._force = 'true';\n        params._refresh = Math.random().toString(36).substring(7);\n      }\n      console.log('🔄 Fetching defect data with params:', params);\n      const response = await axios.get('/api/dashboard/image-stats', {\n        params,\n        // Disable axios caching\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      console.log('📊 API response received:', {\n        success: response.data.success,\n        totalImages: response.data.total_images,\n        imageCount: (_response$data$images = response.data.images) === null || _response$data$images === void 0 ? void 0 : _response$data$images.length\n      });\n      if (response.data.success) {\n        // Process the data to extract defect locations with type information\n        const processedDefects = [];\n        response.data.images.forEach(image => {\n          try {\n            // Only process images with valid coordinates\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\n            if (image.coordinates && image.coordinates !== 'Not Available') {\n              var _image$exif_data;\n              let lat, lng;\n\n              // First, try to use EXIF GPS coordinates if available (most accurate)\n              if ((_image$exif_data = image.exif_data) !== null && _image$exif_data !== void 0 && _image$exif_data.gps_coordinates) {\n                lat = image.exif_data.gps_coordinates.latitude;\n                lng = image.exif_data.gps_coordinates.longitude;\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              } else {\n                // Fallback to stored coordinates\n                // Handle different coordinate formats\n                if (typeof image.coordinates === 'string') {\n                  // Parse coordinates (expected format: \"lat,lng\")\n                  const coords = image.coordinates.split(',');\n                  if (coords.length === 2) {\n                    lat = parseFloat(coords[0].trim());\n                    lng = parseFloat(coords[1].trim());\n                  }\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\n                  // Handle array format [lat, lng]\n                  lat = parseFloat(image.coordinates[0]);\n                  lng = parseFloat(image.coordinates[1]);\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\n                  // Handle object format {lat: x, lng: y}\n                  lat = parseFloat(image.coordinates.lat);\n                  lng = parseFloat(image.coordinates.lng);\n                }\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              }\n\n              // Validate coordinates are within reasonable bounds\n              if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\n                processedDefects.push({\n                  id: image.id,\n                  image_id: image.image_id,\n                  type: image.type,\n                  position: [lat, lng],\n                  defect_count: image.defect_count,\n                  timestamp: new Date(image.timestamp).toLocaleString(),\n                  username: image.username,\n                  original_image_id: image.original_image_id,\n                  // For cracks, include type information if available\n                  type_counts: image.type_counts,\n                  // For kerbs, include condition information if available\n                  condition_counts: image.condition_counts,\n                  // EXIF and metadata information\n                  exif_data: image.exif_data || {},\n                  metadata: image.metadata || {},\n                  media_type: image.media_type || 'image',\n                  original_image_full_url: image.original_image_full_url\n                });\n              } else {\n                console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\n              }\n            } else {\n              console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\n            }\n          } catch (coordError) {\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\n          }\n        });\n        console.log('Processed defects:', processedDefects.length);\n        setDefects(processedDefects);\n\n        // If no defects were processed, show a helpful message\n        if (processedDefects.length === 0) {\n          setError('No defects found with valid coordinates for the selected date range');\n        }\n      } else {\n        console.error('API returned success: false', response.data);\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\n      }\n      setLoading(false);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error fetching defect data:', err);\n      setError('Failed to load defect data: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n      setLoading(false);\n    }\n  };\n\n  // Fetch the list of users for the filter dropdown\n  const fetchUsers = async () => {\n    try {\n      const params = {};\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n      const response = await axios.get('/api/users/all', {\n        params\n      });\n      if (response.data.success) {\n        setUsersList(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  // Initialize default dates for the last 30 days\n  useEffect(() => {\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    console.log('DefectMap component initialized');\n  }, []);\n\n  // Fetch data when component mounts and when filters change\n  useEffect(() => {\n    fetchDefectData();\n    fetchUsers();\n  }, [user]);\n\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\n  useEffect(() => {\n    const interval = setInterval(() => {\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\n      fetchDefectData(true); // Force refresh to get latest EXIF data\n    }, 30000); // 30 seconds\n\n    return () => {\n      console.log('🛑 Clearing auto-refresh interval');\n      clearInterval(interval);\n    };\n  }, [startDate, endDate, selectedUser, user === null || user === void 0 ? void 0 : user.role]);\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    fetchDefectData(true);\n  };\n\n  // Handle filter application\n  const handleApplyFilters = () => {\n    fetchDefectData();\n  };\n\n  // Handle resetting filters\n  const handleResetFilters = () => {\n    // Reset date filters to last 30 days\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    setSelectedUser('');\n    setDefectTypeFilters({\n      pothole: true,\n      crack: true,\n      kerb: true\n    });\n\n    // Refetch data with reset filters\n    fetchDefectData();\n  };\n\n  // Handle defect type filter changes\n  const handleDefectTypeFilterChange = type => {\n    setDefectTypeFilters(prevFilters => ({\n      ...prevFilters,\n      [type]: !prevFilters[type]\n    }));\n  };\n\n  // Filter defects based on applied filters\n  const filteredDefects = defects.filter(defect => defectTypeFilters[defect.type]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"shadow-sm dashboard-card mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-0\",\n        children: \"Defect Map View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Date Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: startDate,\n                    onChange: e => setStartDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: endDate,\n                    onChange: e => setEndDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"User Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Select User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedUser,\n                    onChange: e => setSelectedUser(e.target.value),\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 23\n                    }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: user.username,\n                      children: [user.username, \" (\", user.role, \")\"]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Defect Type Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls defect-type-filters\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"defect-type-filter-options\",\n              style: {\n                marginTop: \"0\",\n                paddingLeft: \"0\",\n                width: \"100%\",\n                minWidth: \"150px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"pothole-filter\",\n                label: \"Potholes\",\n                checked: defectTypeFilters.pothole,\n                onChange: () => handleDefectTypeFilterChange('pothole'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"crack-filter\",\n                label: \"Cracks\",\n                checked: defectTypeFilters.crack,\n                onChange: () => handleDefectTypeFilterChange('crack'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"kerb-filter\",\n                label: \"Kerbs\",\n                checked: defectTypeFilters.kerb,\n                onChange: () => handleDefectTypeFilterChange('kerb'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-actions-container\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              onClick: handleApplyFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Apply Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: handleResetFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              onClick: handleRefresh,\n              disabled: loading,\n              className: \"filter-btn\",\n              children: loading ? '🔄 Refreshing...' : '🔄 Refresh Map'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF7 Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Defects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF9 Videos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pothole Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Crack Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"map-container\",\n        style: {\n          height: '500px',\n          width: '100%',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(MapErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(MapContainer, {\n            center: center,\n            zoom: zoom,\n            style: {\n              height: '100%',\n              width: '100%'\n            },\n            scrollWheelZoom: true,\n            children: [/*#__PURE__*/_jsxDEV(MapSizeFix, {\n              mapRef: mapRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TileLayer, {\n              attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n              url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this), filteredDefects.map(defect => {\n              var _defect$exif_data, _defect$exif_data$gps, _defect$exif_data$gps2, _defect$exif_data2, _defect$exif_data3, _defect$exif_data4, _defect$metadata, _defect$metadata$form, _defect$metadata2, _defect$metadata2$bas, _defect$metadata3, _defect$metadata3$bas, _defect$metadata4, _defect$metadata4$for;\n              // Defensive programming: ensure defect has required properties\n              if (!defect || !defect.position || !Array.isArray(defect.position) || defect.position.length !== 2) {\n                console.warn('Invalid defect data:', defect);\n                return null;\n              }\n\n              // Determine icon based on media type and defect type\n              const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\n              const selectedIcon = icons[iconKey] || icons[defect.type] || icons['pothole']; // fallback icon\n\n              return /*#__PURE__*/_jsxDEV(Marker, {\n                position: defect.position,\n                icon: selectedIcon,\n                children: /*#__PURE__*/_jsxDEV(Popup, {\n                  maxWidth: 400,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"defect-popup\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: [defect.type.charAt(0).toUpperCase() + defect.type.slice(1), \" Defect\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 23\n                    }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 text-center\",\n                      children: [defect.representative_frame ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: `data:image/jpeg;base64,${defect.representative_frame}`,\n                          alt: \"Video thumbnail\",\n                          className: \"img-fluid border rounded shadow-sm\",\n                          style: {\n                            maxHeight: '150px',\n                            maxWidth: '100%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            console.warn(`Failed to load representative frame for video ${defect.image_id}`);\n                            e.target.style.display = 'none';\n                            // Show fallback message\n                            const fallback = e.target.nextElementSibling;\n                            if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\n                              fallback.style.display = 'block';\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\",\n                          style: {\n                            display: 'none'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-video\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 805,\n                            columnNumber: 33\n                          }, this), \" Video thumbnail unavailable\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 804,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted small p-3 border rounded bg-light\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-video fa-2x mb-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 810,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: \"Video thumbnail not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 811,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 809,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-info fw-bold\",\n                          children: \"\\uD83D\\uDCF9 Video Thumbnail\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 815,\n                          columnNumber: 29\n                        }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted d-block\",\n                            children: [\"Video ID: \", defect.video_id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 818,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 817,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 814,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 786,\n                      columnNumber: 25\n                    }, this), defect.media_type !== 'video' && /*#__PURE__*/_jsxDEV(EnhancedMapImageDisplay, {\n                      defect: defect\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 827,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Basic Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 832,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Count:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 834,\n                            columnNumber: 31\n                          }, this), \" \", defect.defect_count]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Date:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 835,\n                            columnNumber: 31\n                          }, this), \" \", defect.timestamp]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 835,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Reported by:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 836,\n                            columnNumber: 31\n                          }, this), \" \", defect.username]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 836,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Media Type:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 837,\n                            columnNumber: 31\n                          }, this), \" \", defect.media_type]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 837,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"GPS:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 838,\n                            columnNumber: 31\n                          }, this), \" \", defect.position[0].toFixed(6), \", \", defect.position[1].toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 838,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 833,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 23\n                    }, this), defect.type === 'crack' && defect.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Crack Types\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 845,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: Object.entries(defect.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [type, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 848,\n                            columnNumber: 46\n                          }, this), \" \", count]\n                        }, type, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 848,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 846,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 844,\n                      columnNumber: 25\n                    }, this), defect.type === 'kerb' && defect.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Kerb Conditions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 856,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: Object.entries(defect.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [condition, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 859,\n                            columnNumber: 51\n                          }, this), \" \", count]\n                        }, condition, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 859,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 857,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 25\n                    }, this), (defect.exif_data || defect.metadata) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"\\uD83D\\uDCCA Media Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 868,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"small\",\n                        children: [((_defect$exif_data = defect.exif_data) === null || _defect$exif_data === void 0 ? void 0 : _defect$exif_data.gps_coordinates) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2 p-2 bg-light rounded\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"text-success\",\n                            children: \"\\uD83C\\uDF0D GPS (EXIF):\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 873,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Lat: \", (_defect$exif_data$gps = defect.exif_data.gps_coordinates.latitude) === null || _defect$exif_data$gps === void 0 ? void 0 : _defect$exif_data$gps.toFixed(6)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 874,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Lng: \", (_defect$exif_data$gps2 = defect.exif_data.gps_coordinates.longitude) === null || _defect$exif_data$gps2 === void 0 ? void 0 : _defect$exif_data$gps2.toFixed(6)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 875,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 872,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data2 = defect.exif_data) === null || _defect$exif_data2 === void 0 ? void 0 : _defect$exif_data2.camera_info) && Object.keys(defect.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\uD83D\\uDCF7 Camera:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 882,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled ms-2\",\n                            children: [defect.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Make: \", defect.exif_data.camera_info.camera_make]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 885,\n                              columnNumber: 37\n                            }, this), defect.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Model: \", defect.exif_data.camera_info.camera_model]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 888,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 883,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 881,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data3 = defect.exif_data) === null || _defect$exif_data3 === void 0 ? void 0 : _defect$exif_data3.technical_info) && Object.keys(defect.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u2699\\uFE0F Technical:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 897,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled ms-2\",\n                            children: [defect.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"ISO: \", defect.exif_data.technical_info.iso]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 900,\n                              columnNumber: 37\n                            }, this), defect.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Exposure: \", defect.exif_data.technical_info.exposure_time]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 903,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 898,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 896,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data4 = defect.exif_data) === null || _defect$exif_data4 === void 0 ? void 0 : _defect$exif_data4.basic_info) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\uD83D\\uDCD0 Dimensions:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 912,\n                            columnNumber: 33\n                          }, this), \" \", defect.exif_data.basic_info.width, \" \\xD7 \", defect.exif_data.basic_info.height, defect.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [\" (\", defect.exif_data.basic_info.format, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 914,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 911,\n                          columnNumber: 31\n                        }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"text-info\",\n                            children: \"\\uD83D\\uDCF9 Video Information\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 922,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled small\",\n                            children: [((_defect$metadata = defect.metadata) === null || _defect$metadata === void 0 ? void 0 : (_defect$metadata$form = _defect$metadata.format_info) === null || _defect$metadata$form === void 0 ? void 0 : _defect$metadata$form.duration) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Duration:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 925,\n                                columnNumber: 41\n                              }, this), \" \", Math.round(defect.metadata.format_info.duration), \"s\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 925,\n                              columnNumber: 37\n                            }, this), ((_defect$metadata2 = defect.metadata) === null || _defect$metadata2 === void 0 ? void 0 : (_defect$metadata2$bas = _defect$metadata2.basic_info) === null || _defect$metadata2$bas === void 0 ? void 0 : _defect$metadata2$bas.width) && ((_defect$metadata3 = defect.metadata) === null || _defect$metadata3 === void 0 ? void 0 : (_defect$metadata3$bas = _defect$metadata3.basic_info) === null || _defect$metadata3$bas === void 0 ? void 0 : _defect$metadata3$bas.height) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Resolution:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 928,\n                                columnNumber: 41\n                              }, this), \" \", defect.metadata.basic_info.width, \"x\", defect.metadata.basic_info.height]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 928,\n                              columnNumber: 37\n                            }, this), ((_defect$metadata4 = defect.metadata) === null || _defect$metadata4 === void 0 ? void 0 : (_defect$metadata4$for = _defect$metadata4.format_info) === null || _defect$metadata4$for === void 0 ? void 0 : _defect$metadata4$for.format_name) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Format:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 931,\n                                columnNumber: 41\n                              }, this), \" \", defect.metadata.format_info.format_name.toUpperCase()]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 931,\n                              columnNumber: 37\n                            }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Video ID:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 934,\n                                columnNumber: 41\n                              }, this), \" \", defect.video_id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 934,\n                              columnNumber: 37\n                            }, this), defect.model_outputs && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Potholes Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 940,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.potholes.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 940,\n                                columnNumber: 41\n                              }, this), defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Cracks Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 943,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.cracks.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 943,\n                                columnNumber: 41\n                              }, this), defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Kerbs Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 946,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.kerbs.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 946,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 923,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 921,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        to: `/view/${defect.image_id}`,\n                        className: \"btn btn-sm btn-primary\",\n                        onClick: e => e.stopPropagation(),\n                        children: \"View Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 959,\n                        columnNumber: 25\n                      }, this), defect.media_type !== 'video' && defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: defect.original_image_full_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"btn btn-sm btn-outline-secondary\",\n                        onClick: e => e.stopPropagation(),\n                        children: \"View Original\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 958,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this)\n              }, defect.id || `defect-${Math.random()}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 17\n              }, this);\n            })]\n          }, `map-${center[0]}-${center[1]}-${zoom}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 577,\n    columnNumber: 5\n  }, this);\n}\n_s3(DefectMap, \"va2KsqkWO8jHrIyKplfdw8m/S68=\");\n_c3 = DefectMap;\nexport default DefectMap;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MapSizeFix\");\n$RefreshReg$(_c2, \"EnhancedMapImageDisplay\");\n$RefreshReg$(_c3, \"DefectMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "useMap", "axios", "L", "Card", "Row", "Col", "Form", "<PERSON><PERSON>", "Spinner", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MapErrorBoundary", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "MapSizeFix", "mapRef", "_s", "map", "current", "t1", "setTimeout", "invalidateSize", "warn", "t2", "onResize", "addEventListener", "onZoomEnd", "onMoveEnd", "on", "clearTimeout", "removeEventListener", "off", "_c", "getImageUrlForDisplay", "imageData", "imageType", "log", "media_type", "representative_frame", "fullUrlField", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "length", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "s3KeyField", "gridfsIdField", "image_id", "EnhancedMapImageDisplay", "defect", "_s2", "currentImageUrl", "setCurrentImageUrl", "setHasError", "isLoading", "setIsLoading", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "isOriginal", "setIsOriginal", "url", "generateImageUrl", "handleImageError", "e", "gridfsId", "original_image_id", "gridfsUrl", "handleImageLoad", "style", "minHeight", "animation", "size", "variant", "src", "alt", "maxHeight", "max<PERSON><PERSON><PERSON>", "onError", "onLoad", "_c2", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createCustomIcon", "color", "isVideo", "iconSize", "svgContent", "icon", "iconAnchor", "popupAnchor", "icons", "pothole", "crack", "kerb", "DefectMap", "user", "_s3", "defects", "setDefects", "loading", "setLoading", "setError", "center", "zoom", "startDate", "setStartDate", "endDate", "setEndDate", "selected<PERSON>ser", "setSelectedUser", "usersList", "setUsersList", "defectTypeFilters", "setDefectTypeFilters", "fetchDefectData", "forceRefresh", "_response$data$images", "params", "start_date", "end_date", "username", "role", "user_role", "_t", "Date", "now", "_force", "_refresh", "Math", "random", "toString", "substring", "response", "get", "headers", "success", "data", "totalImages", "total_images", "imageCount", "images", "processedDefects", "for<PERSON>ach", "image", "coordinates", "type", "_image$exif_data", "lat", "lng", "exif_data", "gps_coordinates", "latitude", "longitude", "coords", "parseFloat", "trim", "Array", "isArray", "isNaN", "id", "push", "position", "defect_count", "timestamp", "toLocaleString", "type_counts", "condition_counts", "metadata", "original_image_full_url", "coordError", "message", "err", "_err$response", "_err$response$data", "fetchUsers", "users", "currentDate", "thirtyDaysAgo", "setDate", "getDate", "toISOString", "interval", "setInterval", "clearInterval", "handleRefresh", "handleApplyFilters", "handleResetFilters", "handleDefectTypeFilterChange", "prevFilters", "filteredDefects", "filter", "Header", "Body", "lg", "Group", "Label", "Control", "value", "onChange", "target", "Select", "index", "marginTop", "paddingLeft", "width", "min<PERSON><PERSON><PERSON>", "Check", "label", "checked", "disabled", "backgroundColor", "height", "scrollWheelZoom", "attribution", "_defect$exif_data", "_defect$exif_data$gps", "_defect$exif_data$gps2", "_defect$exif_data2", "_defect$exif_data3", "_defect$exif_data4", "_defect$metadata", "_defect$metadata$form", "_defect$metadata2", "_defect$metadata2$bas", "_defect$metadata3", "_defect$metadata3$bas", "_defect$metadata4", "_defect$metadata4$for", "<PERSON><PERSON><PERSON>", "selectedIcon", "char<PERSON>t", "toUpperCase", "objectFit", "display", "fallback", "nextElement<PERSON><PERSON>ling", "classList", "contains", "video_id", "toFixed", "Object", "entries", "count", "condition", "camera_info", "keys", "camera_make", "camera_model", "technical_info", "iso", "exposure_time", "basic_info", "format", "format_info", "duration", "round", "format_name", "model_outputs", "potholes", "cracks", "kerbs", "to", "stopPropagation", "href", "rel", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/DefectMap.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';\r\nimport axios from 'axios';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\r\nimport { Link } from 'react-router-dom';\r\n\r\n// Error Boundary for Map Component\r\nclass MapErrorBoundary extends React.Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = { hasError: false, error: null };\r\n  }\r\n\r\n  static getDerivedStateFromError(error) {\r\n    return { hasError: true, error };\r\n  }\r\n\r\n  componentDidCatch(error, errorInfo) {\r\n    console.error('Map Error Boundary caught an error:', error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      return (\r\n        <div className=\"alert alert-danger\">\r\n          <h6>Map Loading Error</h6>\r\n          <p>There was an issue loading the map. Please refresh the page.</p>\r\n          <button\r\n            className=\"btn btn-primary btn-sm\"\r\n            onClick={() => window.location.reload()}\r\n          >\r\n            Refresh Page\r\n          </button>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\n// Helper component to invalidate map size after mount and on tile load\r\nfunction MapSizeFix({ mapRef }) {\r\n  const map = useMap();\r\n\r\n  useEffect(() => {\r\n    // Store map reference for parent component\r\n    if (mapRef) {\r\n      mapRef.current = map;\r\n    }\r\n\r\n    // Invalidate size shortly after mount with better error handling\r\n    const t1 = setTimeout(() => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map invalidateSize error (t1):', error);\r\n      }\r\n    }, 100);\r\n\r\n    const t2 = setTimeout(() => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map invalidateSize error (t2):', error);\r\n      }\r\n    }, 500);\r\n\r\n    // Also on window resize with error handling\r\n    const onResize = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map resize error:', error);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', onResize);\r\n\r\n    // Invalidate after zoom animations end with error handling\r\n    const onZoomEnd = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map zoom end error:', error);\r\n      }\r\n    };\r\n\r\n    const onMoveEnd = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map move end error:', error);\r\n      }\r\n    };\r\n\r\n    if (map) {\r\n      map.on('zoomend', onZoomEnd);\r\n      map.on('moveend', onMoveEnd);\r\n    }\r\n\r\n    return () => {\r\n      clearTimeout(t1);\r\n      clearTimeout(t2);\r\n      window.removeEventListener('resize', onResize);\r\n      if (map) {\r\n        try {\r\n          map.off('zoomend', onZoomEnd);\r\n          map.off('moveend', onMoveEnd);\r\n        } catch (error) {\r\n          console.warn('Map cleanup error:', error);\r\n        }\r\n      }\r\n    };\r\n  }, [map, mapRef]);\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Image URL Resolution Logic - Same as Dashboard\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n */\r\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\r\n  console.log('🔍 Map getImageUrlForDisplay called:', { imageData, imageType });\r\n\r\n  if (!imageData) {\r\n    console.log('❌ No imageData provided');\r\n    return null;\r\n  }\r\n\r\n  // Check if this is video data with representative frame\r\n  if (imageData.media_type === 'video' && imageData.representative_frame) {\r\n    console.log('📹 Using representative frame for video data');\r\n    return `data:image/jpeg;base64,${imageData.representative_frame}`;\r\n  }\r\n\r\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\r\n  const fullUrlField = `${imageType}_image_full_url`;\r\n  if (imageData[fullUrlField]) {\r\n    console.log('🔗 Using full URL field:', fullUrlField, imageData[fullUrlField]);\r\n    // Extract S3 key from full URL and use proxy endpoint\r\n    const urlParts = imageData[fullUrlField].split('/');\r\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('✅ Generated proxy URL from full URL:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n  }\r\n\r\n  // Try S3 key with proxy endpoint (new images)\r\n  const s3KeyField = `${imageType}_image_s3_url`;\r\n  if (imageData[s3KeyField]) {\r\n    console.log('🔗 Using S3 key field:', s3KeyField, imageData[s3KeyField]);\r\n    const s3Key = imageData[s3KeyField];\r\n    const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n    console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\r\n    return proxyUrl;\r\n  }\r\n\r\n  // Try GridFS endpoint (legacy images)\r\n  const gridfsIdField = `${imageType}_image_id`;\r\n  if (imageData[gridfsIdField]) {\r\n    console.log('🗄️ Using GridFS endpoint:', gridfsIdField, imageData[gridfsIdField]);\r\n    return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n  }\r\n\r\n  console.warn('❌ No valid image URL found for:', imageType, imageData.image_id);\r\n  return null;\r\n};\r\n\r\n/**\r\n * Enhanced Image Display Component - Same as Dashboard\r\n * Supports both original and processed images with toggle\r\n */\r\nconst EnhancedMapImageDisplay = ({ defect }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n  const [isOriginal, setIsOriginal] = useState(false); // Start with processed image\r\n\r\n  // Initialize image URL\r\n  useEffect(() => {\r\n    const url = generateImageUrl(defect);\r\n    console.log('🖼️ Setting initial map image URL:', url);\r\n    setCurrentImageUrl(url);\r\n    setHasError(false);\r\n    setIsLoading(true);\r\n    setFallbackAttempts(0);\r\n  }, [defect]);\r\n\r\n  // Handle image load error with fallback attempts\r\n  const handleImageError = (e) => {\r\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\r\n\r\n    if (fallbackAttempts === 0) {\r\n      // First fallback: Try GridFS if we haven't already\r\n      const gridfsId = defect.original_image_id;\r\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\r\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\r\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\r\n        setCurrentImageUrl(gridfsUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks exhausted\r\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\r\n    setHasError(true);\r\n    setIsLoading(false);\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\r\n    setIsLoading(false);\r\n    setHasError(false);\r\n  };\r\n\r\n  // Don't render anything if no URL available\r\n  if (!currentImageUrl) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small\">\r\n          <i className=\"fas fa-image\"></i> Image not available\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '100px' }}>\r\n          <Spinner animation=\"border\" size=\"sm\" variant=\"primary\" />\r\n          <span className=\"ms-2 small\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (hasError) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small p-2 border rounded bg-light\">\r\n          <i className=\"fas fa-exclamation-triangle\"></i> Image unavailable\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render successful image\r\n  return (\r\n    <div className=\"mb-3 text-center\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt=\"Defect image\"\r\n        className=\"img-fluid border rounded\"\r\n        style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n        onError={handleImageError}\r\n        onLoad={handleImageLoad}\r\n      />\r\n      <div className=\"mt-1\">\r\n        <small className=\"text-primary fw-bold\">📷 Original Image</small>\r\n        {fallbackAttempts > 0 && (\r\n          <div>\r\n            <small className=\"text-warning\">(Fallback source)</small>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Fix for the Leaflet default icon issue\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\r\n});\r\n\r\n// Custom marker icons for different defect types using location icons\r\nconst createCustomIcon = (color, isVideo = false) => {\r\n  const iconSize = isVideo ? [36, 36] : [32, 32];\r\n\r\n  // Create different SVG content for video vs image markers\r\n  const svgContent = isVideo ?\r\n    // Video marker with camera icon made from SVG shapes (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\r\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\r\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\r\n    </svg>` :\r\n    // Image marker with standard location pin (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n    </svg>`;\r\n\r\n  return L.icon({\r\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\r\n    iconSize: iconSize,\r\n    iconAnchor: [iconSize[0]/2, iconSize[1]],\r\n    popupAnchor: [0, -iconSize[1]],\r\n  });\r\n};\r\n\r\nconst icons = {\r\n  pothole: createCustomIcon('#FF0000'), // Red for potholes\r\n  crack: createCustomIcon('#FFCC00'),   // Yellow for cracks (alligator cracks)\r\n  kerb: createCustomIcon('#0066FF'),    // Blue for kerb defects\r\n  // Video variants\r\n  'pothole-video': createCustomIcon('#FF0000', true), // Red for pothole videos\r\n  'crack-video': createCustomIcon('#FFCC00', true),   // Yellow for crack videos\r\n  'kerb-video': createCustomIcon('#0066FF', true),    // Blue for kerb videos\r\n};\r\n\r\nfunction DefectMap({ user }) {\r\n  const [defects, setDefects] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [center] = useState([20.5937, 78.9629]); // India center\r\n  const [zoom] = useState(6); // Country-wide zoom for India\r\n  const mapRef = useRef(null);\r\n  \r\n  // Filters for the map\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\r\n    pothole: true,\r\n    crack: true,\r\n    kerb: true,\r\n  });\r\n\r\n  // Fetch all images with defects and coordinates\r\n  const fetchDefectData = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Prepare query parameters\r\n      let params = {};\r\n      if (startDate) params.start_date = startDate;\r\n      if (endDate) params.end_date = endDate;\r\n      if (selectedUser) params.username = selectedUser;\r\n      if (user?.role) params.user_role = user.role;\r\n\r\n      // Always add cache-busting parameter to ensure latest data\r\n      params._t = Date.now();\r\n\r\n      // Add additional cache-busting for force refresh\r\n      if (forceRefresh) {\r\n        params._force = 'true';\r\n        params._refresh = Math.random().toString(36).substring(7);\r\n      }\r\n\r\n      console.log('🔄 Fetching defect data with params:', params);\r\n      const response = await axios.get('/api/dashboard/image-stats', {\r\n        params,\r\n        // Disable axios caching\r\n        headers: {\r\n          'Cache-Control': 'no-cache',\r\n          'Pragma': 'no-cache'\r\n        }\r\n      });\r\n      console.log('📊 API response received:', {\r\n        success: response.data.success,\r\n        totalImages: response.data.total_images,\r\n        imageCount: response.data.images?.length\r\n      });\r\n\r\n      if (response.data.success) {\r\n        // Process the data to extract defect locations with type information\r\n        const processedDefects = [];\r\n        \r\n        response.data.images.forEach(image => {\r\n          try {\r\n            // Only process images with valid coordinates\r\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\r\n            if (image.coordinates && image.coordinates !== 'Not Available') {\r\n              let lat, lng;\r\n\r\n              // First, try to use EXIF GPS coordinates if available (most accurate)\r\n              if (image.exif_data?.gps_coordinates) {\r\n                lat = image.exif_data.gps_coordinates.latitude;\r\n                lng = image.exif_data.gps_coordinates.longitude;\r\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              } else {\r\n                // Fallback to stored coordinates\r\n                // Handle different coordinate formats\r\n                if (typeof image.coordinates === 'string') {\r\n                  // Parse coordinates (expected format: \"lat,lng\")\r\n                  const coords = image.coordinates.split(',');\r\n                  if (coords.length === 2) {\r\n                    lat = parseFloat(coords[0].trim());\r\n                    lng = parseFloat(coords[1].trim());\r\n                  }\r\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\r\n                  // Handle array format [lat, lng]\r\n                  lat = parseFloat(image.coordinates[0]);\r\n                  lng = parseFloat(image.coordinates[1]);\r\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\r\n                  // Handle object format {lat: x, lng: y}\r\n                  lat = parseFloat(image.coordinates.lat);\r\n                  lng = parseFloat(image.coordinates.lng);\r\n                }\r\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              }\r\n\r\n            // Validate coordinates are within reasonable bounds\r\n            if (!isNaN(lat) && !isNaN(lng) &&\r\n                lat >= -90 && lat <= 90 &&\r\n                lng >= -180 && lng <= 180) {\r\n              console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\r\n              processedDefects.push({\r\n                id: image.id,\r\n                image_id: image.image_id,\r\n                type: image.type,\r\n                position: [lat, lng],\r\n                defect_count: image.defect_count,\r\n                timestamp: new Date(image.timestamp).toLocaleString(),\r\n                username: image.username,\r\n                original_image_id: image.original_image_id,\r\n                // For cracks, include type information if available\r\n                type_counts: image.type_counts,\r\n                // For kerbs, include condition information if available\r\n                condition_counts: image.condition_counts,\r\n                // EXIF and metadata information\r\n                exif_data: image.exif_data || {},\r\n                metadata: image.metadata || {},\r\n                media_type: image.media_type || 'image',\r\n                original_image_full_url: image.original_image_full_url\r\n              });\r\n            } else {\r\n              console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\r\n          }\r\n          } catch (coordError) {\r\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\r\n          }\r\n        });\r\n\r\n        console.log('Processed defects:', processedDefects.length);\r\n        setDefects(processedDefects);\r\n\r\n        // If no defects were processed, show a helpful message\r\n        if (processedDefects.length === 0) {\r\n          setError('No defects found with valid coordinates for the selected date range');\r\n        }\r\n      } else {\r\n        console.error('API returned success: false', response.data);\r\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching defect data:', err);\r\n      setError('Failed to load defect data: ' + (err.response?.data?.message || err.message));\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Fetch the list of users for the filter dropdown\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const params = {};\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      const response = await axios.get('/api/users/all', { params });\r\n      if (response.data.success) {\r\n        setUsersList(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  };\r\n\r\n  // Initialize default dates for the last 30 days\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n\r\n    console.log('DefectMap component initialized');\r\n  }, []);\r\n\r\n  // Fetch data when component mounts and when filters change\r\n  useEffect(() => {\r\n    fetchDefectData();\r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\r\n      fetchDefectData(true); // Force refresh to get latest EXIF data\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => {\r\n      console.log('🛑 Clearing auto-refresh interval');\r\n      clearInterval(interval);\r\n    };\r\n  }, [startDate, endDate, selectedUser, user?.role]);\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    fetchDefectData(true);\r\n  };\r\n\r\n  // Handle filter application\r\n  const handleApplyFilters = () => {\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle resetting filters\r\n  const handleResetFilters = () => {\r\n    // Reset date filters to last 30 days\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n    \r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n    setSelectedUser('');\r\n    setDefectTypeFilters({\r\n      pothole: true,\r\n      crack: true,\r\n      kerb: true,\r\n    });\r\n    \r\n    // Refetch data with reset filters\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle defect type filter changes\r\n  const handleDefectTypeFilterChange = (type) => {\r\n    setDefectTypeFilters(prevFilters => ({\r\n      ...prevFilters,\r\n      [type]: !prevFilters[type]\r\n    }));\r\n  };\r\n\r\n  // Filter defects based on applied filters\r\n  const filteredDefects = defects.filter(defect => \r\n    defectTypeFilters[defect.type]\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-sm dashboard-card mb-4\">\r\n      <Card.Header className=\"bg-primary text-white\">\r\n        <h5 className=\"mb-0\">Defect Map View</h5>\r\n      </Card.Header>\r\n      <Card.Body>\r\n        <Row className=\"mb-3\">\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Date Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Start Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={startDate}\r\n                      onChange={(e) => setStartDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">End Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={endDate}\r\n                      onChange={(e) => setEndDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>User Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Select User</Form.Label>\r\n                    <Form.Select\r\n                      value={selectedUser}\r\n                      onChange={(e) => setSelectedUser(e.target.value)}\r\n                      size=\"sm\"\r\n                    >\r\n                      <option value=\"\">All Users</option>\r\n                      {usersList.map((user, index) => (\r\n                        <option key={index} value={user.username}>\r\n                          {user.username} ({user.role})\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Defect Type Filter</h6>\r\n            <div className=\"filter-controls defect-type-filters\">\r\n              <div className=\"defect-type-filter-options\" style={{ marginTop: \"0\", paddingLeft: \"0\", width: \"100%\", minWidth: \"150px\" }}>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"pothole-filter\"\r\n                  label=\"Potholes\"\r\n                  checked={defectTypeFilters.pothole}\r\n                  onChange={() => handleDefectTypeFilterChange('pothole')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"crack-filter\"\r\n                  label=\"Cracks\"\r\n                  checked={defectTypeFilters.crack}\r\n                  onChange={() => handleDefectTypeFilterChange('crack')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"kerb-filter\"\r\n                  label=\"Kerbs\"\r\n                  checked={defectTypeFilters.kerb}\r\n                  onChange={() => handleDefectTypeFilterChange('kerb')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        <Row className=\"mb-3\">\r\n          <Col className=\"text-center\">\r\n            <div className=\"filter-actions-container\">\r\n              <Button \r\n                variant=\"primary\" \r\n                size=\"sm\" \r\n                onClick={handleApplyFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Apply Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={handleResetFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Reset Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading}\r\n                className=\"filter-btn\"\r\n              >\r\n                {loading ? '🔄 Refreshing...' : '🔄 Refresh Map'}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Row>\r\n          <Col>\r\n            <div className=\"map-legend mb-3\">\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📷 Images</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FF0000' }}></div>\r\n                  <span>Potholes</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FFCC00' }}></div>\r\n                  <span>Cracks</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#0066FF' }}></div>\r\n                  <span>Kerb Defects</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📹 Videos</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FF0000' }}>📹</div>\r\n                  <span>Pothole Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FFCC00' }}>📹</div>\r\n                  <span>Crack Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#0066FF' }}>📹</div>\r\n                  <span>Kerb Videos</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center p-5\">\r\n            <div className=\"spinner-border text-primary\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"alert alert-danger\">{error}</div>\r\n        ) : (\r\n          <div className=\"map-container\" style={{ height: '500px', width: '100%', position: 'relative' }}>\r\n            <MapErrorBoundary>\r\n              <MapContainer\r\n              center={center}\r\n              zoom={zoom}\r\n              style={{ height: '100%', width: '100%' }}\r\n              scrollWheelZoom={true}\r\n              key={`map-${center[0]}-${center[1]}-${zoom}`} // Force remount on center/zoom change\r\n            >\r\n              {/* Ensure Leaflet recalculates size after mount/visibility */}\r\n              <MapSizeFix mapRef={mapRef} />\r\n              <TileLayer\r\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              />\r\n              \r\n              {filteredDefects.map((defect) => {\r\n                // Defensive programming: ensure defect has required properties\r\n                if (!defect || !defect.position || !Array.isArray(defect.position) || defect.position.length !== 2) {\r\n                  console.warn('Invalid defect data:', defect);\r\n                  return null;\r\n                }\r\n\r\n                // Determine icon based on media type and defect type\r\n                const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\r\n                const selectedIcon = icons[iconKey] || icons[defect.type] || icons['pothole']; // fallback icon\r\n\r\n                return (\r\n                <Marker\r\n                  key={defect.id || `defect-${Math.random()}`}\r\n                  position={defect.position}\r\n                  icon={selectedIcon}\r\n                >\r\n                  <Popup maxWidth={400}>\r\n                    <div className=\"defect-popup\">\r\n                      <h6>{defect.type.charAt(0).toUpperCase() + defect.type.slice(1)} Defect</h6>\r\n\r\n                      {/* Video Thumbnail */}\r\n                      {defect.media_type === 'video' && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          {defect.representative_frame ? (\r\n                            <>\r\n                              <img\r\n                                src={`data:image/jpeg;base64,${defect.representative_frame}`}\r\n                                alt=\"Video thumbnail\"\r\n                                className=\"img-fluid border rounded shadow-sm\"\r\n                                style={{ maxHeight: '150px', maxWidth: '100%', objectFit: 'cover' }}\r\n                                onError={(e) => {\r\n                                  console.warn(`Failed to load representative frame for video ${defect.image_id}`);\r\n                                  e.target.style.display = 'none';\r\n                                  // Show fallback message\r\n                                  const fallback = e.target.nextElementSibling;\r\n                                  if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\r\n                                    fallback.style.display = 'block';\r\n                                  }\r\n                                }}\r\n                              />\r\n                              <div className=\"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\" style={{ display: 'none' }}>\r\n                                <i className=\"fas fa-video\"></i> Video thumbnail unavailable\r\n                              </div>\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"text-muted small p-3 border rounded bg-light\">\r\n                              <i className=\"fas fa-video fa-2x mb-2\"></i>\r\n                              <div>Video thumbnail not available</div>\r\n                            </div>\r\n                          )}\r\n                          <div className=\"mt-2\">\r\n                            <small className=\"text-info fw-bold\">📹 Video Thumbnail</small>\r\n                            {defect.video_id && (\r\n                              <div>\r\n                                <small className=\"text-muted d-block\">Video ID: {defect.video_id}</small>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Enhanced Image Display with Fallbacks */}\r\n                      {defect.media_type !== 'video' && (\r\n                        <EnhancedMapImageDisplay defect={defect} />\r\n                      )}\r\n\r\n                      {/* Basic Information */}\r\n                      <div className=\"mb-3\">\r\n                        <h6 className=\"text-primary\">Basic Information</h6>\r\n                        <ul className=\"list-unstyled small\">\r\n                          <li><strong>Count:</strong> {defect.defect_count}</li>\r\n                          <li><strong>Date:</strong> {defect.timestamp}</li>\r\n                          <li><strong>Reported by:</strong> {defect.username}</li>\r\n                          <li><strong>Media Type:</strong> {defect.media_type}</li>\r\n                          <li><strong>GPS:</strong> {defect.position[0].toFixed(6)}, {defect.position[1].toFixed(6)}</li>\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Defect-specific Information */}\r\n                      {defect.type === 'crack' && defect.type_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Crack Types</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.type_counts).map(([type, count]) => (\r\n                              <li key={type}><strong>{type}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {defect.type === 'kerb' && defect.condition_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Kerb Conditions</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.condition_counts).map(([condition, count]) => (\r\n                              <li key={condition}><strong>{condition}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* EXIF/Metadata Information */}\r\n                      {(defect.exif_data || defect.metadata) && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">📊 Media Information</h6>\r\n                          <div className=\"small\">\r\n                            {/* GPS Information - Show first as it's most important for mapping */}\r\n                            {defect.exif_data?.gps_coordinates && (\r\n                              <div className=\"mb-2 p-2 bg-light rounded\">\r\n                                <strong className=\"text-success\">🌍 GPS (EXIF):</strong>\r\n                                <div>Lat: {defect.exif_data.gps_coordinates.latitude?.toFixed(6)}</div>\r\n                                <div>Lng: {defect.exif_data.gps_coordinates.longitude?.toFixed(6)}</div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Camera Information */}\r\n                            {defect.exif_data?.camera_info && Object.keys(defect.exif_data.camera_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📷 Camera:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.camera_info.camera_make && (\r\n                                    <li>Make: {defect.exif_data.camera_info.camera_make}</li>\r\n                                  )}\r\n                                  {defect.exif_data.camera_info.camera_model && (\r\n                                    <li>Model: {defect.exif_data.camera_info.camera_model}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Technical Information */}\r\n                            {defect.exif_data?.technical_info && Object.keys(defect.exif_data.technical_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>⚙️ Technical:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.technical_info.iso && (\r\n                                    <li>ISO: {defect.exif_data.technical_info.iso}</li>\r\n                                  )}\r\n                                  {defect.exif_data.technical_info.exposure_time && (\r\n                                    <li>Exposure: {defect.exif_data.technical_info.exposure_time}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Basic Media Info */}\r\n                            {defect.exif_data?.basic_info && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📐 Dimensions:</strong> {defect.exif_data.basic_info.width} × {defect.exif_data.basic_info.height}\r\n                                {defect.exif_data.basic_info.format && (\r\n                                  <span> ({defect.exif_data.basic_info.format})</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Video-specific metadata */}\r\n                            {defect.media_type === 'video' && (\r\n                              <div className=\"mb-3\">\r\n                                <h6 className=\"text-info\">📹 Video Information</h6>\r\n                                <ul className=\"list-unstyled small\">\r\n                                  {defect.metadata?.format_info?.duration && (\r\n                                    <li><strong>Duration:</strong> {Math.round(defect.metadata.format_info.duration)}s</li>\r\n                                  )}\r\n                                  {defect.metadata?.basic_info?.width && defect.metadata?.basic_info?.height && (\r\n                                    <li><strong>Resolution:</strong> {defect.metadata.basic_info.width}x{defect.metadata.basic_info.height}</li>\r\n                                  )}\r\n                                  {defect.metadata?.format_info?.format_name && (\r\n                                    <li><strong>Format:</strong> {defect.metadata.format_info.format_name.toUpperCase()}</li>\r\n                                  )}\r\n                                  {defect.video_id && (\r\n                                    <li><strong>Video ID:</strong> {defect.video_id}</li>\r\n                                  )}\r\n                                  {/* Show detection counts for videos */}\r\n                                  {defect.model_outputs && (\r\n                                    <>\r\n                                      {defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && (\r\n                                        <li><strong>Potholes Detected:</strong> {defect.model_outputs.potholes.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && (\r\n                                        <li><strong>Cracks Detected:</strong> {defect.model_outputs.cracks.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && (\r\n                                        <li><strong>Kerbs Detected:</strong> {defect.model_outputs.kerbs.length}</li>\r\n                                      )}\r\n                                    </>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Action Buttons */}\r\n                      <div className=\"d-flex gap-2\">\r\n                        <Link\r\n                          to={`/view/${defect.image_id}`}\r\n                          className=\"btn btn-sm btn-primary\"\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                        {/* Only show 'View Original' button for non-video entries */}\r\n                        {defect.media_type !== 'video' && defect.original_image_full_url && (\r\n                          <a\r\n                            href={defect.original_image_full_url}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"btn btn-sm btn-outline-secondary\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            View Original\r\n                          </a>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Popup>\r\n                </Marker>\r\n                );\r\n              })}\r\n            </MapContainer>\r\n            </MapErrorBoundary>\r\n          </div>\r\n        )}\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DefectMap; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACvE,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,SAAStB,KAAK,CAACuB,SAAS,CAAC;EAC7CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC;EAC/C;EAEA,OAAOC,wBAAwBA,CAACD,KAAK,EAAE;IACrC,OAAO;MAAED,QAAQ,EAAE,IAAI;MAAEC;IAAM,CAAC;EAClC;EAEAE,iBAAiBA,CAACF,KAAK,EAAEG,SAAS,EAAE;IAClCC,OAAO,CAACJ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,EAAEG,SAAS,CAAC;EACxE;EAEAE,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACER,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BpB,OAAA;UAAAgB,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnEpB,OAAA;UACEe,SAAS,EAAC,wBAAwB;UAClCM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAR,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEV;IAEA,OAAO,IAAI,CAACd,KAAK,CAACU,QAAQ;EAC5B;AACF;;AAEA;AACA,SAASS,UAAUA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC9B,MAAMC,GAAG,GAAGvC,MAAM,CAAC,CAAC;EAEpBP,SAAS,CAAC,MAAM;IACd;IACA,IAAI4C,MAAM,EAAE;MACVA,MAAM,CAACG,OAAO,GAAGD,GAAG;IACtB;;IAEA;IACA,MAAME,EAAE,GAAGC,UAAU,CAAC,MAAM;MAC1B,IAAI;QACF,IAAIH,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,gCAAgC,EAAExB,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,MAAMyB,EAAE,GAAGH,UAAU,CAAC,MAAM;MAC1B,IAAI;QACF,IAAIH,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,gCAAgC,EAAExB,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA,MAAM0B,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI;QACF,IAAIP,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,mBAAmB,EAAExB,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDa,MAAM,CAACc,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;;IAE3C;IACA,MAAME,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIT,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,qBAAqB,EAAExB,KAAK,CAAC;MAC5C;IACF,CAAC;IAED,MAAM6B,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIV,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,qBAAqB,EAAExB,KAAK,CAAC;MAC5C;IACF,CAAC;IAED,IAAImB,GAAG,EAAE;MACPA,GAAG,CAACW,EAAE,CAAC,SAAS,EAAEF,SAAS,CAAC;MAC5BT,GAAG,CAACW,EAAE,CAAC,SAAS,EAAED,SAAS,CAAC;IAC9B;IAEA,OAAO,MAAM;MACXE,YAAY,CAACV,EAAE,CAAC;MAChBU,YAAY,CAACN,EAAE,CAAC;MAChBZ,MAAM,CAACmB,mBAAmB,CAAC,QAAQ,EAAEN,QAAQ,CAAC;MAC9C,IAAIP,GAAG,EAAE;QACP,IAAI;UACFA,GAAG,CAACc,GAAG,CAAC,SAAS,EAAEL,SAAS,CAAC;UAC7BT,GAAG,CAACc,GAAG,CAAC,SAAS,EAAEJ,SAAS,CAAC;QAC/B,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdI,OAAO,CAACoB,IAAI,CAAC,oBAAoB,EAAExB,KAAK,CAAC;QAC3C;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACmB,GAAG,EAAEF,MAAM,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AAHAC,EAAA,CAvFSF,UAAU;EAAA,QACLpC,MAAM;AAAA;AAAAsD,EAAA,GADXlB,UAAU;AA2FnB,MAAMmB,qBAAqB,GAAGA,CAACC,SAAS,EAAEC,SAAS,GAAG,UAAU,KAAK;EACnEjC,OAAO,CAACkC,GAAG,CAAC,sCAAsC,EAAE;IAAEF,SAAS;IAAEC;EAAU,CAAC,CAAC;EAE7E,IAAI,CAACD,SAAS,EAAE;IACdhC,OAAO,CAACkC,GAAG,CAAC,yBAAyB,CAAC;IACtC,OAAO,IAAI;EACb;;EAEA;EACA,IAAIF,SAAS,CAACG,UAAU,KAAK,OAAO,IAAIH,SAAS,CAACI,oBAAoB,EAAE;IACtEpC,OAAO,CAACkC,GAAG,CAAC,8CAA8C,CAAC;IAC3D,OAAO,0BAA0BF,SAAS,CAACI,oBAAoB,EAAE;EACnE;;EAEA;EACA,MAAMC,YAAY,GAAG,GAAGJ,SAAS,iBAAiB;EAClD,IAAID,SAAS,CAACK,YAAY,CAAC,EAAE;IAC3BrC,OAAO,CAACkC,GAAG,CAAC,0BAA0B,EAAEG,YAAY,EAAEL,SAAS,CAACK,YAAY,CAAC,CAAC;IAC9E;IACA,MAAMC,QAAQ,GAAGN,SAAS,CAACK,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;IACnD,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,IAAIH,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAE;MAC3D,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,KAAK,CAACN,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;MACvD,MAAMC,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;MAC1E7C,OAAO,CAACkC,GAAG,CAAC,sCAAsC,EAAEc,QAAQ,CAAC;MAC7D,OAAOA,QAAQ;IACjB;EACF;;EAEA;EACA,MAAME,UAAU,GAAG,GAAGjB,SAAS,eAAe;EAC9C,IAAID,SAAS,CAACkB,UAAU,CAAC,EAAE;IACzBlD,OAAO,CAACkC,GAAG,CAAC,wBAAwB,EAAEgB,UAAU,EAAElB,SAAS,CAACkB,UAAU,CAAC,CAAC;IACxE,MAAML,KAAK,GAAGb,SAAS,CAACkB,UAAU,CAAC;IACnC,MAAMF,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;IAC1E7C,OAAO,CAACkC,GAAG,CAAC,oCAAoC,EAAEc,QAAQ,CAAC;IAC3D,OAAOA,QAAQ;EACjB;;EAEA;EACA,MAAMG,aAAa,GAAG,GAAGlB,SAAS,WAAW;EAC7C,IAAID,SAAS,CAACmB,aAAa,CAAC,EAAE;IAC5BnD,OAAO,CAACkC,GAAG,CAAC,4BAA4B,EAAEiB,aAAa,EAAEnB,SAAS,CAACmB,aAAa,CAAC,CAAC;IAClF,OAAO,2BAA2BnB,SAAS,CAACmB,aAAa,CAAC,EAAE;EAC9D;EAEAnD,OAAO,CAACoB,IAAI,CAAC,iCAAiC,EAAEa,SAAS,EAAED,SAAS,CAACoB,QAAQ,CAAC;EAC9E,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyB,QAAQ,EAAE+D,WAAW,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyF,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMgG,GAAG,GAAGC,gBAAgB,CAACZ,MAAM,CAAC;IACpCtD,OAAO,CAACkC,GAAG,CAAC,oCAAoC,EAAE+B,GAAG,CAAC;IACtDR,kBAAkB,CAACQ,GAAG,CAAC;IACvBP,WAAW,CAAC,KAAK,CAAC;IAClBE,YAAY,CAAC,IAAI,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMa,gBAAgB,GAAIC,CAAC,IAAK;IAC9BpE,OAAO,CAACoB,IAAI,CAAC,oCAAoCyC,gBAAgB,GAAG,CAAC,IAAI,EAAEL,eAAe,CAAC;IAE3F,IAAIK,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAMQ,QAAQ,GAAGf,MAAM,CAACgB,iBAAiB;MACzC,IAAID,QAAQ,IAAI,CAACb,eAAe,CAACb,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtD,MAAM4B,SAAS,GAAG,2BAA2BF,QAAQ,EAAE;QACvDrE,OAAO,CAACkC,GAAG,CAAC,4BAA4B,EAAEqC,SAAS,CAAC;QACpDd,kBAAkB,CAACc,SAAS,CAAC;QAC7BT,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA;IACA9D,OAAO,CAACJ,KAAK,CAAC,0CAA0C,EAAE0D,MAAM,CAACF,QAAQ,CAAC;IAC1EM,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5BxE,OAAO,CAACkC,GAAG,CAAC,kCAAkC,EAAEsB,eAAe,CAAC;IAChEI,YAAY,CAAC,KAAK,CAAC;IACnBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAI,CAACF,eAAe,EAAE;IACpB,oBACErE,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhB,OAAA;UAAGe,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,wBAClC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIoD,SAAS,EAAE;IACb,oBACExE,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,kDAAkD;QAACuE,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAvE,QAAA,gBAC9FhB,OAAA,CAACH,OAAO;UAAC2F,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,IAAI;UAACC,OAAO,EAAC;QAAS;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DpB,OAAA;UAAMe,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIZ,QAAQ,EAAE;IACZ,oBACER,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DhB,OAAA;UAAGe,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sBACjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BhB,OAAA;MACE2F,GAAG,EAAEtB,eAAgB;MACrBuB,GAAG,EAAC,cAAc;MAClB7E,SAAS,EAAC,0BAA0B;MACpCuE,KAAK,EAAE;QAAEO,SAAS,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAChDC,OAAO,EAAEf,gBAAiB;MAC1BgB,MAAM,EAAEX;IAAgB;MAAApE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFpB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhB,OAAA;QAAOe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEsD,gBAAgB,GAAG,CAAC,iBACnB1E,OAAA;QAAAgB,QAAA,eACEhB,OAAA;UAAOe,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAgD,GAAA,CAtGMF,uBAAuB;AAAA+B,GAAA,GAAvB/B,uBAAuB;AAuG7B,OAAO3E,CAAC,CAAC2G,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3C9G,CAAC,CAAC2G,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,KAAK;EACnD,MAAMC,QAAQ,GAAGD,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAME,UAAU,GAAGF,OAAO;EACxB;EACA,qEAAqED,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA,8CAA8CF,KAAK;AACnD,sDAAsDA,KAAK;AAC3D,WAAW;EACP;EACA,qEAAqEA,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA,WAAW;EAET,OAAOtH,CAAC,CAACwH,IAAI,CAAC;IACZP,OAAO,EAAE,oCAAoC1C,kBAAkB,CAACgD,UAAU,CAAC,EAAE;IAC7ED,QAAQ,EAAEA,QAAQ;IAClBG,UAAU,EAAE,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxCI,WAAW,EAAE,CAAC,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,KAAK,GAAG;EACZC,OAAO,EAAET,gBAAgB,CAAC,SAAS,CAAC;EAAE;EACtCU,KAAK,EAAEV,gBAAgB,CAAC,SAAS,CAAC;EAAI;EACtCW,IAAI,EAAEX,gBAAgB,CAAC,SAAS,CAAC;EAAK;EACtC;EACA,eAAe,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAE;EACpD,aAAa,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAI;EACpD,YAAY,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAK;AACtD,CAAC;AAED,SAASY,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,GAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4I,OAAO,EAAEC,UAAU,CAAC,GAAG7I,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEoH,QAAQ,CAAC,GAAG9I,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+I,MAAM,CAAC,GAAG/I,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgJ,IAAI,CAAC,GAAGhJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM2C,MAAM,GAAG1C,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAACgJ,SAAS,EAAEC,YAAY,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmJ,OAAO,EAAEC,UAAU,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqJ,YAAY,EAAEC,eAAe,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuJ,SAAS,EAAEC,YAAY,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1J,QAAQ,CAAC;IACzDoI,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMqB,eAAe,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACtD,IAAI;MAAA,IAAAC,qBAAA;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIgB,MAAM,GAAG,CAAC,CAAC;MACf,IAAIb,SAAS,EAAEa,MAAM,CAACC,UAAU,GAAGd,SAAS;MAC5C,IAAIE,OAAO,EAAEW,MAAM,CAACE,QAAQ,GAAGb,OAAO;MACtC,IAAIE,YAAY,EAAES,MAAM,CAACG,QAAQ,GAAGZ,YAAY;MAChD,IAAIb,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;;MAE5C;MACAJ,MAAM,CAACM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIV,YAAY,EAAE;QAChBE,MAAM,CAACS,MAAM,GAAG,MAAM;QACtBT,MAAM,CAACU,QAAQ,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3D;MAEA9I,OAAO,CAACkC,GAAG,CAAC,sCAAsC,EAAE8F,MAAM,CAAC;MAC3D,MAAMe,QAAQ,GAAG,MAAMtK,KAAK,CAACuK,GAAG,CAAC,4BAA4B,EAAE;QAC7DhB,MAAM;QACN;QACAiB,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACFjJ,OAAO,CAACkC,GAAG,CAAC,2BAA2B,EAAE;QACvCgH,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;QAC9BE,WAAW,EAAEL,QAAQ,CAACI,IAAI,CAACE,YAAY;QACvCC,UAAU,GAAAvB,qBAAA,GAAEgB,QAAQ,CAACI,IAAI,CAACI,MAAM,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsBnF;MACpC,CAAC,CAAC;MAEF,IAAImG,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzB;QACA,MAAMM,gBAAgB,GAAG,EAAE;QAE3BT,QAAQ,CAACI,IAAI,CAACI,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;UACpC,IAAI;YACF;YACA1J,OAAO,CAACkC,GAAG,CAAC,iBAAiBwH,KAAK,CAACvH,UAAU,IAAI,OAAO,IAAIuH,KAAK,CAACtG,QAAQ,iBAAiBsG,KAAK,CAACC,WAAW,UAAUD,KAAK,CAACE,IAAI,EAAE,CAAC;YACnI,IAAIF,KAAK,CAACC,WAAW,IAAID,KAAK,CAACC,WAAW,KAAK,eAAe,EAAE;cAAA,IAAAE,gBAAA;cAC9D,IAAIC,GAAG,EAAEC,GAAG;;cAEZ;cACA,KAAAF,gBAAA,GAAIH,KAAK,CAACM,SAAS,cAAAH,gBAAA,eAAfA,gBAAA,CAAiBI,eAAe,EAAE;gBACpCH,GAAG,GAAGJ,KAAK,CAACM,SAAS,CAACC,eAAe,CAACC,QAAQ;gBAC9CH,GAAG,GAAGL,KAAK,CAACM,SAAS,CAACC,eAAe,CAACE,SAAS;gBAC/CnK,OAAO,CAACkC,GAAG,CAAC,qCAAqCwH,KAAK,CAACvH,UAAU,IAAI,OAAO,IAAIuH,KAAK,CAACtG,QAAQ,MAAM0G,GAAG,KAAKC,GAAG,GAAG,CAAC;cACrH,CAAC,MAAM;gBACL;gBACA;gBACA,IAAI,OAAOL,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;kBACzC;kBACA,MAAMS,MAAM,GAAGV,KAAK,CAACC,WAAW,CAACpH,KAAK,CAAC,GAAG,CAAC;kBAC3C,IAAI6H,MAAM,CAACxH,MAAM,KAAK,CAAC,EAAE;oBACvBkH,GAAG,GAAGO,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;oBAClCP,GAAG,GAAGM,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;kBACpC;gBACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACd,KAAK,CAACC,WAAW,CAAC,IAAID,KAAK,CAACC,WAAW,CAAC/G,MAAM,KAAK,CAAC,EAAE;kBAC7E;kBACAkH,GAAG,GAAGO,UAAU,CAACX,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;kBACtCI,GAAG,GAAGM,UAAU,CAACX,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,MAAM,IAAI,OAAOD,KAAK,CAACC,WAAW,KAAK,QAAQ,IAAID,KAAK,CAACC,WAAW,CAACG,GAAG,IAAIJ,KAAK,CAACC,WAAW,CAACI,GAAG,EAAE;kBAClG;kBACAD,GAAG,GAAGO,UAAU,CAACX,KAAK,CAACC,WAAW,CAACG,GAAG,CAAC;kBACvCC,GAAG,GAAGM,UAAU,CAACX,KAAK,CAACC,WAAW,CAACI,GAAG,CAAC;gBACzC;gBACA/J,OAAO,CAACkC,GAAG,CAAC,mCAAmCwH,KAAK,CAACvH,UAAU,IAAI,OAAO,IAAIuH,KAAK,CAACtG,QAAQ,MAAM0G,GAAG,KAAKC,GAAG,GAAG,CAAC;cACnH;;cAEF;cACA,IAAI,CAACU,KAAK,CAACX,GAAG,CAAC,IAAI,CAACW,KAAK,CAACV,GAAG,CAAC,IAC1BD,GAAG,IAAI,CAAC,EAAE,IAAIA,GAAG,IAAI,EAAE,IACvBC,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;gBAC7B/J,OAAO,CAACkC,GAAG,CAAC,2BAA2BwH,KAAK,CAACvH,UAAU,IAAI,OAAO,IAAIuH,KAAK,CAACgB,EAAE,MAAMZ,GAAG,KAAKC,GAAG,mBAAmB,CAAC;gBACnHP,gBAAgB,CAACmB,IAAI,CAAC;kBACpBD,EAAE,EAAEhB,KAAK,CAACgB,EAAE;kBACZtH,QAAQ,EAAEsG,KAAK,CAACtG,QAAQ;kBACxBwG,IAAI,EAAEF,KAAK,CAACE,IAAI;kBAChBgB,QAAQ,EAAE,CAACd,GAAG,EAAEC,GAAG,CAAC;kBACpBc,YAAY,EAAEnB,KAAK,CAACmB,YAAY;kBAChCC,SAAS,EAAE,IAAIvC,IAAI,CAACmB,KAAK,CAACoB,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;kBACrD5C,QAAQ,EAAEuB,KAAK,CAACvB,QAAQ;kBACxB7D,iBAAiB,EAAEoF,KAAK,CAACpF,iBAAiB;kBAC1C;kBACA0G,WAAW,EAAEtB,KAAK,CAACsB,WAAW;kBAC9B;kBACAC,gBAAgB,EAAEvB,KAAK,CAACuB,gBAAgB;kBACxC;kBACAjB,SAAS,EAAEN,KAAK,CAACM,SAAS,IAAI,CAAC,CAAC;kBAChCkB,QAAQ,EAAExB,KAAK,CAACwB,QAAQ,IAAI,CAAC,CAAC;kBAC9B/I,UAAU,EAAEuH,KAAK,CAACvH,UAAU,IAAI,OAAO;kBACvCgJ,uBAAuB,EAAEzB,KAAK,CAACyB;gBACjC,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLnL,OAAO,CAACoB,IAAI,CAAC,6BAA6BsI,KAAK,CAACvH,UAAU,IAAI,OAAO,IAAIuH,KAAK,CAACgB,EAAE,GAAG,EAAEhB,KAAK,CAACC,WAAW,EAAE,eAAeG,GAAG,SAASC,GAAG,EAAE,CAAC;cAC5I;YACF,CAAC,MAAM;cACL/J,OAAO,CAACkC,GAAG,CAAC,eAAewH,KAAK,CAACvH,UAAU,IAAI,OAAO,IAAIuH,KAAK,CAACgB,EAAE,iBAAiBhB,KAAK,CAACC,WAAW,EAAE,CAAC;YACzG;UACA,CAAC,CAAC,OAAOyB,UAAU,EAAE;YACnBpL,OAAO,CAACJ,KAAK,CAAC,4CAA4C8J,KAAK,CAACgB,EAAE,GAAG,EAAEU,UAAU,EAAE1B,KAAK,CAACC,WAAW,CAAC;UACvG;QACF,CAAC,CAAC;QAEF3J,OAAO,CAACkC,GAAG,CAAC,oBAAoB,EAAEsH,gBAAgB,CAAC5G,MAAM,CAAC;QAC1DiE,UAAU,CAAC2C,gBAAgB,CAAC;;QAE5B;QACA,IAAIA,gBAAgB,CAAC5G,MAAM,KAAK,CAAC,EAAE;UACjCoE,QAAQ,CAAC,qEAAqE,CAAC;QACjF;MACF,CAAC,MAAM;QACLhH,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAEmJ,QAAQ,CAACI,IAAI,CAAC;QAC3DnC,QAAQ,CAAC,8BAA8B,IAAI+B,QAAQ,CAACI,IAAI,CAACkC,OAAO,IAAI,eAAe,CAAC,CAAC;MACvF;MAEAtE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOuE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZxL,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAE0L,GAAG,CAAC;MACjDtE,QAAQ,CAAC,8BAA8B,IAAI,EAAAuE,aAAA,GAAAD,GAAG,CAACvC,QAAQ,cAAAwC,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcpC,IAAI,cAAAqC,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;MACvFtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0E,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMzD,MAAM,GAAG,CAAC,CAAC;MACjB,IAAItB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;MAE5C,MAAMW,QAAQ,GAAG,MAAMtK,KAAK,CAACuK,GAAG,CAAC,gBAAgB,EAAE;QAAEhB;MAAO,CAAC,CAAC;MAC9D,IAAIe,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzBxB,YAAY,CAACqB,QAAQ,CAACI,IAAI,CAACuC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC,OAAO9L,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM0N,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAACxJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD6E,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAACxJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvDvC,OAAO,CAACkC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd4J,eAAe,CAAC,CAAC;IACjB4D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC/E,IAAI,CAAC,CAAC;;EAEV;EACAzI,SAAS,CAAC,MAAM;IACd,MAAM+N,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCjM,OAAO,CAACkC,GAAG,CAAC,+DAA+D,CAAC;MAC5E2F,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM;MACX7H,OAAO,CAACkC,GAAG,CAAC,mCAAmC,CAAC;MAChDgK,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAAC7E,SAAS,EAAEE,OAAO,EAAEE,YAAY,EAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC,CAAC;;EAElD;EACA,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvE,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMV,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAACxJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD6E,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAACxJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvDiF,eAAe,CAAC,EAAE,CAAC;IACnBI,oBAAoB,CAAC;MACnBtB,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACAqB,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyE,4BAA4B,GAAI1C,IAAI,IAAK;IAC7ChC,oBAAoB,CAAC2E,WAAW,KAAK;MACnC,GAAGA,WAAW;MACd,CAAC3C,IAAI,GAAG,CAAC2C,WAAW,CAAC3C,IAAI;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM4C,eAAe,GAAG5F,OAAO,CAAC6F,MAAM,CAACnJ,MAAM,IAC3CqE,iBAAiB,CAACrE,MAAM,CAACsG,IAAI,CAC/B,CAAC;EAED,oBACEzK,OAAA,CAACR,IAAI;IAACuB,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC7ChB,OAAA,CAACR,IAAI,CAAC+N,MAAM;MAACxM,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5ChB,OAAA;QAAIe,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdpB,OAAA,CAACR,IAAI,CAACgO,IAAI;MAAAxM,QAAA,gBACRhB,OAAA,CAACP,GAAG;QAACsB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA,CAACN,GAAG;UAAC+N,EAAE,EAAE,CAAE;UAAC1M,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAKe,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrChB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAAC+N,KAAK;kBAAA1M,QAAA,gBACThB,OAAA,CAACL,IAAI,CAACgO,KAAK;oBAAC5M,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrDpB,OAAA,CAACL,IAAI,CAACiO,OAAO;oBACXnD,IAAI,EAAC,MAAM;oBACXoD,KAAK,EAAE7F,SAAU;oBACjB8F,QAAQ,EAAG7I,CAAC,IAAKgD,YAAY,CAAChD,CAAC,CAAC8I,MAAM,CAACF,KAAK,CAAE;oBAC9CpI,IAAI,EAAC;kBAAI;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAAC+N,KAAK;kBAAA1M,QAAA,gBACThB,OAAA,CAACL,IAAI,CAACgO,KAAK;oBAAC5M,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnDpB,OAAA,CAACL,IAAI,CAACiO,OAAO;oBACXnD,IAAI,EAAC,MAAM;oBACXoD,KAAK,EAAE3F,OAAQ;oBACf4F,QAAQ,EAAG7I,CAAC,IAAKkD,UAAU,CAAClD,CAAC,CAAC8I,MAAM,CAACF,KAAK,CAAE;oBAC5CpI,IAAI,EAAC;kBAAI;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA,CAACN,GAAG;UAAC+N,EAAE,EAAE,CAAE;UAAC1M,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAKe,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrChB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAAC+N,KAAK;kBAAA1M,QAAA,gBACThB,OAAA,CAACL,IAAI,CAACgO,KAAK;oBAAC5M,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDpB,OAAA,CAACL,IAAI,CAACqO,MAAM;oBACVH,KAAK,EAAEzF,YAAa;oBACpB0F,QAAQ,EAAG7I,CAAC,IAAKoD,eAAe,CAACpD,CAAC,CAAC8I,MAAM,CAACF,KAAK,CAAE;oBACjDpI,IAAI,EAAC,IAAI;oBAAAzE,QAAA,gBAEThB,OAAA;sBAAQ6N,KAAK,EAAC,EAAE;sBAAA7M,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAClCkH,SAAS,CAAC1G,GAAG,CAAC,CAAC2F,IAAI,EAAE0G,KAAK,kBACzBjO,OAAA;sBAAoB6N,KAAK,EAAEtG,IAAI,CAACyB,QAAS;sBAAAhI,QAAA,GACtCuG,IAAI,CAACyB,QAAQ,EAAC,IAAE,EAACzB,IAAI,CAAC0B,IAAI,EAAC,GAC9B;oBAAA,GAFagF,KAAK;sBAAAhN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA,CAACN,GAAG;UAAC+N,EAAE,EAAE,CAAE;UAAC1M,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BpB,OAAA;YAAKe,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDhB,OAAA;cAAKe,SAAS,EAAC,4BAA4B;cAACuE,KAAK,EAAE;gBAAE4I,SAAS,EAAE,GAAG;gBAAEC,WAAW,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAArN,QAAA,gBACxHhB,OAAA,CAACL,IAAI,CAAC2O,KAAK;gBACT7D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,gBAAgB;gBACnBgD,KAAK,EAAC,UAAU;gBAChBC,OAAO,EAAEhG,iBAAiB,CAACrB,OAAQ;gBACnC2G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,SAAS,CAAE;gBACxDpM,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFpB,OAAA,CAACL,IAAI,CAAC2O,KAAK;gBACT7D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,cAAc;gBACjBgD,KAAK,EAAC,QAAQ;gBACdC,OAAO,EAAEhG,iBAAiB,CAACpB,KAAM;gBACjC0G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,OAAO,CAAE;gBACtDpM,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFpB,OAAA,CAACL,IAAI,CAAC2O,KAAK;gBACT7D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,aAAa;gBAChBgD,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAEhG,iBAAiB,CAACnB,IAAK;gBAChCyG,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,MAAM,CAAE;gBACrDpM,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACP,GAAG;QAACsB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhB,OAAA,CAACN,GAAG;UAACqB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BhB,OAAA;YAAKe,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvChB,OAAA,CAACJ,MAAM;cACL8F,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTpE,OAAO,EAAE4L,kBAAmB;cAC5BlM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACJ,MAAM;cACL8F,OAAO,EAAC,mBAAmB;cAC3BD,IAAI,EAAC,IAAI;cACTpE,OAAO,EAAE6L,kBAAmB;cAC5BnM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACJ,MAAM;cACL8F,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTpE,OAAO,EAAE2L,aAAc;cACvByB,QAAQ,EAAE9G,OAAQ;cAClB5G,SAAS,EAAC,YAAY;cAAAC,QAAA,EAErB2G,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAA1G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACP,GAAG;QAAAuB,QAAA,eACFhB,OAAA,CAACN,GAAG;UAAAsB,QAAA,eACFhB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BhB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhB,OAAA;gBAAIe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAACuE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU;gBAAE;kBAAAzN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAACuE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU;gBAAE;kBAAAzN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAACuE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU;gBAAE;kBAAAzN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhB,OAAA;gBAAIe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAACuE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU,CAAE;kBAAA1N,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAACuE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU,CAAE;kBAAA1N,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAACuE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU,CAAE;kBAAA1N,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELuG,OAAO,gBACN3H,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAACkI,IAAI,EAAC,QAAQ;UAAAjI,QAAA,eACxDhB,OAAA;YAAMe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJX,KAAK,gBACPT,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEjDpB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAACuE,KAAK,EAAE;UAAEqJ,MAAM,EAAE,OAAO;UAAEP,KAAK,EAAE,MAAM;UAAE3C,QAAQ,EAAE;QAAW,CAAE;QAAAzK,QAAA,eAC7FhB,OAAA,CAACG,gBAAgB;UAAAa,QAAA,eACfhB,OAAA,CAACf,YAAY;YACb6I,MAAM,EAAEA,MAAO;YACfC,IAAI,EAAEA,IAAK;YACXzC,KAAK,EAAE;cAAEqJ,MAAM,EAAE,MAAM;cAAEP,KAAK,EAAE;YAAO,CAAE;YACzCQ,eAAe,EAAE,IAAK;YAAA5N,QAAA,gBAItBhB,OAAA,CAACyB,UAAU;cAACC,MAAM,EAAEA;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BpB,OAAA,CAACd,SAAS;cACR2P,WAAW,EAAC,yFAAyF;cACrG/J,GAAG,EAAC;YAAoD;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EAEDiM,eAAe,CAACzL,GAAG,CAAEuC,MAAM,IAAK;cAAA,IAAA2K,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;cAC/B;cACA,IAAI,CAACxL,MAAM,IAAI,CAACA,MAAM,CAACsH,QAAQ,IAAI,CAACL,KAAK,CAACC,OAAO,CAAClH,MAAM,CAACsH,QAAQ,CAAC,IAAItH,MAAM,CAACsH,QAAQ,CAAChI,MAAM,KAAK,CAAC,EAAE;gBAClG5C,OAAO,CAACoB,IAAI,CAAC,sBAAsB,EAAEkC,MAAM,CAAC;gBAC5C,OAAO,IAAI;cACb;;cAEA;cACA,MAAMyL,OAAO,GAAGzL,MAAM,CAACnB,UAAU,KAAK,OAAO,GAAG,GAAGmB,MAAM,CAACsG,IAAI,QAAQ,GAAGtG,MAAM,CAACsG,IAAI;cACpF,MAAMoF,YAAY,GAAG3I,KAAK,CAAC0I,OAAO,CAAC,IAAI1I,KAAK,CAAC/C,MAAM,CAACsG,IAAI,CAAC,IAAIvD,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;;cAE/E,oBACAlH,OAAA,CAACb,MAAM;gBAELsM,QAAQ,EAAEtH,MAAM,CAACsH,QAAS;gBAC1B1E,IAAI,EAAE8I,YAAa;gBAAA7O,QAAA,eAEnBhB,OAAA,CAACZ,KAAK;kBAAC0G,QAAQ,EAAE,GAAI;kBAAA9E,QAAA,eACnBhB,OAAA;oBAAKe,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BhB,OAAA;sBAAAgB,QAAA,GAAKmD,MAAM,CAACsG,IAAI,CAACqF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG5L,MAAM,CAACsG,IAAI,CAAC9G,KAAK,CAAC,CAAC,CAAC,EAAC,SAAO;oBAAA;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAG3E+C,MAAM,CAACnB,UAAU,KAAK,OAAO,iBAC5BhD,OAAA;sBAAKe,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,GAC9BmD,MAAM,CAAClB,oBAAoB,gBAC1BjD,OAAA,CAAAE,SAAA;wBAAAc,QAAA,gBACEhB,OAAA;0BACE2F,GAAG,EAAE,0BAA0BxB,MAAM,CAAClB,oBAAoB,EAAG;0BAC7D2C,GAAG,EAAC,iBAAiB;0BACrB7E,SAAS,EAAC,oCAAoC;0BAC9CuE,KAAK,EAAE;4BAAEO,SAAS,EAAE,OAAO;4BAAEC,QAAQ,EAAE,MAAM;4BAAEkK,SAAS,EAAE;0BAAQ,CAAE;0BACpEjK,OAAO,EAAGd,CAAC,IAAK;4BACdpE,OAAO,CAACoB,IAAI,CAAC,iDAAiDkC,MAAM,CAACF,QAAQ,EAAE,CAAC;4BAChFgB,CAAC,CAAC8I,MAAM,CAACzI,KAAK,CAAC2K,OAAO,GAAG,MAAM;4BAC/B;4BACA,MAAMC,QAAQ,GAAGjL,CAAC,CAAC8I,MAAM,CAACoC,kBAAkB;4BAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,CAACC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;8BACvEH,QAAQ,CAAC5K,KAAK,CAAC2K,OAAO,GAAG,OAAO;4BAClC;0BACF;wBAAE;0BAAAhP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFpB,OAAA;0BAAKe,SAAS,EAAC,uEAAuE;0BAACuE,KAAK,EAAE;4BAAE2K,OAAO,EAAE;0BAAO,CAAE;0BAAAjP,QAAA,gBAChHhB,OAAA;4BAAGe,SAAS,EAAC;0BAAc;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,gCAClC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACN,CAAC,gBAEHpB,OAAA;wBAAKe,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,gBAC3DhB,OAAA;0BAAGe,SAAS,EAAC;wBAAyB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3CpB,OAAA;0BAAAgB,QAAA,EAAK;wBAA6B;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACN,eACDpB,OAAA;wBAAKe,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBhB,OAAA;0BAAOe,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAAC;wBAAkB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EAC9D+C,MAAM,CAACmM,QAAQ,iBACdtQ,OAAA;0BAAAgB,QAAA,eACEhB,OAAA;4BAAOe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAAC,YAAU,EAACmD,MAAM,CAACmM,QAAQ;0BAAA;4BAAArP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtE,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,EAGA+C,MAAM,CAACnB,UAAU,KAAK,OAAO,iBAC5BhD,OAAA,CAACkE,uBAAuB;sBAACC,MAAM,EAAEA;oBAAO;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC3C,eAGDpB,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnDpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,gBACjChB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACuH,YAAY;wBAAA;0BAAAzK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACwH,SAAS;wBAAA;0BAAA1K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAY;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC6E,QAAQ;wBAAA;0BAAA/H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAW;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACnB,UAAU;wBAAA;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC8E,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACpM,MAAM,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC8E,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAtP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,EAGL+C,MAAM,CAACsG,IAAI,KAAK,OAAO,IAAItG,MAAM,CAAC0H,WAAW,iBAC5C7L,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7CpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAChCwP,MAAM,CAACC,OAAO,CAACtM,MAAM,CAAC0H,WAAW,CAAC,CAACjK,GAAG,CAAC,CAAC,CAAC6I,IAAI,EAAEiG,KAAK,CAAC,kBACpD1Q,OAAA;0BAAAgB,QAAA,gBAAehB,OAAA;4BAAAgB,QAAA,GAASyJ,IAAI,EAAC,GAAC;0BAAA;4BAAAxJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACsP,KAAK;wBAAA,GAArCjG,IAAI;0BAAAxJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAsC,CACpD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN,EAEA+C,MAAM,CAACsG,IAAI,KAAK,MAAM,IAAItG,MAAM,CAAC2H,gBAAgB,iBAChD9L,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjDpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAChCwP,MAAM,CAACC,OAAO,CAACtM,MAAM,CAAC2H,gBAAgB,CAAC,CAAClK,GAAG,CAAC,CAAC,CAAC+O,SAAS,EAAED,KAAK,CAAC,kBAC9D1Q,OAAA;0BAAAgB,QAAA,gBAAoBhB,OAAA;4BAAAgB,QAAA,GAAS2P,SAAS,EAAC,GAAC;0BAAA;4BAAA1P,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACsP,KAAK;wBAAA,GAA/CC,SAAS;0BAAA1P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAA2C,CAC9D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN,EAGA,CAAC+C,MAAM,CAAC0G,SAAS,IAAI1G,MAAM,CAAC4H,QAAQ,kBACnC/L,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtDpB,OAAA;wBAAKe,SAAS,EAAC,OAAO;wBAAAC,QAAA,GAEnB,EAAA8N,iBAAA,GAAA3K,MAAM,CAAC0G,SAAS,cAAAiE,iBAAA,uBAAhBA,iBAAA,CAAkBhE,eAAe,kBAChC9K,OAAA;0BAAKe,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxChB,OAAA;4BAAQe,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAAC;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACxDpB,OAAA;4BAAAgB,QAAA,GAAK,OAAK,GAAA+N,qBAAA,GAAC5K,MAAM,CAAC0G,SAAS,CAACC,eAAe,CAACC,QAAQ,cAAAgE,qBAAA,uBAAzCA,qBAAA,CAA2CwB,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAAtP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACvEpB,OAAA;4BAAAgB,QAAA,GAAK,OAAK,GAAAgO,sBAAA,GAAC7K,MAAM,CAAC0G,SAAS,CAACC,eAAe,CAACE,SAAS,cAAAgE,sBAAA,uBAA1CA,sBAAA,CAA4CuB,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAAtP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrE,CACN,EAGA,EAAA6N,kBAAA,GAAA9K,MAAM,CAAC0G,SAAS,cAAAoE,kBAAA,uBAAhBA,kBAAA,CAAkB2B,WAAW,KAAIJ,MAAM,CAACK,IAAI,CAAC1M,MAAM,CAAC0G,SAAS,CAAC+F,WAAW,CAAC,CAACnN,MAAM,GAAG,CAAC,iBACpFzD,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAU;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC3BpB,OAAA;4BAAIe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAC/BmD,MAAM,CAAC0G,SAAS,CAAC+F,WAAW,CAACE,WAAW,iBACvC9Q,OAAA;8BAAAgB,QAAA,GAAI,QAAM,EAACmD,MAAM,CAAC0G,SAAS,CAAC+F,WAAW,CAACE,WAAW;4BAAA;8BAAA7P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACzD,EACA+C,MAAM,CAAC0G,SAAS,CAAC+F,WAAW,CAACG,YAAY,iBACxC/Q,OAAA;8BAAAgB,QAAA,GAAI,SAAO,EAACmD,MAAM,CAAC0G,SAAS,CAAC+F,WAAW,CAACG,YAAY;4BAAA;8BAAA9P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC3D;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN,EAGA,EAAA8N,kBAAA,GAAA/K,MAAM,CAAC0G,SAAS,cAAAqE,kBAAA,uBAAhBA,kBAAA,CAAkB8B,cAAc,KAAIR,MAAM,CAACK,IAAI,CAAC1M,MAAM,CAAC0G,SAAS,CAACmG,cAAc,CAAC,CAACvN,MAAM,GAAG,CAAC,iBAC1FzD,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAa;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC9BpB,OAAA;4BAAIe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAC/BmD,MAAM,CAAC0G,SAAS,CAACmG,cAAc,CAACC,GAAG,iBAClCjR,OAAA;8BAAAgB,QAAA,GAAI,OAAK,EAACmD,MAAM,CAAC0G,SAAS,CAACmG,cAAc,CAACC,GAAG;4BAAA;8BAAAhQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACnD,EACA+C,MAAM,CAAC0G,SAAS,CAACmG,cAAc,CAACE,aAAa,iBAC5ClR,OAAA;8BAAAgB,QAAA,GAAI,YAAU,EAACmD,MAAM,CAAC0G,SAAS,CAACmG,cAAc,CAACE,aAAa;4BAAA;8BAAAjQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAClE;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN,EAGA,EAAA+N,kBAAA,GAAAhL,MAAM,CAAC0G,SAAS,cAAAsE,kBAAA,uBAAhBA,kBAAA,CAAkBgC,UAAU,kBAC3BnR,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC0G,SAAS,CAACsG,UAAU,CAAC/C,KAAK,EAAC,QAAG,EAACjK,MAAM,CAAC0G,SAAS,CAACsG,UAAU,CAACxC,MAAM,EACxGxK,MAAM,CAAC0G,SAAS,CAACsG,UAAU,CAACC,MAAM,iBACjCpR,OAAA;4BAAAgB,QAAA,GAAM,IAAE,EAACmD,MAAM,CAAC0G,SAAS,CAACsG,UAAU,CAACC,MAAM,EAAC,GAAC;0BAAA;4BAAAnQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACpD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN,EAGA+C,MAAM,CAACnB,UAAU,KAAK,OAAO,iBAC5BhD,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAIe,SAAS,EAAC,WAAW;4BAAAC,QAAA,EAAC;0BAAoB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnDpB,OAAA;4BAAIe,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,GAChC,EAAAoO,gBAAA,GAAAjL,MAAM,CAAC4H,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBiC,WAAW,cAAAhC,qBAAA,uBAA5BA,qBAAA,CAA8BiC,QAAQ,kBACrCtR,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAACoI,IAAI,CAAC+H,KAAK,CAACpN,MAAM,CAAC4H,QAAQ,CAACsF,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;4BAAA;8BAAArQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CACvF,EACA,EAAAkO,iBAAA,GAAAnL,MAAM,CAAC4H,QAAQ,cAAAuD,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB6B,UAAU,cAAA5B,qBAAA,uBAA3BA,qBAAA,CAA6BnB,KAAK,OAAAoB,iBAAA,GAAIrL,MAAM,CAAC4H,QAAQ,cAAAyD,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB2B,UAAU,cAAA1B,qBAAA,uBAA3BA,qBAAA,CAA6Bd,MAAM,kBACxE3O,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAW;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC4H,QAAQ,CAACoF,UAAU,CAAC/C,KAAK,EAAC,GAAC,EAACjK,MAAM,CAAC4H,QAAQ,CAACoF,UAAU,CAACxC,MAAM;4BAAA;8BAAA1N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC5G,EACA,EAAAsO,iBAAA,GAAAvL,MAAM,CAAC4H,QAAQ,cAAA2D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB2B,WAAW,cAAA1B,qBAAA,uBAA5BA,qBAAA,CAA8B6B,WAAW,kBACxCxR,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAO;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC4H,QAAQ,CAACsF,WAAW,CAACG,WAAW,CAACzB,WAAW,CAAC,CAAC;4BAAA;8BAAA9O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACzF,EACA+C,MAAM,CAACmM,QAAQ,iBACdtQ,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACmM,QAAQ;4BAAA;8BAAArP,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACrD,EAEA+C,MAAM,CAACsN,aAAa,iBACnBzR,OAAA,CAAAE,SAAA;8BAAAc,QAAA,GACGmD,MAAM,CAACsN,aAAa,CAACC,QAAQ,IAAIvN,MAAM,CAACsN,aAAa,CAACC,QAAQ,CAACjO,MAAM,GAAG,CAAC,iBACxEzD,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAkB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACsN,aAAa,CAACC,QAAQ,CAACjO,MAAM;8BAAA;gCAAAxC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CACnF,EACA+C,MAAM,CAACsN,aAAa,CAACE,MAAM,IAAIxN,MAAM,CAACsN,aAAa,CAACE,MAAM,CAAClO,MAAM,GAAG,CAAC,iBACpEzD,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAgB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACsN,aAAa,CAACE,MAAM,CAAClO,MAAM;8BAAA;gCAAAxC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAC/E,EACA+C,MAAM,CAACsN,aAAa,CAACG,KAAK,IAAIzN,MAAM,CAACsN,aAAa,CAACG,KAAK,CAACnO,MAAM,GAAG,CAAC,iBAClEzD,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAe;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACsN,aAAa,CAACG,KAAK,CAACnO,MAAM;8BAAA;gCAAAxC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAC7E;4BAAA,eACD,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,eAGDpB,OAAA;sBAAKe,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BhB,OAAA,CAACF,IAAI;wBACH+R,EAAE,EAAE,SAAS1N,MAAM,CAACF,QAAQ,EAAG;wBAC/BlD,SAAS,EAAC,wBAAwB;wBAClCM,OAAO,EAAG4D,CAAC,IAAKA,CAAC,CAAC6M,eAAe,CAAC,CAAE;wBAAA9Q,QAAA,EACrC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAEN+C,MAAM,CAACnB,UAAU,KAAK,OAAO,IAAImB,MAAM,CAAC6H,uBAAuB,iBAC9DhM,OAAA;wBACE+R,IAAI,EAAE5N,MAAM,CAAC6H,uBAAwB;wBACrC+B,MAAM,EAAC,QAAQ;wBACfiE,GAAG,EAAC,qBAAqB;wBACzBjR,SAAS,EAAC,kCAAkC;wBAC5CM,OAAO,EAAG4D,CAAC,IAAKA,CAAC,CAAC6M,eAAe,CAAC,CAAE;wBAAA9Q,QAAA,EACrC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC,GA5MH+C,MAAM,CAACoH,EAAE,IAAI,UAAU/B,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;gBAAAxI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6MrC,CAAC;YAEX,CAAC,CAAC;UAAA,GArOG,OAAO0G,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIC,IAAI,EAAE;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsOhC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX;AAACoG,GAAA,CA7oBQF,SAAS;AAAA2K,GAAA,GAAT3K,SAAS;AA+oBlB,eAAeA,SAAS;AAAC,IAAA3E,EAAA,EAAAsD,GAAA,EAAAgM,GAAA;AAAAC,YAAA,CAAAvP,EAAA;AAAAuP,YAAA,CAAAjM,GAAA;AAAAiM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}