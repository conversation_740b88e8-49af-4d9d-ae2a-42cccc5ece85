{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\DefectDetail.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, Button, Spinner, Alert, Badge } from 'react-bootstrap';\nimport axios from 'axios';\nimport './dashboard.css';\n\n/**\r\n * Enhanced Image Display Component for Defect Details\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EnhancedDefectImageDisplay = ({\n  imageData,\n  imageType,\n  defectType\n}) => {\n  _s();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n\n  // CLEAN IMAGE URL GENERATION - SAME LOGIC AS \"ALL UPLOADED IMAGES\" SECTION\n  const generateImageUrl = (data, type) => {\n    if (!data) {\n      console.log('❌ No data provided to generateImageUrl');\n      return null;\n    }\n    console.log('🔍 Generating image URL for:', {\n      type,\n      imageId: data.image_id,\n      availableFields: Object.keys(data).filter(key => key.includes('image'))\n    });\n\n    // Check if this is video data with representative frame\n    if (data.media_type === 'video' && data.representative_frame) {\n      console.log('📹 Using representative frame for video');\n      return `data:image/jpeg;base64,${data.representative_frame}`;\n    }\n\n    // Priority 1: Try S3 key with proxy endpoint (same as Dashboard) - MOST RELIABLE\n    const s3KeyField = `${type}_image_s3_url`;\n    if (data[s3KeyField]) {\n      console.log('🔗 PRIORITY 1: Using S3 key field:', s3KeyField, '=', data[s3KeyField]);\n      const s3Key = data[s3KeyField];\n\n      // FIXED: Use proper encoding - encode the entire S3 key as one unit\n      const encodedKey = encodeURIComponent(s3Key);\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\n      console.log('✅ Generated proxy URL from S3 key (FIXED encoding):', proxyUrl);\n      console.log('   Original S3 key:', s3Key);\n      console.log('   Encoded S3 key:', encodedKey);\n      return proxyUrl;\n    }\n\n    // Priority 2: Try S3 full URL with proxy extraction (same as Dashboard)\n    const fullUrlField = `${type}_image_full_url`;\n    if (data[fullUrlField]) {\n      console.log('🔗 PRIORITY 2: Using full URL field:', fullUrlField, '=', data[fullUrlField]);\n      // Extract S3 key from full URL and use proxy endpoint\n      const urlParts = data[fullUrlField].split('/');\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n        // FIXED: Use proper encoding - encode the entire S3 key as one unit\n        const encodedKey = encodeURIComponent(s3Key);\n        const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\n        console.log('✅ Generated proxy URL from full URL (FIXED encoding):', proxyUrl);\n        console.log('   Extracted S3 key:', s3Key);\n        console.log('   Encoded S3 key:', encodedKey);\n        return proxyUrl;\n      }\n    }\n\n    // Priority 3: Try GridFS endpoint (legacy support)\n    const gridfsIdField = `${type}_image_id`;\n    if (data[gridfsIdField]) {\n      console.log('🗄️ PRIORITY 3: Using GridFS endpoint:', gridfsIdField, '=', data[gridfsIdField]);\n      return `/api/pavement/get-image/${data[gridfsIdField]}`;\n    }\n    console.log('❌ No valid image URL found for type:', type);\n    console.log('❌ Available data fields:', Object.keys(data));\n    return null;\n  };\n\n  // Initialize image URL - Clean approach like Dashboard\n  useEffect(() => {\n    console.log('🔄 DefectDetail Image Component - useEffect triggered');\n    console.log('   imageData:', imageData);\n    console.log('   imageType:', imageType);\n    const url = generateImageUrl(imageData, imageType);\n    console.log('🖼️ Setting image URL:', url);\n    setCurrentImageUrl(url);\n    setHasError(false);\n    setIsLoading(!!url);\n    setFallbackAttempts(0);\n\n    // Set a timeout to prevent infinite loading\n    if (url) {\n      const timeout = setTimeout(() => {\n        console.warn('⏰ Image loading timeout reached for URL:', url);\n        setHasError(true);\n        setIsLoading(false);\n      }, 15000);\n      return () => clearTimeout(timeout);\n    } else {\n      console.warn('⚠️ No URL generated, setting error state');\n      setHasError(true);\n      setIsLoading(false);\n    }\n  }, [imageData, imageType]);\n\n  // CLEAN FALLBACK SYSTEM - SAME LOGIC AS DASHBOARD\n  const handleImageError = e => {\n    console.warn(`❌ Image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\n    setIsLoading(false);\n\n    // Simple fallback system like Dashboard\n    if (fallbackAttempts === 0) {\n      // Try direct S3 URL if we have the full URL field\n      const fullUrlField = `${imageType}_image_full_url`;\n      if (imageData[fullUrlField]) {\n        console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\n        setCurrentImageUrl(imageData[fullUrlField]);\n        setFallbackAttempts(1);\n        return;\n      }\n\n      // Try GridFS if S3 failed\n      const gridfsIdField = `${imageType}_image_id`;\n      if (imageData[gridfsIdField]) {\n        console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\n        setCurrentImageUrl(`/api/pavement/get-image/${imageData[gridfsIdField]}`);\n        setFallbackAttempts(1);\n        return;\n      }\n\n      // Try alternative S3 proxy with different encoding\n      const s3KeyField = `${imageType}_image_s3_url`;\n      if (imageData[s3KeyField]) {\n        console.log('🔄 Trying alternative S3 proxy encoding');\n        const s3Key = imageData[s3KeyField];\n        const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n        console.log('🔄 Alternative proxy URL:', alternativeUrl);\n        if (alternativeUrl !== currentImageUrl) {\n          setCurrentImageUrl(alternativeUrl);\n          setFallbackAttempts(1);\n          return;\n        }\n      }\n    }\n\n    // All fallbacks exhausted\n    console.log('❌ No fallback URL available');\n    setHasError(true);\n    setIsLoading(false);\n  };\n  const handleImageLoad = () => {\n    console.log('✅ Image loaded successfully:', currentImageUrl);\n    setIsLoading(false);\n    setHasError(false);\n  };\n\n  // Render loading state\n  if (isLoading && currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"defect-image-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          minHeight: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          variant: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2\",\n          children: \"Loading image...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render error state\n  if (hasError || !currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"defect-image-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-4 border rounded bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-muted mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-image fa-3x\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-muted\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Image not available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: (imageData === null || imageData === void 0 ? void 0 : imageData.media_type) === 'video' ? 'Video thumbnail unavailable' : 'Image could not be loaded'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"details\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            className: \"text-muted small\",\n            children: \"Debug Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"text-start small mt-1\",\n            children: JSON.stringify({\n              imageType,\n              fallbackAttempts,\n              currentImageUrl,\n              hasS3FullUrl: !!(imageData !== null && imageData !== void 0 && imageData[`${imageType}_image_full_url`]),\n              hasS3Key: !!(imageData !== null && imageData !== void 0 && imageData[`${imageType}_image_s3_url`]),\n              hasGridfsId: !!(imageData !== null && imageData !== void 0 && imageData[`${imageType}_image_id`]),\n              mediaType: imageData === null || imageData === void 0 ? void 0 : imageData.media_type\n            }, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render successful image\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"defect-image-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: `${defectType} ${imageType} image`,\n      className: \"img-fluid border rounded shadow-sm\",\n      style: {\n        maxHeight: '400px'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-primary fw-bold\",\n        children: (imageData === null || imageData === void 0 ? void 0 : imageData.media_type) === 'video' ? '📹 Video Thumbnail' : imageType === 'original' ? '📷 Original Image' : '🔍 Processed Image'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), fallbackAttempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-warning\",\n          children: \"(Fallback source used)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedDefectImageDisplay, \"PL1c4k1ZSbH8d+OQcD09kxd231s=\");\n_c = EnhancedDefectImageDisplay;\nfunction DefectDetail() {\n  _s2();\n  var _defectData$image$exi, _defectData$image$exi2, _defectData$image$exi3, _defectData$image$exi4, _defectData$image$exi5, _defectData$image$exi6, _defectData$image$met, _defectData$image$met2, _defectData$image$met3, _defectData$image$met4, _defectData$image$met5, _defectData$image$met6, _defectData$image$met7, _defectData$image$met8;\n  const {\n    imageId\n  } = useParams();\n  const [defectData, setDefectData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [imageType, setImageType] = useState('processed'); // 'original' or 'processed'\n\n  useEffect(() => {\n    const fetchDefectDetail = async () => {\n      try {\n        setLoading(true);\n        const response = await axios.get(`/api/pavement/images/${imageId}`);\n        if (response.data.success) {\n          setDefectData(response.data);\n        } else {\n          setError('Failed to load defect details');\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Error fetching defect details:', err);\n        setError(`Error loading defect details: ${err.message}`);\n        setLoading(false);\n      }\n    };\n    if (imageId) {\n      fetchDefectDetail();\n    }\n  }, [imageId]);\n  const toggleImageType = () => {\n    setImageType(prev => prev === 'original' ? 'processed' : 'original');\n  };\n  const getDefectTypeLabel = type => {\n    switch (type) {\n      case 'pothole':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"danger\",\n          children: \"Pothole\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 16\n        }, this);\n      case 'crack':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"warning\",\n          text: \"dark\",\n          children: \"Crack\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 16\n        }, this);\n      case 'kerb':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"primary\",\n          children: \"Kerb\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          children: type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Defect Detail\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard\",\n        className: \"btn btn-outline-primary\",\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-5\",\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        variant: \"primary\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }, this) : defectData ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"shadow-sm mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"bg-primary text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [getDefectTypeLabel(defectData.type), \" - ID: \", imageId]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), defectData.image.media_type !== 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: imageType === 'original' ? 'light' : 'outline-light',\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: toggleImageType,\n                children: \"Original\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: imageType === 'processed' ? 'light' : 'outline-light',\n                size: \"sm\",\n                onClick: toggleImageType,\n                children: \"Processed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              className: \"text-center mb-4\",\n              children: /*#__PURE__*/_jsxDEV(EnhancedDefectImageDisplay, {\n                imageData: defectData.image,\n                imageType: imageType,\n                defectType: defectData.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-bordered\",\n                children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      width: \"40%\",\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Defect Count\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.pothole_count || defectData.image.crack_count || defectData.image.kerb_count || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Date Detected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: formatDate(defectData.image.timestamp)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Reported By\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.username || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Role\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.role || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Coordinates\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.coordinates || 'Not Available'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Media Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.media_type === 'video' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-info\",\n                        children: \"\\uD83D\\uDCF9 Video\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-primary\",\n                        children: \"\\uD83D\\uDCF7 Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 389,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), (defectData.image.exif_data || defectData.image.metadata) && /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: \"\\uD83D\\uDCCA Media Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: /*#__PURE__*/_jsxDEV(Row, {\n                    children: [((_defectData$image$exi = defectData.image.exif_data) === null || _defectData$image$exi === void 0 ? void 0 : _defectData$image$exi.camera_info) && Object.keys(defectData.image.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"\\uD83D\\uDCF7 Camera Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [defectData.image.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              width: \"40%\",\n                              children: \"Make:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 416,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.camera_info.camera_make\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 417,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 415,\n                            columnNumber: 37\n                          }, this), defectData.image.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Model:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 422,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.camera_info.camera_model\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 423,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 421,\n                            columnNumber: 37\n                          }, this), defectData.image.exif_data.camera_info.software && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Software:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 428,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.camera_info.software\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 429,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 427,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 413,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 412,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 29\n                    }, this), ((_defectData$image$exi2 = defectData.image.exif_data) === null || _defectData$image$exi2 === void 0 ? void 0 : _defectData$image$exi2.technical_info) && Object.keys(defectData.image.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-success\",\n                        children: \"\\u2699\\uFE0F Technical Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 440,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [defectData.image.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              width: \"40%\",\n                              children: \"ISO:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 445,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.technical_info.iso\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 446,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 444,\n                            columnNumber: 37\n                          }, this), defectData.image.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Exposure:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 451,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.technical_info.exposure_time\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 452,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 450,\n                            columnNumber: 37\n                          }, this), defectData.image.exif_data.technical_info.focal_length && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Focal Length:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 457,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.technical_info.focal_length\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 458,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 456,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 442,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 29\n                    }, this), ((_defectData$image$exi3 = defectData.image.exif_data) === null || _defectData$image$exi3 === void 0 ? void 0 : _defectData$image$exi3.basic_info) && /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-info\",\n                        children: \"\\uD83D\\uDCD0 Media Properties\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 469,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              width: \"40%\",\n                              children: \"Dimensions:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 473,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [defectData.image.exif_data.basic_info.width, \" \\xD7 \", defectData.image.exif_data.basic_info.height]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 474,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 472,\n                            columnNumber: 35\n                          }, this), defectData.image.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Format:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 478,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.exif_data.basic_info.format\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 479,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 477,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 471,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 29\n                    }, this), ((_defectData$image$exi4 = defectData.image.exif_data) === null || _defectData$image$exi4 === void 0 ? void 0 : _defectData$image$exi4.gps_coordinates) && /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-warning\",\n                        children: \"\\uD83C\\uDF0D GPS Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              width: \"40%\",\n                              children: \"Latitude:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 494,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: (_defectData$image$exi5 = defectData.image.exif_data.gps_coordinates.latitude) === null || _defectData$image$exi5 === void 0 ? void 0 : _defectData$image$exi5.toFixed(6)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 495,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 493,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Longitude:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 498,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: (_defectData$image$exi6 = defectData.image.exif_data.gps_coordinates.longitude) === null || _defectData$image$exi6 === void 0 ? void 0 : _defectData$image$exi6.toFixed(6)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 499,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 497,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 492,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 29\n                    }, this), defectData.image.media_type === 'video' && /*#__PURE__*/_jsxDEV(Col, {\n                      md: 12,\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-danger\",\n                        children: \"\\uD83C\\uDFAC Video Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [defectData.image.video_id && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              width: \"20%\",\n                              children: \"Video ID:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 514,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.video_id\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 515,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 513,\n                            columnNumber: 37\n                          }, this), ((_defectData$image$met = defectData.image.metadata) === null || _defectData$image$met === void 0 ? void 0 : (_defectData$image$met2 = _defectData$image$met.format_info) === null || _defectData$image$met2 === void 0 ? void 0 : _defectData$image$met2.duration) && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Duration:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 520,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [Math.round(defectData.image.metadata.format_info.duration), \"s\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 521,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 519,\n                            columnNumber: 37\n                          }, this), ((_defectData$image$met3 = defectData.image.metadata) === null || _defectData$image$met3 === void 0 ? void 0 : (_defectData$image$met4 = _defectData$image$met3.basic_info) === null || _defectData$image$met4 === void 0 ? void 0 : _defectData$image$met4.width) && ((_defectData$image$met5 = defectData.image.metadata) === null || _defectData$image$met5 === void 0 ? void 0 : (_defectData$image$met6 = _defectData$image$met5.basic_info) === null || _defectData$image$met6 === void 0 ? void 0 : _defectData$image$met6.height) && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Resolution:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 526,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [defectData.image.metadata.basic_info.width, \"x\", defectData.image.metadata.basic_info.height]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 527,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 525,\n                            columnNumber: 37\n                          }, this), ((_defectData$image$met7 = defectData.image.metadata) === null || _defectData$image$met7 === void 0 ? void 0 : (_defectData$image$met8 = _defectData$image$met7.format_info) === null || _defectData$image$met8 === void 0 ? void 0 : _defectData$image$met8.format_name) && /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Format:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 532,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: defectData.image.metadata.format_info.format_name.toUpperCase()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 533,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 531,\n                            columnNumber: 37\n                          }, this), defectData.image.model_outputs && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [defectData.image.model_outputs.potholes && defectData.image.model_outputs.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"tr\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                                children: \"Potholes Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 541,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                children: defectData.image.model_outputs.potholes.length\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 542,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 540,\n                              columnNumber: 41\n                            }, this), defectData.image.model_outputs.cracks && defectData.image.model_outputs.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"tr\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                                children: \"Cracks Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 547,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                children: defectData.image.model_outputs.cracks.length\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 548,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 546,\n                              columnNumber: 41\n                            }, this), defectData.image.model_outputs.kerbs && defectData.image.model_outputs.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"tr\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                                children: \"Kerbs Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 553,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                children: defectData.image.model_outputs.kerbs.length\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 554,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 552,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 511,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 510,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 17\n          }, this), defectData.type === 'pothole' && defectData.image.potholes && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Pothole Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-striped table-bordered\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-primary\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Area (cm\\xB2)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Depth (cm)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Volume (cm\\xB3)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Severity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: defectData.image.potholes.map((pothole, index) => {\n                    var _pothole$area_cm, _pothole$depth_cm, _pothole$volume;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: pothole.pothole_id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$area_cm = pothole.area_cm2) === null || _pothole$area_cm === void 0 ? void 0 : _pothole$area_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$depth_cm = pothole.depth_cm) === null || _pothole$depth_cm === void 0 ? void 0 : _pothole$depth_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$volume = pothole.volume) === null || _pothole$volume === void 0 ? void 0 : _pothole$volume.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: pothole.area_cm2 > 1000 ? /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"danger\",\n                          children: \"High\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 593,\n                          columnNumber: 33\n                        }, this) : pothole.area_cm2 > 500 ? /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"warning\",\n                          text: \"dark\",\n                          children: \"Medium\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 595,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          children: \"Low\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 597,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 17\n          }, this), defectData.type === 'crack' && defectData.image.cracks && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Crack Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-striped table-bordered\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-primary\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Area (cm\\xB2)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Confidence\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: defectData.image.cracks.map((crack, index) => {\n                    var _crack$area_cm;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: crack.crack_id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 624,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: crack.crack_type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 625,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_crack$area_cm = crack.area_cm2) === null || _crack$area_cm === void 0 ? void 0 : _crack$area_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [(crack.confidence * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 19\n            }, this), defectData.image.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Crack Type Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap\",\n                children: Object.entries(defectData.image.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"info\",\n                    className: \"me-1\",\n                    children: count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 29\n                  }, this), \" \", type]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 17\n          }, this), defectData.type === 'kerb' && defectData.image.kerbs && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Kerb Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-striped table-bordered\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-primary\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 656,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Condition\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Length (m)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: defectData.image.kerbs.map((kerb, index) => {\n                    var _kerb$length_m;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: kerb.kerb_id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: kerb.kerb_type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: kerb.condition === 'Good' ? 'success' : kerb.condition === 'Fair' ? 'warning' : 'danger',\n                          text: kerb.condition === 'Fair' ? 'dark' : undefined,\n                          children: kerb.condition\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_kerb$length_m = kerb.length_m) === null || _kerb$length_m === void 0 ? void 0 : _kerb$length_m.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this), defectData.image.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Condition Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap\",\n                children: Object.entries(defectData.image.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Badge, {\n                    bg: condition === 'Good' ? 'success' : condition === 'Fair' ? 'warning' : 'danger',\n                    text: condition === 'Fair' ? 'dark' : undefined,\n                    className: \"me-1\",\n                    children: count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 29\n                  }, this), \" \", condition]\n                }, condition, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"bg-light\",\n              children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-0\",\n                  children: \"Recommended Action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Based on the defect analysis, the following action is recommended:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 21\n                }, this), defectData.type === 'pothole' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Clean out loose material\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Apply tack coat\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Fill with hot mix asphalt\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Compact thoroughly\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 28\n                    }, this), \" \", defectData.image.potholes && defectData.image.potholes.length > 0 && defectData.image.potholes.some(p => p.area_cm2 > 1000) ? 'High' : defectData.image.potholes && defectData.image.potholes.length > 0 && defectData.image.potholes.some(p => p.area_cm2 > 500) ? 'Medium' : 'Low']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 23\n                }, this), defectData.type === 'crack' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Clean cracks with compressed air\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Apply appropriate crack sealant\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 27\n                    }, this), defectData.image.type_counts && defectData.image.type_counts['Alligator Crack'] > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Consider section replacement for alligator crack areas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 28\n                    }, this), \" \", defectData.image.type_counts && defectData.image.type_counts['Alligator Crack'] > 0 ? 'High' : 'Medium']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 23\n                }, this), defectData.type === 'kerb' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Repair damaged sections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Realign displaced kerbs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Replace severely damaged kerbs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 758,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 760,\n                      columnNumber: 28\n                    }, this), \" \", defectData.image.condition_counts && defectData.image.condition_counts['Poor'] > 0 ? 'High' : defectData.image.condition_counts['Fair'] > 0 ? 'Medium' : 'Low']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          children: \"Generate Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"warning\",\n      children: [\"No defect data found for ID: \", imageId]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n}\n_s2(DefectDetail, \"kQjyLq30HWoqEhsuowi/BrDiCFw=\", false, function () {\n  return [useParams];\n});\n_c2 = DefectDetail;\nexport default DefectDetail;\nvar _c, _c2;\n$RefreshReg$(_c, \"EnhancedDefectImageDisplay\");\n$RefreshReg$(_c2, \"DefectDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON>", "Badge", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EnhancedDefectImageDisplay", "imageData", "imageType", "defectType", "_s", "currentImageUrl", "setCurrentImageUrl", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "isLoading", "setIsLoading", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "generateImageUrl", "data", "type", "console", "log", "imageId", "image_id", "availableFields", "Object", "keys", "filter", "key", "includes", "media_type", "representative_frame", "s3KeyField", "s3Key", "<PERSON><PERSON><PERSON>", "encodeURIComponent", "proxyUrl", "fullUrlField", "urlParts", "split", "bucketIndex", "findIndex", "part", "length", "slice", "join", "gridfsIdField", "url", "timeout", "setTimeout", "warn", "clearTimeout", "handleImageError", "e", "alternativeUrl", "handleImageLoad", "className", "children", "style", "minHeight", "animation", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "process", "env", "NODE_ENV", "JSON", "stringify", "hasS3FullUrl", "hasS3Key", "hasGridfsId", "mediaType", "src", "alt", "maxHeight", "onError", "onLoad", "_c", "DefectDetail", "_s2", "_defectData$image$exi", "_defectData$image$exi2", "_defectData$image$exi3", "_defectData$image$exi4", "_defectData$image$exi5", "_defectData$image$exi6", "_defectData$image$met", "_defectData$image$met2", "_defectData$image$met3", "_defectData$image$met4", "_defectData$image$met5", "_defectData$image$met6", "_defectData$image$met7", "_defectData$image$met8", "defectData", "setDefectData", "loading", "setLoading", "error", "setError", "setImageType", "fetchDefectDetail", "response", "get", "success", "err", "message", "toggleImageType", "prev", "getDefectTypeLabel", "bg", "text", "formatDate", "dateString", "Date", "toLocaleString", "to", "role", "Header", "image", "size", "onClick", "Body", "md", "width", "pothole_count", "crack_count", "kerb_count", "timestamp", "username", "coordinates", "exif_data", "metadata", "camera_info", "camera_make", "camera_model", "software", "technical_info", "iso", "exposure_time", "focal_length", "basic_info", "height", "format", "gps_coordinates", "latitude", "toFixed", "longitude", "video_id", "format_info", "duration", "Math", "round", "format_name", "toUpperCase", "model_outputs", "potholes", "cracks", "kerbs", "map", "pothole", "index", "_pothole$area_cm", "_pothole$depth_cm", "_pothole$volume", "pothole_id", "area_cm2", "depth_cm", "volume", "crack", "_crack$area_cm", "crack_id", "crack_type", "confidence", "type_counts", "entries", "count", "kerb", "_kerb$length_m", "kerb_id", "kerb_type", "condition", "undefined", "length_m", "condition_counts", "some", "p", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/DefectDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport './dashboard.css';\r\n\r\n/**\r\n * Enhanced Image Display Component for Defect Details\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\r\nconst EnhancedDefectImageDisplay = ({ imageData, imageType, defectType }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n\r\n  // CLEAN IMAGE URL GENERATION - SAME LOGIC AS \"ALL UPLOADED IMAGES\" SECTION\r\n  const generateImageUrl = (data, type) => {\r\n    if (!data) {\r\n      console.log('❌ No data provided to generateImageUrl');\r\n      return null;\r\n    }\r\n\r\n    console.log('🔍 Generating image URL for:', {\r\n      type,\r\n      imageId: data.image_id,\r\n      availableFields: Object.keys(data).filter(key => key.includes('image'))\r\n    });\r\n\r\n    // Check if this is video data with representative frame\r\n    if (data.media_type === 'video' && data.representative_frame) {\r\n      console.log('📹 Using representative frame for video');\r\n      return `data:image/jpeg;base64,${data.representative_frame}`;\r\n    }\r\n\r\n    // Priority 1: Try S3 key with proxy endpoint (same as Dashboard) - MOST RELIABLE\r\n    const s3KeyField = `${type}_image_s3_url`;\r\n    if (data[s3KeyField]) {\r\n      console.log('🔗 PRIORITY 1: Using S3 key field:', s3KeyField, '=', data[s3KeyField]);\r\n      const s3Key = data[s3KeyField];\r\n\r\n      // FIXED: Use proper encoding - encode the entire S3 key as one unit\r\n      const encodedKey = encodeURIComponent(s3Key);\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\r\n\r\n      console.log('✅ Generated proxy URL from S3 key (FIXED encoding):', proxyUrl);\r\n      console.log('   Original S3 key:', s3Key);\r\n      console.log('   Encoded S3 key:', encodedKey);\r\n\r\n      return proxyUrl;\r\n    }\r\n\r\n    // Priority 2: Try S3 full URL with proxy extraction (same as Dashboard)\r\n    const fullUrlField = `${type}_image_full_url`;\r\n    if (data[fullUrlField]) {\r\n      console.log('🔗 PRIORITY 2: Using full URL field:', fullUrlField, '=', data[fullUrlField]);\r\n      // Extract S3 key from full URL and use proxy endpoint\r\n      const urlParts = data[fullUrlField].split('/');\r\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n        // FIXED: Use proper encoding - encode the entire S3 key as one unit\r\n        const encodedKey = encodeURIComponent(s3Key);\r\n        const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\r\n        console.log('✅ Generated proxy URL from full URL (FIXED encoding):', proxyUrl);\r\n        console.log('   Extracted S3 key:', s3Key);\r\n        console.log('   Encoded S3 key:', encodedKey);\r\n        return proxyUrl;\r\n      }\r\n    }\r\n\r\n    // Priority 3: Try GridFS endpoint (legacy support)\r\n    const gridfsIdField = `${type}_image_id`;\r\n    if (data[gridfsIdField]) {\r\n      console.log('🗄️ PRIORITY 3: Using GridFS endpoint:', gridfsIdField, '=', data[gridfsIdField]);\r\n      return `/api/pavement/get-image/${data[gridfsIdField]}`;\r\n    }\r\n\r\n    console.log('❌ No valid image URL found for type:', type);\r\n    console.log('❌ Available data fields:', Object.keys(data));\r\n    return null;\r\n  };\r\n\r\n  // Initialize image URL - Clean approach like Dashboard\r\n  useEffect(() => {\r\n    console.log('🔄 DefectDetail Image Component - useEffect triggered');\r\n    console.log('   imageData:', imageData);\r\n    console.log('   imageType:', imageType);\r\n\r\n    const url = generateImageUrl(imageData, imageType);\r\n    console.log('🖼️ Setting image URL:', url);\r\n\r\n    setCurrentImageUrl(url);\r\n    setHasError(false);\r\n    setIsLoading(!!url);\r\n    setFallbackAttempts(0);\r\n\r\n    // Set a timeout to prevent infinite loading\r\n    if (url) {\r\n      const timeout = setTimeout(() => {\r\n        console.warn('⏰ Image loading timeout reached for URL:', url);\r\n        setHasError(true);\r\n        setIsLoading(false);\r\n      }, 15000);\r\n\r\n      return () => clearTimeout(timeout);\r\n    } else {\r\n      console.warn('⚠️ No URL generated, setting error state');\r\n      setHasError(true);\r\n      setIsLoading(false);\r\n    }\r\n  }, [imageData, imageType]);\r\n\r\n  // CLEAN FALLBACK SYSTEM - SAME LOGIC AS DASHBOARD\r\n  const handleImageError = (e) => {\r\n    console.warn(`❌ Image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\r\n    setIsLoading(false);\r\n\r\n    // Simple fallback system like Dashboard\r\n    if (fallbackAttempts === 0) {\r\n      // Try direct S3 URL if we have the full URL field\r\n      const fullUrlField = `${imageType}_image_full_url`;\r\n      if (imageData[fullUrlField]) {\r\n        console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\r\n        setCurrentImageUrl(imageData[fullUrlField]);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n\r\n      // Try GridFS if S3 failed\r\n      const gridfsIdField = `${imageType}_image_id`;\r\n      if (imageData[gridfsIdField]) {\r\n        console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\r\n        setCurrentImageUrl(`/api/pavement/get-image/${imageData[gridfsIdField]}`);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n\r\n      // Try alternative S3 proxy with different encoding\r\n      const s3KeyField = `${imageType}_image_s3_url`;\r\n      if (imageData[s3KeyField]) {\r\n        console.log('🔄 Trying alternative S3 proxy encoding');\r\n        const s3Key = imageData[s3KeyField];\r\n        const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n        console.log('🔄 Alternative proxy URL:', alternativeUrl);\r\n        if (alternativeUrl !== currentImageUrl) {\r\n          setCurrentImageUrl(alternativeUrl);\r\n          setFallbackAttempts(1);\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    // All fallbacks exhausted\r\n    console.log('❌ No fallback URL available');\r\n    setHasError(true);\r\n    setIsLoading(false);\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    console.log('✅ Image loaded successfully:', currentImageUrl);\r\n    setIsLoading(false);\r\n    setHasError(false);\r\n  };\r\n\r\n  // Render loading state\r\n  if (isLoading && currentImageUrl) {\r\n    return (\r\n      <div className=\"defect-image-container\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '200px' }}>\r\n          <Spinner animation=\"border\" variant=\"primary\" />\r\n          <span className=\"ms-2\">Loading image...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (hasError || !currentImageUrl) {\r\n    return (\r\n      <div className=\"defect-image-container\">\r\n        <div className=\"text-center p-4 border rounded bg-light\">\r\n          <div className=\"text-muted mb-2\">\r\n            <i className=\"fas fa-image fa-3x\"></i>\r\n          </div>\r\n          <div className=\"text-muted\">\r\n            <strong>Image not available</strong>\r\n          </div>\r\n          <small className=\"text-muted\">\r\n            {imageData?.media_type === 'video' ? 'Video thumbnail unavailable' : 'Image could not be loaded'}\r\n          </small>\r\n          {/* Simple Debug Information - Only in Development */}\r\n          {process.env.NODE_ENV === 'development' && (\r\n            <details className=\"mt-2\">\r\n              <summary className=\"text-muted small\">Debug Info</summary>\r\n              <pre className=\"text-start small mt-1\">\r\n                {JSON.stringify({\r\n                  imageType,\r\n                  fallbackAttempts,\r\n                  currentImageUrl,\r\n                  hasS3FullUrl: !!imageData?.[`${imageType}_image_full_url`],\r\n                  hasS3Key: !!imageData?.[`${imageType}_image_s3_url`],\r\n                  hasGridfsId: !!imageData?.[`${imageType}_image_id`],\r\n                  mediaType: imageData?.media_type\r\n                }, null, 2)}\r\n              </pre>\r\n            </details>\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render successful image\r\n  return (\r\n    <div className=\"defect-image-container\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt={`${defectType} ${imageType} image`}\r\n        className=\"img-fluid border rounded shadow-sm\"\r\n        style={{ maxHeight: '400px' }}\r\n        onError={handleImageError}\r\n        onLoad={handleImageLoad}\r\n      />\r\n      <div className=\"mt-2\">\r\n        <small className=\"text-primary fw-bold\">\r\n          {imageData?.media_type === 'video' ? '📹 Video Thumbnail' :\r\n           imageType === 'original' ? '📷 Original Image' : '🔍 Processed Image'}\r\n        </small>\r\n        {fallbackAttempts > 0 && (\r\n          <div>\r\n            <small className=\"text-warning\">\r\n              (Fallback source used)\r\n            </small>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction DefectDetail() {\r\n  const { imageId } = useParams();\r\n  const [defectData, setDefectData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [imageType, setImageType] = useState('processed'); // 'original' or 'processed'\r\n\r\n  useEffect(() => {\r\n    const fetchDefectDetail = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await axios.get(`/api/pavement/images/${imageId}`);\r\n        \r\n        if (response.data.success) {\r\n          setDefectData(response.data);\r\n        } else {\r\n          setError('Failed to load defect details');\r\n        }\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Error fetching defect details:', err);\r\n        setError(`Error loading defect details: ${err.message}`);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (imageId) {\r\n      fetchDefectDetail();\r\n    }\r\n  }, [imageId]);\r\n\r\n  const toggleImageType = () => {\r\n    setImageType(prev => prev === 'original' ? 'processed' : 'original');\r\n  };\r\n\r\n  const getDefectTypeLabel = (type) => {\r\n    switch (type) {\r\n      case 'pothole':\r\n        return <Badge bg=\"danger\">Pothole</Badge>;\r\n      case 'crack':\r\n        return <Badge bg=\"warning\" text=\"dark\">Crack</Badge>;\r\n      case 'kerb':\r\n        return <Badge bg=\"primary\">Kerb</Badge>;\r\n      default:\r\n        return <Badge bg=\"secondary\">{type}</Badge>;\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return 'N/A';\r\n    return new Date(dateString).toLocaleString();\r\n  };\r\n\r\n  return (\r\n    <Container className=\"py-4\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h2>Defect Detail</h2>\r\n        <Link to=\"/dashboard\" className=\"btn btn-outline-primary\">\r\n          Back to Dashboard\r\n        </Link>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"text-center py-5\">\r\n          <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </Spinner>\r\n        </div>\r\n      ) : error ? (\r\n        <Alert variant=\"danger\">{error}</Alert>\r\n      ) : defectData ? (\r\n        <>\r\n          <Card className=\"shadow-sm mb-4\">\r\n            <Card.Header className=\"bg-primary text-white\">\r\n              <div className=\"d-flex justify-content-between align-items-center\">\r\n                <h5 className=\"mb-0\">\r\n                  {getDefectTypeLabel(defectData.type)} - ID: {imageId}\r\n                </h5>\r\n                {/* Only show Original/Processed buttons for non-video entries */}\r\n                {defectData.image.media_type !== 'video' && (\r\n                  <div>\r\n                    <Button\r\n                      variant={imageType === 'original' ? 'light' : 'outline-light'}\r\n                      size=\"sm\"\r\n                      className=\"me-2\"\r\n                      onClick={toggleImageType}\r\n                    >\r\n                      Original\r\n                    </Button>\r\n                    <Button\r\n                      variant={imageType === 'processed' ? 'light' : 'outline-light'}\r\n                      size=\"sm\"\r\n                      onClick={toggleImageType}\r\n                    >\r\n                      Processed\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col md={6} className=\"text-center mb-4\">\r\n                  <EnhancedDefectImageDisplay\r\n                    imageData={defectData.image}\r\n                    imageType={imageType}\r\n                    defectType={defectData.type}\r\n                  />\r\n                </Col>\r\n                <Col md={6}>\r\n                  <h5>Basic Information</h5>\r\n                  <table className=\"table table-bordered\">\r\n                    <tbody>\r\n                      <tr>\r\n                        <th width=\"40%\">Type</th>\r\n                        <td>{defectData.type}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Defect Count</th>\r\n                        <td>\r\n                          {defectData.image.pothole_count || \r\n                           defectData.image.crack_count || \r\n                           defectData.image.kerb_count || 'N/A'}\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Date Detected</th>\r\n                        <td>{formatDate(defectData.image.timestamp)}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Reported By</th>\r\n                        <td>{defectData.image.username || 'N/A'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Role</th>\r\n                        <td>{defectData.image.role || 'N/A'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Coordinates</th>\r\n                        <td>{defectData.image.coordinates || 'Not Available'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Media Type</th>\r\n                        <td>\r\n                          {defectData.image.media_type === 'video' ? (\r\n                            <span className=\"text-info\">📹 Video</span>\r\n                          ) : (\r\n                            <span className=\"text-primary\">📷 Image</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* EXIF and Metadata Information */}\r\n              {(defectData.image.exif_data || defectData.image.metadata) && (\r\n                <Row className=\"mt-4\">\r\n                  <Col>\r\n                    <Card>\r\n                      <Card.Header>\r\n                        <h5 className=\"mb-0\">📊 Media Information</h5>\r\n                      </Card.Header>\r\n                      <Card.Body>\r\n                        <Row>\r\n                          {/* Camera Information */}\r\n                          {defectData.image.exif_data?.camera_info && Object.keys(defectData.image.exif_data.camera_info).length > 0 && (\r\n                            <Col md={6} className=\"mb-3\">\r\n                              <h6 className=\"text-primary\">📷 Camera Information</h6>\r\n                              <table className=\"table table-sm\">\r\n                                <tbody>\r\n                                  {defectData.image.exif_data.camera_info.camera_make && (\r\n                                    <tr>\r\n                                      <th width=\"40%\">Make:</th>\r\n                                      <td>{defectData.image.exif_data.camera_info.camera_make}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.exif_data.camera_info.camera_model && (\r\n                                    <tr>\r\n                                      <th>Model:</th>\r\n                                      <td>{defectData.image.exif_data.camera_info.camera_model}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.exif_data.camera_info.software && (\r\n                                    <tr>\r\n                                      <th>Software:</th>\r\n                                      <td>{defectData.image.exif_data.camera_info.software}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                </tbody>\r\n                              </table>\r\n                            </Col>\r\n                          )}\r\n\r\n                          {/* Technical Information */}\r\n                          {defectData.image.exif_data?.technical_info && Object.keys(defectData.image.exif_data.technical_info).length > 0 && (\r\n                            <Col md={6} className=\"mb-3\">\r\n                              <h6 className=\"text-success\">⚙️ Technical Details</h6>\r\n                              <table className=\"table table-sm\">\r\n                                <tbody>\r\n                                  {defectData.image.exif_data.technical_info.iso && (\r\n                                    <tr>\r\n                                      <th width=\"40%\">ISO:</th>\r\n                                      <td>{defectData.image.exif_data.technical_info.iso}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.exif_data.technical_info.exposure_time && (\r\n                                    <tr>\r\n                                      <th>Exposure:</th>\r\n                                      <td>{defectData.image.exif_data.technical_info.exposure_time}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.exif_data.technical_info.focal_length && (\r\n                                    <tr>\r\n                                      <th>Focal Length:</th>\r\n                                      <td>{defectData.image.exif_data.technical_info.focal_length}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                </tbody>\r\n                              </table>\r\n                            </Col>\r\n                          )}\r\n\r\n                          {/* Basic Media Info */}\r\n                          {defectData.image.exif_data?.basic_info && (\r\n                            <Col md={6} className=\"mb-3\">\r\n                              <h6 className=\"text-info\">📐 Media Properties</h6>\r\n                              <table className=\"table table-sm\">\r\n                                <tbody>\r\n                                  <tr>\r\n                                    <th width=\"40%\">Dimensions:</th>\r\n                                    <td>{defectData.image.exif_data.basic_info.width} × {defectData.image.exif_data.basic_info.height}</td>\r\n                                  </tr>\r\n                                  {defectData.image.exif_data.basic_info.format && (\r\n                                    <tr>\r\n                                      <th>Format:</th>\r\n                                      <td>{defectData.image.exif_data.basic_info.format}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                </tbody>\r\n                              </table>\r\n                            </Col>\r\n                          )}\r\n\r\n                          {/* GPS Information */}\r\n                          {defectData.image.exif_data?.gps_coordinates && (\r\n                            <Col md={6} className=\"mb-3\">\r\n                              <h6 className=\"text-warning\">🌍 GPS Information</h6>\r\n                              <table className=\"table table-sm\">\r\n                                <tbody>\r\n                                  <tr>\r\n                                    <th width=\"40%\">Latitude:</th>\r\n                                    <td>{defectData.image.exif_data.gps_coordinates.latitude?.toFixed(6)}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <th>Longitude:</th>\r\n                                    <td>{defectData.image.exif_data.gps_coordinates.longitude?.toFixed(6)}</td>\r\n                                  </tr>\r\n                                </tbody>\r\n                              </table>\r\n                            </Col>\r\n                          )}\r\n\r\n                          {/* Video-specific Information */}\r\n                          {defectData.image.media_type === 'video' && (\r\n                            <Col md={12} className=\"mb-3\">\r\n                              <h6 className=\"text-danger\">🎬 Video Information</h6>\r\n                              <table className=\"table table-sm\">\r\n                                <tbody>\r\n                                  {defectData.image.video_id && (\r\n                                    <tr>\r\n                                      <th width=\"20%\">Video ID:</th>\r\n                                      <td>{defectData.image.video_id}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.metadata?.format_info?.duration && (\r\n                                    <tr>\r\n                                      <th>Duration:</th>\r\n                                      <td>{Math.round(defectData.image.metadata.format_info.duration)}s</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.metadata?.basic_info?.width && defectData.image.metadata?.basic_info?.height && (\r\n                                    <tr>\r\n                                      <th>Resolution:</th>\r\n                                      <td>{defectData.image.metadata.basic_info.width}x{defectData.image.metadata.basic_info.height}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {defectData.image.metadata?.format_info?.format_name && (\r\n                                    <tr>\r\n                                      <th>Format:</th>\r\n                                      <td>{defectData.image.metadata.format_info.format_name.toUpperCase()}</td>\r\n                                    </tr>\r\n                                  )}\r\n                                  {/* Show detection counts for videos */}\r\n                                  {defectData.image.model_outputs && (\r\n                                    <>\r\n                                      {defectData.image.model_outputs.potholes && defectData.image.model_outputs.potholes.length > 0 && (\r\n                                        <tr>\r\n                                          <th>Potholes Detected:</th>\r\n                                          <td>{defectData.image.model_outputs.potholes.length}</td>\r\n                                        </tr>\r\n                                      )}\r\n                                      {defectData.image.model_outputs.cracks && defectData.image.model_outputs.cracks.length > 0 && (\r\n                                        <tr>\r\n                                          <th>Cracks Detected:</th>\r\n                                          <td>{defectData.image.model_outputs.cracks.length}</td>\r\n                                        </tr>\r\n                                      )}\r\n                                      {defectData.image.model_outputs.kerbs && defectData.image.model_outputs.kerbs.length > 0 && (\r\n                                        <tr>\r\n                                          <th>Kerbs Detected:</th>\r\n                                          <td>{defectData.image.model_outputs.kerbs.length}</td>\r\n                                        </tr>\r\n                                      )}\r\n                                    </>\r\n                                  )}\r\n                                </tbody>\r\n                              </table>\r\n                            </Col>\r\n                          )}\r\n                        </Row>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  </Col>\r\n                </Row>\r\n              )}\r\n\r\n              {defectData.type === 'pothole' && defectData.image.potholes && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Pothole Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Area (cm²)</th>\r\n                          <th>Depth (cm)</th>\r\n                          <th>Volume (cm³)</th>\r\n                          <th>Severity</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.potholes.map((pothole, index) => (\r\n                          <tr key={index}>\r\n                            <td>{pothole.pothole_id}</td>\r\n                            <td>{pothole.area_cm2?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.depth_cm?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.volume?.toFixed(2) || 'N/A'}</td>\r\n                            <td>\r\n                              {pothole.area_cm2 > 1000 ? (\r\n                                <Badge bg=\"danger\">High</Badge>\r\n                              ) : pothole.area_cm2 > 500 ? (\r\n                                <Badge bg=\"warning\" text=\"dark\">Medium</Badge>\r\n                              ) : (\r\n                                <Badge bg=\"success\">Low</Badge>\r\n                              )}\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {defectData.type === 'crack' && defectData.image.cracks && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Crack Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Type</th>\r\n                          <th>Area (cm²)</th>\r\n                          <th>Confidence</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.cracks.map((crack, index) => (\r\n                          <tr key={index}>\r\n                            <td>{crack.crack_id}</td>\r\n                            <td>{crack.crack_type}</td>\r\n                            <td>{crack.area_cm2?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{(crack.confidence * 100).toFixed(1)}%</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                  \r\n                  {defectData.image.type_counts && (\r\n                    <div className=\"mt-3\">\r\n                      <h6>Crack Type Distribution</h6>\r\n                      <div className=\"d-flex flex-wrap\">\r\n                        {Object.entries(defectData.image.type_counts).map(([type, count]) => (\r\n                          <div key={type} className=\"me-3 mb-2\">\r\n                            <Badge bg=\"info\" className=\"me-1\">{count}</Badge> {type}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {defectData.type === 'kerb' && defectData.image.kerbs && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Kerb Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Type</th>\r\n                          <th>Condition</th>\r\n                          <th>Length (m)</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.kerbs.map((kerb, index) => (\r\n                          <tr key={index}>\r\n                            <td>{kerb.kerb_id}</td>\r\n                            <td>{kerb.kerb_type}</td>\r\n                            <td>\r\n                              <Badge \r\n                                bg={\r\n                                  kerb.condition === 'Good' ? 'success' :\r\n                                  kerb.condition === 'Fair' ? 'warning' : 'danger'\r\n                                }\r\n                                text={kerb.condition === 'Fair' ? 'dark' : undefined}\r\n                              >\r\n                                {kerb.condition}\r\n                              </Badge>\r\n                            </td>\r\n                            <td>{kerb.length_m?.toFixed(2) || 'N/A'}</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                  \r\n                  {defectData.image.condition_counts && (\r\n                    <div className=\"mt-3\">\r\n                      <h6>Condition Distribution</h6>\r\n                      <div className=\"d-flex flex-wrap\">\r\n                        {Object.entries(defectData.image.condition_counts).map(([condition, count]) => (\r\n                          <div key={condition} className=\"me-3 mb-2\">\r\n                            <Badge \r\n                              bg={\r\n                                condition === 'Good' ? 'success' :\r\n                                condition === 'Fair' ? 'warning' : 'danger'\r\n                              }\r\n                              text={condition === 'Fair' ? 'dark' : undefined}\r\n                              className=\"me-1\"\r\n                            >\r\n                              {count}\r\n                            </Badge> {condition}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Recommendation section - if available */}\r\n              <div className=\"mt-4\">\r\n                <Card className=\"bg-light\">\r\n                  <Card.Header>\r\n                    <h5 className=\"mb-0\">Recommended Action</h5>\r\n                  </Card.Header>\r\n                  <Card.Body>\r\n                    <p>Based on the defect analysis, the following action is recommended:</p>\r\n                    {defectData.type === 'pothole' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Clean out loose material</li>\r\n                          <li>Apply tack coat</li>\r\n                          <li>Fill with hot mix asphalt</li>\r\n                          <li>Compact thoroughly</li>\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.potholes && defectData.image.potholes.length > 0 && \r\n                          defectData.image.potholes.some(p => p.area_cm2 > 1000) ? \r\n                          'High' : defectData.image.potholes && defectData.image.potholes.length > 0 && \r\n                          defectData.image.potholes.some(p => p.area_cm2 > 500) ? \r\n                          'Medium' : 'Low'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {defectData.type === 'crack' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Clean cracks with compressed air</li>\r\n                          <li>Apply appropriate crack sealant</li>\r\n                          {defectData.image.type_counts && \r\n                           defectData.image.type_counts['Alligator Crack'] > 0 && (\r\n                            <li>Consider section replacement for alligator crack areas</li>\r\n                          )}\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.type_counts && \r\n                          defectData.image.type_counts['Alligator Crack'] > 0 ? \r\n                          'High' : 'Medium'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {defectData.type === 'kerb' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Repair damaged sections</li>\r\n                          <li>Realign displaced kerbs</li>\r\n                          <li>Replace severely damaged kerbs</li>\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.condition_counts && \r\n                          defectData.image.condition_counts['Poor'] > 0 ? \r\n                          'High' : defectData.image.condition_counts['Fair'] > 0 ? \r\n                          'Medium' : 'Low'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n          <div className=\"d-flex justify-content-end mt-4\">\r\n            <Button variant=\"primary\">\r\n              Generate Report\r\n            </Button>\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <Alert variant=\"warning\">No defect data found for ID: {imageId}</Alert>\r\n      )}\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default DefectDetail; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;;AAExB;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIA,MAAMC,0BAA0B,GAAGA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAM8B,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACvC,IAAI,CAACD,IAAI,EAAE;MACTE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,OAAO,IAAI;IACb;IAEAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CF,IAAI;MACJG,OAAO,EAAEJ,IAAI,CAACK,QAAQ;MACtBC,eAAe,EAAEC,MAAM,CAACC,IAAI,CAACR,IAAI,CAAC,CAACS,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC;IACxE,CAAC,CAAC;;IAEF;IACA,IAAIX,IAAI,CAACY,UAAU,KAAK,OAAO,IAAIZ,IAAI,CAACa,oBAAoB,EAAE;MAC5DX,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,OAAO,0BAA0BH,IAAI,CAACa,oBAAoB,EAAE;IAC9D;;IAEA;IACA,MAAMC,UAAU,GAAG,GAAGb,IAAI,eAAe;IACzC,IAAID,IAAI,CAACc,UAAU,CAAC,EAAE;MACpBZ,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEW,UAAU,EAAE,GAAG,EAAEd,IAAI,CAACc,UAAU,CAAC,CAAC;MACpF,MAAMC,KAAK,GAAGf,IAAI,CAACc,UAAU,CAAC;;MAE9B;MACA,MAAME,UAAU,GAAGC,kBAAkB,CAACF,KAAK,CAAC;MAC5C,MAAMG,QAAQ,GAAG,8BAA8BF,UAAU,EAAE;MAE3Dd,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEe,QAAQ,CAAC;MAC5EhB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEY,KAAK,CAAC;MACzCb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,UAAU,CAAC;MAE7C,OAAOE,QAAQ;IACjB;;IAEA;IACA,MAAMC,YAAY,GAAG,GAAGlB,IAAI,iBAAiB;IAC7C,IAAID,IAAI,CAACmB,YAAY,CAAC,EAAE;MACtBjB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEgB,YAAY,EAAE,GAAG,EAAEnB,IAAI,CAACmB,YAAY,CAAC,CAAC;MAC1F;MACA,MAAMC,QAAQ,GAAGpB,IAAI,CAACmB,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9C,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACb,QAAQ,CAAC,MAAM,CAAC,CAAC;MACrE,IAAIW,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACK,MAAM,EAAE;QAC3D,MAAMV,KAAK,GAAGK,QAAQ,CAACM,KAAK,CAACJ,WAAW,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;QACvD;QACA,MAAMX,UAAU,GAAGC,kBAAkB,CAACF,KAAK,CAAC;QAC5C,MAAMG,QAAQ,GAAG,8BAA8BF,UAAU,EAAE;QAC3Dd,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEe,QAAQ,CAAC;QAC9EhB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEY,KAAK,CAAC;QAC1Cb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,UAAU,CAAC;QAC7C,OAAOE,QAAQ;MACjB;IACF;;IAEA;IACA,MAAMU,aAAa,GAAG,GAAG3B,IAAI,WAAW;IACxC,IAAID,IAAI,CAAC4B,aAAa,CAAC,EAAE;MACvB1B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEyB,aAAa,EAAE,GAAG,EAAE5B,IAAI,CAAC4B,aAAa,CAAC,CAAC;MAC9F,OAAO,2BAA2B5B,IAAI,CAAC4B,aAAa,CAAC,EAAE;IACzD;IAEA1B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEF,IAAI,CAAC;IACzDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,MAAM,CAACC,IAAI,CAACR,IAAI,CAAC,CAAC;IAC1D,OAAO,IAAI;EACb,CAAC;;EAED;EACA9B,SAAS,CAAC,MAAM;IACdgC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,SAAS,CAAC;IACvCe,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEf,SAAS,CAAC;IAEvC,MAAMyC,GAAG,GAAG9B,gBAAgB,CAACZ,SAAS,EAAEC,SAAS,CAAC;IAClDc,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,GAAG,CAAC;IAE1CrC,kBAAkB,CAACqC,GAAG,CAAC;IACvBnC,WAAW,CAAC,KAAK,CAAC;IAClBE,YAAY,CAAC,CAAC,CAACiC,GAAG,CAAC;IACnB/B,mBAAmB,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAI+B,GAAG,EAAE;MACP,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC/B7B,OAAO,CAAC8B,IAAI,CAAC,0CAA0C,EAAEH,GAAG,CAAC;QAC7DnC,WAAW,CAAC,IAAI,CAAC;QACjBE,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,KAAK,CAAC;MAET,OAAO,MAAMqC,YAAY,CAACH,OAAO,CAAC;IACpC,CAAC,MAAM;MACL5B,OAAO,CAAC8B,IAAI,CAAC,0CAA0C,CAAC;MACxDtC,WAAW,CAAC,IAAI,CAAC;MACjBE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACT,SAAS,EAAEC,SAAS,CAAC,CAAC;;EAE1B;EACA,MAAM8C,gBAAgB,GAAIC,CAAC,IAAK;IAC9BjC,OAAO,CAAC8B,IAAI,CAAC,gCAAgCnC,gBAAgB,GAAG,CAAC,IAAI,EAAEN,eAAe,CAAC;IACvFK,YAAY,CAAC,KAAK,CAAC;;IAEnB;IACA,IAAIC,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAMsB,YAAY,GAAG,GAAG/B,SAAS,iBAAiB;MAClD,IAAID,SAAS,CAACgC,YAAY,CAAC,EAAE;QAC3BjB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,SAAS,CAACgC,YAAY,CAAC,CAAC;QAChE3B,kBAAkB,CAACL,SAAS,CAACgC,YAAY,CAAC,CAAC;QAC3CrB,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;;MAEA;MACA,MAAM8B,aAAa,GAAG,GAAGxC,SAAS,WAAW;MAC7C,IAAID,SAAS,CAACyC,aAAa,CAAC,EAAE;QAC5B1B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhB,SAAS,CAACyC,aAAa,CAAC,CAAC;QAC9DpC,kBAAkB,CAAC,2BAA2BL,SAAS,CAACyC,aAAa,CAAC,EAAE,CAAC;QACzE9B,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;;MAEA;MACA,MAAMgB,UAAU,GAAG,GAAG1B,SAAS,eAAe;MAC9C,IAAID,SAAS,CAAC2B,UAAU,CAAC,EAAE;QACzBZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD,MAAMY,KAAK,GAAG5B,SAAS,CAAC2B,UAAU,CAAC;QACnC,MAAMsB,cAAc,GAAG,8BAA8BnB,kBAAkB,CAACF,KAAK,CAAC,EAAE;QAChFb,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiC,cAAc,CAAC;QACxD,IAAIA,cAAc,KAAK7C,eAAe,EAAE;UACtCC,kBAAkB,CAAC4C,cAAc,CAAC;UAClCtC,mBAAmB,CAAC,CAAC,CAAC;UACtB;QACF;MACF;IACF;;IAEA;IACAI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CT,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMyC,eAAe,GAAGA,CAAA,KAAM;IAC5BnC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEZ,eAAe,CAAC;IAC5DK,YAAY,CAAC,KAAK,CAAC;IACnBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAIC,SAAS,IAAIJ,eAAe,EAAE;IAChC,oBACER,OAAA;MAAKuD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCxD,OAAA;QAAKuD,SAAS,EAAC,kDAAkD;QAACE,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAF,QAAA,gBAC9FxD,OAAA,CAACL,OAAO;UAACgE,SAAS,EAAC,QAAQ;UAACC,OAAO,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDhE,OAAA;UAAMuD,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAgB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAItD,QAAQ,IAAI,CAACF,eAAe,EAAE;IAChC,oBACER,OAAA;MAAKuD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCxD,OAAA;QAAKuD,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDxD,OAAA;UAAKuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BxD,OAAA;YAAGuD,SAAS,EAAC;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACNhE,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBxD,OAAA;YAAAwD,QAAA,EAAQ;UAAmB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNhE,OAAA;UAAOuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAC1B,CAAApD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,UAAU,MAAK,OAAO,GAAG,6BAA6B,GAAG;QAA2B;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,EAEPC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCnE,OAAA;UAASuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACvBxD,OAAA;YAASuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC1DhE,OAAA;YAAKuD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACnCY,IAAI,CAACC,SAAS,CAAC;cACdhE,SAAS;cACTS,gBAAgB;cAChBN,eAAe;cACf8D,YAAY,EAAE,CAAC,EAAClE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAG,GAAGC,SAAS,iBAAiB,CAAC;cAC1DkE,QAAQ,EAAE,CAAC,EAACnE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAG,GAAGC,SAAS,eAAe,CAAC;cACpDmE,WAAW,EAAE,CAAC,EAACpE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAG,GAAGC,SAAS,WAAW,CAAC;cACnDoE,SAAS,EAAErE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB;YACxB,CAAC,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEhE,OAAA;IAAKuD,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCxD,OAAA;MACE0E,GAAG,EAAElE,eAAgB;MACrBmE,GAAG,EAAE,GAAGrE,UAAU,IAAID,SAAS,QAAS;MACxCkD,SAAS,EAAC,oCAAoC;MAC9CE,KAAK,EAAE;QAAEmB,SAAS,EAAE;MAAQ,CAAE;MAC9BC,OAAO,EAAE1B,gBAAiB;MAC1B2B,MAAM,EAAExB;IAAgB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFhE,OAAA;MAAKuD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBxD,OAAA;QAAOuD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EACpC,CAAApD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,UAAU,MAAK,OAAO,GAAG,oBAAoB,GACxDxB,SAAS,KAAK,UAAU,GAAG,mBAAmB,GAAG;MAAoB;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,EACPlD,gBAAgB,GAAG,CAAC,iBACnBd,OAAA;QAAAwD,QAAA,eACExD,OAAA;UAAOuD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAEhC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CArOIJ,0BAA0B;AAAA4E,EAAA,GAA1B5E,0BAA0B;AAuOhC,SAAS6E,YAAYA,CAAA,EAAG;EAAAC,GAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtB,MAAM;IAAE1E;EAAQ,CAAC,GAAGjC,SAAS,CAAC,CAAC;EAC/B,MAAM,CAAC4G,UAAU,EAAEC,aAAa,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgH,OAAO,EAAEC,UAAU,CAAC,GAAGjH,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkH,KAAK,EAAEC,QAAQ,CAAC,GAAGnH,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,SAAS,EAAEiG,YAAY,CAAC,GAAGpH,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;;EAEzDC,SAAS,CAAC,MAAM;IACd,MAAMoH,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFJ,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMK,QAAQ,GAAG,MAAM1G,KAAK,CAAC2G,GAAG,CAAC,wBAAwBpF,OAAO,EAAE,CAAC;QAEnE,IAAImF,QAAQ,CAACvF,IAAI,CAACyF,OAAO,EAAE;UACzBT,aAAa,CAACO,QAAQ,CAACvF,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLoF,QAAQ,CAAC,+BAA+B,CAAC;QAC3C;QACAF,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOQ,GAAG,EAAE;QACZxF,OAAO,CAACiF,KAAK,CAAC,gCAAgC,EAAEO,GAAG,CAAC;QACpDN,QAAQ,CAAC,iCAAiCM,GAAG,CAACC,OAAO,EAAE,CAAC;QACxDT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAI9E,OAAO,EAAE;MACXkF,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAClF,OAAO,CAAC,CAAC;EAEb,MAAMwF,eAAe,GAAGA,CAAA,KAAM;IAC5BP,YAAY,CAACQ,IAAI,IAAIA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;EACtE,CAAC;EAED,MAAMC,kBAAkB,GAAI7F,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,oBAAOlB,OAAA,CAACH,KAAK;UAACmH,EAAE,EAAC,QAAQ;UAAAxD,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAC3C,KAAK,OAAO;QACV,oBAAOhE,OAAA,CAACH,KAAK;UAACmH,EAAE,EAAC,SAAS;UAACC,IAAI,EAAC,MAAM;UAAAzD,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACtD,KAAK,MAAM;QACT,oBAAOhE,OAAA,CAACH,KAAK;UAACmH,EAAE,EAAC,SAAS;UAAAxD,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACzC;QACE,oBAAOhE,OAAA,CAACH,KAAK;UAACmH,EAAE,EAAC,WAAW;UAAAxD,QAAA,EAAEtC;QAAI;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;IAC/C;EACF,CAAC;EAED,MAAMkD,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,CAAC;EAC9C,CAAC;EAED,oBACErH,OAAA,CAACV,SAAS;IAACiE,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACzBxD,OAAA;MAAKuD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrExD,OAAA;QAAAwD,QAAA,EAAI;MAAa;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBhE,OAAA,CAACX,IAAI;QAACiI,EAAE,EAAC,YAAY;QAAC/D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAE1D;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELkC,OAAO,gBACNlG,OAAA;MAAKuD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BxD,OAAA,CAACL,OAAO;QAACgE,SAAS,EAAC,QAAQ;QAAC4D,IAAI,EAAC,QAAQ;QAAC3D,OAAO,EAAC,SAAS;QAAAJ,QAAA,eACzDxD,OAAA;UAAMuD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,GACJoC,KAAK,gBACPpG,OAAA,CAACJ,KAAK;MAACgE,OAAO,EAAC,QAAQ;MAAAJ,QAAA,EAAE4C;IAAK;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrCgC,UAAU,gBACZhG,OAAA,CAAAE,SAAA;MAAAsD,QAAA,gBACExD,OAAA,CAACP,IAAI;QAAC8D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC9BxD,OAAA,CAACP,IAAI,CAAC+H,MAAM;UAACjE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAC5CxD,OAAA;YAAKuD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChExD,OAAA;cAAIuD,SAAS,EAAC,MAAM;cAAAC,QAAA,GACjBuD,kBAAkB,CAACf,UAAU,CAAC9E,IAAI,CAAC,EAAC,SAAO,EAACG,OAAO;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,EAEJgC,UAAU,CAACyB,KAAK,CAAC5F,UAAU,KAAK,OAAO,iBACtC7B,OAAA;cAAAwD,QAAA,gBACExD,OAAA,CAACN,MAAM;gBACLkE,OAAO,EAAEvD,SAAS,KAAK,UAAU,GAAG,OAAO,GAAG,eAAgB;gBAC9DqH,IAAI,EAAC,IAAI;gBACTnE,SAAS,EAAC,MAAM;gBAChBoE,OAAO,EAAEd,eAAgB;gBAAArD,QAAA,EAC1B;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThE,OAAA,CAACN,MAAM;gBACLkE,OAAO,EAAEvD,SAAS,KAAK,WAAW,GAAG,OAAO,GAAG,eAAgB;gBAC/DqH,IAAI,EAAC,IAAI;gBACTC,OAAO,EAAEd,eAAgB;gBAAArD,QAAA,EAC1B;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACdhE,OAAA,CAACP,IAAI,CAACmI,IAAI;UAAApE,QAAA,gBACRxD,OAAA,CAACT,GAAG;YAAAiE,QAAA,gBACFxD,OAAA,CAACR,GAAG;cAACqI,EAAE,EAAE,CAAE;cAACtE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eACtCxD,OAAA,CAACG,0BAA0B;gBACzBC,SAAS,EAAE4F,UAAU,CAACyB,KAAM;gBAC5BpH,SAAS,EAAEA,SAAU;gBACrBC,UAAU,EAAE0F,UAAU,CAAC9E;cAAK;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhE,OAAA,CAACR,GAAG;cAACqI,EAAE,EAAE,CAAE;cAAArE,QAAA,gBACTxD,OAAA;gBAAAwD,QAAA,EAAI;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BhE,OAAA;gBAAOuD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACrCxD,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAI8H,KAAK,EAAC,KAAK;sBAAAtE,QAAA,EAAC;oBAAI;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzBhE,OAAA;sBAAAwD,QAAA,EAAKwC,UAAU,CAAC9E;oBAAI;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrBhE,OAAA;sBAAAwD,QAAA,EACGwC,UAAU,CAACyB,KAAK,CAACM,aAAa,IAC9B/B,UAAU,CAACyB,KAAK,CAACO,WAAW,IAC5BhC,UAAU,CAACyB,KAAK,CAACQ,UAAU,IAAI;oBAAK;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAa;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtBhE,OAAA;sBAAAwD,QAAA,EAAK0D,UAAU,CAAClB,UAAU,CAACyB,KAAK,CAACS,SAAS;oBAAC;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpBhE,OAAA;sBAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACU,QAAQ,IAAI;oBAAK;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAI;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACbhE,OAAA;sBAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACF,IAAI,IAAI;oBAAK;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpBhE,OAAA;sBAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACW,WAAW,IAAI;oBAAe;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBhE,OAAA;sBAAAwD,QAAA,EACGwC,UAAU,CAACyB,KAAK,CAAC5F,UAAU,KAAK,OAAO,gBACtC7B,OAAA;wBAAMuD,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,gBAE3ChE,OAAA;wBAAMuD,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAC9C;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAACgC,UAAU,CAACyB,KAAK,CAACY,SAAS,IAAIrC,UAAU,CAACyB,KAAK,CAACa,QAAQ,kBACvDtI,OAAA,CAACT,GAAG;YAACgE,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBxD,OAAA,CAACR,GAAG;cAAAgE,QAAA,eACFxD,OAAA,CAACP,IAAI;gBAAA+D,QAAA,gBACHxD,OAAA,CAACP,IAAI,CAAC+H,MAAM;kBAAAhE,QAAA,eACVxD,OAAA;oBAAIuD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACdhE,OAAA,CAACP,IAAI,CAACmI,IAAI;kBAAApE,QAAA,eACRxD,OAAA,CAACT,GAAG;oBAAAiE,QAAA,GAED,EAAA0B,qBAAA,GAAAc,UAAU,CAACyB,KAAK,CAACY,SAAS,cAAAnD,qBAAA,uBAA1BA,qBAAA,CAA4BqD,WAAW,KAAI/G,MAAM,CAACC,IAAI,CAACuE,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAAC,CAAC7F,MAAM,GAAG,CAAC,iBACxG1C,OAAA,CAACR,GAAG;sBAACqI,EAAE,EAAE,CAAE;sBAACtE,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BxD,OAAA;wBAAIuD,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAqB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvDhE,OAAA;wBAAOuD,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,eAC/BxD,OAAA;0BAAAwD,QAAA,GACGwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAACC,WAAW,iBACjDxI,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAI8H,KAAK,EAAC,KAAK;8BAAAtE,QAAA,EAAC;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC1BhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAACC;4BAAW;8BAAA3E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3D,CACL,EACAgC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAACE,YAAY,iBAClDzI,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAM;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACfhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAACE;4BAAY;8BAAA5E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5D,CACL,EACAgC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAACG,QAAQ,iBAC9C1I,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAS;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACE,WAAW,CAACG;4BAAQ;8BAAA7E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CACL;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACN,EAGA,EAAAmB,sBAAA,GAAAa,UAAU,CAACyB,KAAK,CAACY,SAAS,cAAAlD,sBAAA,uBAA1BA,sBAAA,CAA4BwD,cAAc,KAAInH,MAAM,CAACC,IAAI,CAACuE,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAAC,CAACjG,MAAM,GAAG,CAAC,iBAC9G1C,OAAA,CAACR,GAAG;sBAACqI,EAAE,EAAE,CAAE;sBAACtE,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BxD,OAAA;wBAAIuD,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtDhE,OAAA;wBAAOuD,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,eAC/BxD,OAAA;0BAAAwD,QAAA,GACGwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAACC,GAAG,iBAC5C5I,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAI8H,KAAK,EAAC,KAAK;8BAAAtE,QAAA,EAAC;4BAAI;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzBhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAACC;4BAAG;8BAAA/E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CACL,EACAgC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAACE,aAAa,iBACtD7I,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAS;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAACE;4BAAa;8BAAAhF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChE,CACL,EACAgC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAACG,YAAY,iBACrD9I,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAa;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACtBhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACM,cAAc,CAACG;4BAAY;8BAAAjF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/D,CACL;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACN,EAGA,EAAAoB,sBAAA,GAAAY,UAAU,CAACyB,KAAK,CAACY,SAAS,cAAAjD,sBAAA,uBAA1BA,sBAAA,CAA4B2D,UAAU,kBACrC/I,OAAA,CAACR,GAAG;sBAACqI,EAAE,EAAE,CAAE;sBAACtE,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BxD,OAAA;wBAAIuD,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAmB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClDhE,OAAA;wBAAOuD,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,eAC/BxD,OAAA;0BAAAwD,QAAA,gBACExD,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAI8H,KAAK,EAAC,KAAK;8BAAAtE,QAAA,EAAC;4BAAW;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChChE,OAAA;8BAAAwD,QAAA,GAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACU,UAAU,CAACjB,KAAK,EAAC,QAAG,EAAC9B,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACU,UAAU,CAACC,MAAM;4BAAA;8BAAAnF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrG,CAAC,EACJgC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACU,UAAU,CAACE,MAAM,iBAC3CjJ,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChBhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACU,UAAU,CAACE;4BAAM;8BAAApF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CACL;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACN,EAGA,EAAAqB,sBAAA,GAAAW,UAAU,CAACyB,KAAK,CAACY,SAAS,cAAAhD,sBAAA,uBAA1BA,sBAAA,CAA4B6D,eAAe,kBAC1ClJ,OAAA,CAACR,GAAG;sBAACqI,EAAE,EAAE,CAAE;sBAACtE,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC1BxD,OAAA;wBAAIuD,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpDhE,OAAA;wBAAOuD,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,eAC/BxD,OAAA;0BAAAwD,QAAA,gBACExD,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAI8H,KAAK,EAAC,KAAK;8BAAAtE,QAAA,EAAC;4BAAS;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC9BhE,OAAA;8BAAAwD,QAAA,GAAA8B,sBAAA,GAAKU,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACa,eAAe,CAACC,QAAQ,cAAA7D,sBAAA,uBAAnDA,sBAAA,CAAqD8D,OAAO,CAAC,CAAC;4BAAC;8BAAAvF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxE,CAAC,eACLhE,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAU;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnBhE,OAAA;8BAAAwD,QAAA,GAAA+B,sBAAA,GAAKS,UAAU,CAACyB,KAAK,CAACY,SAAS,CAACa,eAAe,CAACG,SAAS,cAAA9D,sBAAA,uBAApDA,sBAAA,CAAsD6D,OAAO,CAAC,CAAC;4BAAC;8BAAAvF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACN,EAGAgC,UAAU,CAACyB,KAAK,CAAC5F,UAAU,KAAK,OAAO,iBACtC7B,OAAA,CAACR,GAAG;sBAACqI,EAAE,EAAE,EAAG;sBAACtE,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBAC3BxD,OAAA;wBAAIuD,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrDhE,OAAA;wBAAOuD,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,eAC/BxD,OAAA;0BAAAwD,QAAA,GACGwC,UAAU,CAACyB,KAAK,CAAC6B,QAAQ,iBACxBtJ,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAI8H,KAAK,EAAC,KAAK;8BAAAtE,QAAA,EAAC;4BAAS;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC9BhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAAC6B;4BAAQ;8BAAAzF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC,CACL,EACA,EAAAwB,qBAAA,GAAAQ,UAAU,CAACyB,KAAK,CAACa,QAAQ,cAAA9C,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B+D,WAAW,cAAA9D,sBAAA,uBAAtCA,sBAAA,CAAwC+D,QAAQ,kBAC/CxJ,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAS;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBhE,OAAA;8BAAAwD,QAAA,GAAKiG,IAAI,CAACC,KAAK,CAAC1D,UAAU,CAACyB,KAAK,CAACa,QAAQ,CAACiB,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;4BAAA;8BAAA3F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpE,CACL,EACA,EAAA0B,sBAAA,GAAAM,UAAU,CAACyB,KAAK,CAACa,QAAQ,cAAA5C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BqD,UAAU,cAAApD,sBAAA,uBAArCA,sBAAA,CAAuCmC,KAAK,OAAAlC,sBAAA,GAAII,UAAU,CAACyB,KAAK,CAACa,QAAQ,cAAA1C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BmD,UAAU,cAAAlD,sBAAA,uBAArCA,sBAAA,CAAuCmD,MAAM,kBAC5FhJ,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAW;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACpBhE,OAAA;8BAAAwD,QAAA,GAAKwC,UAAU,CAACyB,KAAK,CAACa,QAAQ,CAACS,UAAU,CAACjB,KAAK,EAAC,GAAC,EAAC9B,UAAU,CAACyB,KAAK,CAACa,QAAQ,CAACS,UAAU,CAACC,MAAM;4BAAA;8BAAAnF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CACL,EACA,EAAA8B,sBAAA,GAAAE,UAAU,CAACyB,KAAK,CAACa,QAAQ,cAAAxC,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2ByD,WAAW,cAAAxD,sBAAA,uBAAtCA,sBAAA,CAAwC4D,WAAW,kBAClD3J,OAAA;4BAAAwD,QAAA,gBACExD,OAAA;8BAAAwD,QAAA,EAAI;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChBhE,OAAA;8BAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACa,QAAQ,CAACiB,WAAW,CAACI,WAAW,CAACC,WAAW,CAAC;4BAAC;8BAAA/F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxE,CACL,EAEAgC,UAAU,CAACyB,KAAK,CAACoC,aAAa,iBAC7B7J,OAAA,CAAAE,SAAA;4BAAAsD,QAAA,GACGwC,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACC,QAAQ,IAAI9D,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACC,QAAQ,CAACpH,MAAM,GAAG,CAAC,iBAC5F1C,OAAA;8BAAAwD,QAAA,gBACExD,OAAA;gCAAAwD,QAAA,EAAI;8BAAkB;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,eAC3BhE,OAAA;gCAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACC,QAAQ,CAACpH;8BAAM;gCAAAmB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvD,CACL,EACAgC,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACE,MAAM,IAAI/D,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACE,MAAM,CAACrH,MAAM,GAAG,CAAC,iBACxF1C,OAAA;8BAAAwD,QAAA,gBACExD,OAAA;gCAAAwD,QAAA,EAAI;8BAAgB;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,eACzBhE,OAAA;gCAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACE,MAAM,CAACrH;8BAAM;gCAAAmB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrD,CACL,EACAgC,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACG,KAAK,IAAIhE,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACG,KAAK,CAACtH,MAAM,GAAG,CAAC,iBACtF1C,OAAA;8BAAAwD,QAAA,gBACExD,OAAA;gCAAAwD,QAAA,EAAI;8BAAe;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,eACxBhE,OAAA;gCAAAwD,QAAA,EAAKwC,UAAU,CAACyB,KAAK,CAACoC,aAAa,CAACG,KAAK,CAACtH;8BAAM;gCAAAmB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CACL;0BAAA,eACD,CACH;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAgC,UAAU,CAAC9E,IAAI,KAAK,SAAS,IAAI8E,UAAU,CAACyB,KAAK,CAACqC,QAAQ,iBACzD9J,OAAA;YAAKuD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxD,OAAA;cAAAwD,QAAA,EAAI;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBhE,OAAA;cAAKuD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BxD,OAAA;gBAAOuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACnDxD,OAAA;kBAAOuD,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC9BxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAE;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACXhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrBhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRhE,OAAA;kBAAAwD,QAAA,EACGwC,UAAU,CAACyB,KAAK,CAACqC,QAAQ,CAACG,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;oBAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,eAAA;oBAAA,oBAC5CtK,OAAA;sBAAAwD,QAAA,gBACExD,OAAA;wBAAAwD,QAAA,EAAK0G,OAAO,CAACK;sBAAU;wBAAA1G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7BhE,OAAA;wBAAAwD,QAAA,EAAK,EAAA4G,gBAAA,GAAAF,OAAO,CAACM,QAAQ,cAAAJ,gBAAA,uBAAhBA,gBAAA,CAAkBhB,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChDhE,OAAA;wBAAAwD,QAAA,EAAK,EAAA6G,iBAAA,GAAAH,OAAO,CAACO,QAAQ,cAAAJ,iBAAA,uBAAhBA,iBAAA,CAAkBjB,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChDhE,OAAA;wBAAAwD,QAAA,EAAK,EAAA8G,eAAA,GAAAJ,OAAO,CAACQ,MAAM,cAAAJ,eAAA,uBAAdA,eAAA,CAAgBlB,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9ChE,OAAA;wBAAAwD,QAAA,EACG0G,OAAO,CAACM,QAAQ,GAAG,IAAI,gBACtBxK,OAAA,CAACH,KAAK;0BAACmH,EAAE,EAAC,QAAQ;0BAAAxD,QAAA,EAAC;wBAAI;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,GAC7BkG,OAAO,CAACM,QAAQ,GAAG,GAAG,gBACxBxK,OAAA,CAACH,KAAK;0BAACmH,EAAE,EAAC,SAAS;0BAACC,IAAI,EAAC,MAAM;0BAAAzD,QAAA,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,gBAE9ChE,OAAA,CAACH,KAAK;0BAACmH,EAAE,EAAC,SAAS;0BAAAxD,QAAA,EAAC;wBAAG;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAC/B;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA,GAbEmG,KAAK;sBAAAtG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcV,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAgC,UAAU,CAAC9E,IAAI,KAAK,OAAO,IAAI8E,UAAU,CAACyB,KAAK,CAACsC,MAAM,iBACrD/J,OAAA;YAAKuD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxD,OAAA;cAAAwD,QAAA,EAAI;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBhE,OAAA;cAAKuD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BxD,OAAA;gBAAOuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACnDxD,OAAA;kBAAOuD,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC9BxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAE;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACXhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAI;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACbhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRhE,OAAA;kBAAAwD,QAAA,EACGwC,UAAU,CAACyB,KAAK,CAACsC,MAAM,CAACE,GAAG,CAAC,CAACU,KAAK,EAAER,KAAK;oBAAA,IAAAS,cAAA;oBAAA,oBACxC5K,OAAA;sBAAAwD,QAAA,gBACExD,OAAA;wBAAAwD,QAAA,EAAKmH,KAAK,CAACE;sBAAQ;wBAAAhH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzBhE,OAAA;wBAAAwD,QAAA,EAAKmH,KAAK,CAACG;sBAAU;wBAAAjH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3BhE,OAAA;wBAAAwD,QAAA,EAAK,EAAAoH,cAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,cAAA,uBAAdA,cAAA,CAAgBxB,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9ChE,OAAA;wBAAAwD,QAAA,GAAK,CAACmH,KAAK,CAACI,UAAU,GAAG,GAAG,EAAE3B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA,GAJxCmG,KAAK;sBAAAtG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKV,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAELgC,UAAU,CAACyB,KAAK,CAACuD,WAAW,iBAC3BhL,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAAwD,QAAA,EAAI;cAAuB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChChE,OAAA;gBAAKuD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BhC,MAAM,CAACyJ,OAAO,CAACjF,UAAU,CAACyB,KAAK,CAACuD,WAAW,CAAC,CAACf,GAAG,CAAC,CAAC,CAAC/I,IAAI,EAAEgK,KAAK,CAAC,kBAC9DlL,OAAA;kBAAgBuD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACnCxD,OAAA,CAACH,KAAK;oBAACmH,EAAE,EAAC,MAAM;oBAACzD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE0H;kBAAK;oBAAArH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC9C,IAAI;gBAAA,GAD/CA,IAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEAgC,UAAU,CAAC9E,IAAI,KAAK,MAAM,IAAI8E,UAAU,CAACyB,KAAK,CAACuC,KAAK,iBACnDhK,OAAA;YAAKuD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxD,OAAA;cAAAwD,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBhE,OAAA;cAAKuD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BxD,OAAA;gBAAOuD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACnDxD,OAAA;kBAAOuD,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC9BxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAE;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACXhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAI;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACbhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClBhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRhE,OAAA;kBAAAwD,QAAA,EACGwC,UAAU,CAACyB,KAAK,CAACuC,KAAK,CAACC,GAAG,CAAC,CAACkB,IAAI,EAAEhB,KAAK;oBAAA,IAAAiB,cAAA;oBAAA,oBACtCpL,OAAA;sBAAAwD,QAAA,gBACExD,OAAA;wBAAAwD,QAAA,EAAK2H,IAAI,CAACE;sBAAO;wBAAAxH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACvBhE,OAAA;wBAAAwD,QAAA,EAAK2H,IAAI,CAACG;sBAAS;wBAAAzH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzBhE,OAAA;wBAAAwD,QAAA,eACExD,OAAA,CAACH,KAAK;0BACJmH,EAAE,EACAmE,IAAI,CAACI,SAAS,KAAK,MAAM,GAAG,SAAS,GACrCJ,IAAI,CAACI,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,QACzC;0BACDtE,IAAI,EAAEkE,IAAI,CAACI,SAAS,KAAK,MAAM,GAAG,MAAM,GAAGC,SAAU;0BAAAhI,QAAA,EAEpD2H,IAAI,CAACI;wBAAS;0BAAA1H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACLhE,OAAA;wBAAAwD,QAAA,EAAK,EAAA4H,cAAA,GAAAD,IAAI,CAACM,QAAQ,cAAAL,cAAA,uBAAbA,cAAA,CAAehC,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GAdtCmG,KAAK;sBAAAtG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAeV,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAELgC,UAAU,CAACyB,KAAK,CAACiE,gBAAgB,iBAChC1L,OAAA;cAAKuD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxD,OAAA;gBAAAwD,QAAA,EAAI;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhE,OAAA;gBAAKuD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BhC,MAAM,CAACyJ,OAAO,CAACjF,UAAU,CAACyB,KAAK,CAACiE,gBAAgB,CAAC,CAACzB,GAAG,CAAC,CAAC,CAACsB,SAAS,EAAEL,KAAK,CAAC,kBACxElL,OAAA;kBAAqBuD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxCxD,OAAA,CAACH,KAAK;oBACJmH,EAAE,EACAuE,SAAS,KAAK,MAAM,GAAG,SAAS,GAChCA,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,QACpC;oBACDtE,IAAI,EAAEsE,SAAS,KAAK,MAAM,GAAG,MAAM,GAAGC,SAAU;oBAChDjI,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAEf0H;kBAAK;oBAAArH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,KAAC,EAACuH,SAAS;gBAAA,GAVXA,SAAS;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGDhE,OAAA;YAAKuD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBxD,OAAA,CAACP,IAAI;cAAC8D,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACxBxD,OAAA,CAACP,IAAI,CAAC+H,MAAM;gBAAAhE,QAAA,eACVxD,OAAA;kBAAIuD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACdhE,OAAA,CAACP,IAAI,CAACmI,IAAI;gBAAApE,QAAA,gBACRxD,OAAA;kBAAAwD,QAAA,EAAG;gBAAkE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACxEgC,UAAU,CAAC9E,IAAI,KAAK,SAAS,iBAC5BlB,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAwB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjChE,OAAA;sBAAAwD,QAAA,EAAI;oBAAe;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxBhE,OAAA;sBAAAwD,QAAA,EAAI;oBAAyB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClChE,OAAA;sBAAAwD,QAAA,EAAI;oBAAkB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBAAGxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAC5BgC,UAAU,CAACyB,KAAK,CAACqC,QAAQ,IAAI9D,UAAU,CAACyB,KAAK,CAACqC,QAAQ,CAACpH,MAAM,GAAG,CAAC,IACjEsD,UAAU,CAACyB,KAAK,CAACqC,QAAQ,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,QAAQ,GAAG,IAAI,CAAC,GACtD,MAAM,GAAGxE,UAAU,CAACyB,KAAK,CAACqC,QAAQ,IAAI9D,UAAU,CAACyB,KAAK,CAACqC,QAAQ,CAACpH,MAAM,GAAG,CAAC,IAC1EsD,UAAU,CAACyB,KAAK,CAACqC,QAAQ,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,QAAQ,GAAG,GAAG,CAAC,GACrD,QAAQ,GAAG,KAAK;kBAAA;oBAAA3G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEAgC,UAAU,CAAC9E,IAAI,KAAK,OAAO,iBAC1BlB,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAgC;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzChE,OAAA;sBAAAwD,QAAA,EAAI;oBAA+B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACvCgC,UAAU,CAACyB,KAAK,CAACuD,WAAW,IAC5BhF,UAAU,CAACyB,KAAK,CAACuD,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,iBAClDhL,OAAA;sBAAAwD,QAAA,EAAI;oBAAsD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAC/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBAAGxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAC5BgC,UAAU,CAACyB,KAAK,CAACuD,WAAW,IAC5BhF,UAAU,CAACyB,KAAK,CAACuD,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,GACnD,MAAM,GAAG,QAAQ;kBAAA;oBAAAnH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEAgC,UAAU,CAAC9E,IAAI,KAAK,MAAM,iBACzBlB,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,EAAI;oBAAuB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChChE,OAAA;sBAAAwD,QAAA,EAAI;oBAAuB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChChE,OAAA;sBAAAwD,QAAA,EAAI;oBAA8B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACLhE,OAAA;oBAAAwD,QAAA,gBAAGxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAC5BgC,UAAU,CAACyB,KAAK,CAACiE,gBAAgB,IACjC1F,UAAU,CAACyB,KAAK,CAACiE,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,GAC7C,MAAM,GAAG1F,UAAU,CAACyB,KAAK,CAACiE,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,GACtD,QAAQ,GAAG,KAAK;kBAAA;oBAAA7H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEPhE,OAAA;QAAKuD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9CxD,OAAA,CAACN,MAAM;UAACkE,OAAO,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAE1B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CAAC,gBAEHhE,OAAA,CAACJ,KAAK;MAACgE,OAAO,EAAC,SAAS;MAAAJ,QAAA,GAAC,+BAA6B,EAACnC,OAAO;IAAA;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACvE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB;AAACiB,GAAA,CA/hBQD,YAAY;EAAA,QACC5F,SAAS;AAAA;AAAAyM,GAAA,GADtB7G,YAAY;AAiiBrB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAA8G,GAAA;AAAAC,YAAA,CAAA/G,EAAA;AAAA+G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}