#!/usr/bin/env python3
"""
Test the PDF endpoint directly
"""

import requests
import sys
import time

def test_pdf_endpoint():
    """Test the PDF generation endpoint"""
    
    # Test URL
    url = "http://localhost:5000/api/pavement/images/test123/report"
    
    print(f"🧪 Testing PDF endpoint: {url}")
    
    try:
        # Make request
        response = requests.get(url, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            # Check if it's a PDF
            content_type = response.headers.get('content-type', '')
            if 'application/pdf' in content_type:
                # Save the PDF
                with open('test_downloaded_report.pdf', 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ PDF downloaded successfully!")
                print(f"📄 PDF size: {len(response.content)} bytes")
                print(f"💾 Saved as: test_downloaded_report.pdf")
                return True
            else:
                print(f"❌ Response is not a PDF. Content-Type: {content_type}")
                print(f"📄 Response content: {response.text[:500]}")
                return False
        else:
            print(f"❌ Request failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📄 Error message: {error_data.get('message', 'No message')}")
            except:
                print(f"📄 Response content: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Is the server running on localhost:5000?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def wait_for_server(max_wait=30):
    """Wait for the server to be ready"""
    print(f"⏳ Waiting for server to be ready (max {max_wait}s)...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:5000/", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 0:
            print(f"⏳ Still waiting... ({i}s)")
    
    print("❌ Server did not become ready in time")
    return False

if __name__ == "__main__":
    print("🚀 PDF Endpoint Test")
    print("=" * 50)
    
    # Wait for server
    if wait_for_server():
        # Test PDF endpoint
        success = test_pdf_endpoint()
        sys.exit(0 if success else 1)
    else:
        print("❌ Server is not running. Please start the backend server first.")
        sys.exit(1)
