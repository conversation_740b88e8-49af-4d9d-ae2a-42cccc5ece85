{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\DefectMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, useMap } from 'react-leaflet';\nimport axios from 'axios';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\n// Helper component to invalidate map size after mount and on tile load\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction MapSizeFix() {\n  _s();\n  const map = useMap();\n  useEffect(() => {\n    // Invalidate size shortly after mount\n    const t1 = setTimeout(() => {\n      try {\n        map.invalidateSize();\n      } catch (_) {}\n    }, 50);\n    const t2 = setTimeout(() => {\n      try {\n        map.invalidateSize();\n      } catch (_) {}\n    }, 250);\n\n    // Also on window resize\n    const onResize = () => {\n      try {\n        map.invalidateSize();\n      } catch (_) {}\n    };\n    window.addEventListener('resize', onResize);\n\n    // Invalidate after zoom animations end\n    map.on('zoomend', onResize);\n    map.on('moveend', onResize);\n    return () => {\n      clearTimeout(t1);\n      clearTimeout(t2);\n      window.removeEventListener('resize', onResize);\n      map.off('zoomend', onResize);\n      map.off('moveend', onResize);\n    };\n  }, [map]);\n  return null;\n}\n\n/**\r\n * Enhanced Image Display Component for Map Popups\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\n_s(MapSizeFix, \"IoceErwr5KVGS9kN4RQ1bOkYMAg=\", false, function () {\n  return [useMap];\n});\n_c = MapSizeFix;\nconst EnhancedMapImageDisplay = ({\n  defect\n}) => {\n  _s2();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n\n  // Generate comprehensive image URL with multiple fallback options\n  const generateImageUrl = defectData => {\n    if (!defectData) return null;\n    console.log('🔍 Generating map image URL for:', defectData.image_id);\n\n    // Priority 1: Try pre-signed URL (most secure and reliable)\n    if (defectData.original_image_presigned_url) {\n      console.log('🔗 Using pre-signed URL for map image');\n      return defectData.original_image_presigned_url;\n    }\n\n    // Priority 2: Try S3 full URL with proxy\n    if (defectData.original_image_full_url) {\n      console.log('🔗 Using full URL field');\n      // Extract S3 key from full URL and use proxy endpoint\n      const urlParts = defectData.original_image_full_url.split('/');\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n        const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n        console.log('✅ Generated proxy URL from full URL:', proxyUrl);\n        return proxyUrl;\n      }\n    }\n\n    // Priority 3: Try S3 key with proxy endpoint\n    if (defectData.original_image_s3_url) {\n      console.log('🔗 Using S3 key field');\n      const s3Key = defectData.original_image_s3_url;\n      const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\n      console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\n      return proxyUrl;\n    }\n\n    // Priority 4: Try direct S3 URL (fallback)\n    if (defectData.original_image_full_url) {\n      console.log('🔗 Using direct S3 URL as fallback');\n      return defectData.original_image_full_url;\n    }\n\n    // Priority 5: Try GridFS endpoint\n    if (defectData.original_image_id) {\n      console.log('🗄️ Using GridFS endpoint');\n      return `/api/pavement/get-image/${defectData.original_image_id}`;\n    }\n    console.warn('❌ No valid image URL found for defect:', defectData.image_id);\n    return null;\n  };\n\n  // Initialize image URL\n  useEffect(() => {\n    const url = generateImageUrl(defect);\n    console.log('🖼️ Setting initial map image URL:', url);\n    setCurrentImageUrl(url);\n    setHasError(false);\n    setIsLoading(true);\n    setFallbackAttempts(0);\n  }, [defect]);\n\n  // Handle image load error with fallback attempts\n  const handleImageError = e => {\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\n    if (fallbackAttempts === 0) {\n      // First fallback: Try GridFS if we haven't already\n      const gridfsId = defect.original_image_id;\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\n        setCurrentImageUrl(gridfsUrl);\n        setFallbackAttempts(1);\n        return;\n      }\n    }\n\n    // All fallbacks exhausted\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\n    setHasError(true);\n    setIsLoading(false);\n  };\n  const handleImageLoad = () => {\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\n    setIsLoading(false);\n    setHasError(false);\n  };\n\n  // Don't render anything if no URL available\n  if (!currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), \" Image not available\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          minHeight: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          variant: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2 small\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render error state\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small p-2 border rounded bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), \" Image unavailable\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render successful image\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-3 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: \"Defect image\",\n      className: \"img-fluid border rounded\",\n      style: {\n        maxHeight: '150px',\n        maxWidth: '100%'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-primary fw-bold\",\n        children: \"\\uD83D\\uDCF7 Original Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), fallbackAttempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-warning\",\n          children: \"(Fallback source)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n\n// Fix for the Leaflet default icon issue\n_s2(EnhancedMapImageDisplay, \"PL1c4k1ZSbH8d+OQcD09kxd231s=\");\n_c2 = EnhancedMapImageDisplay;\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Custom marker icons for different defect types using location icons\nconst createCustomIcon = (color, isVideo = false) => {\n  const iconSize = isVideo ? [36, 36] : [32, 32];\n\n  // Create different SVG content for video vs image markers\n  const svgContent = isVideo ?\n  // Video marker with camera icon made from SVG shapes (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\n    </svg>` :\n  // Image marker with standard location pin (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>`;\n  return L.icon({\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\n    iconSize: iconSize,\n    iconAnchor: [iconSize[0] / 2, iconSize[1]],\n    popupAnchor: [0, -iconSize[1]]\n  });\n};\nconst icons = {\n  pothole: createCustomIcon('#FF0000'),\n  // Red for potholes\n  crack: createCustomIcon('#FFCC00'),\n  // Yellow for cracks (alligator cracks)\n  kerb: createCustomIcon('#0066FF'),\n  // Blue for kerb defects\n  // Video variants\n  'pothole-video': createCustomIcon('#FF0000', true),\n  // Red for pothole videos\n  'crack-video': createCustomIcon('#FFCC00', true),\n  // Yellow for crack videos\n  'kerb-video': createCustomIcon('#0066FF', true) // Blue for kerb videos\n};\nfunction DefectMap({\n  user\n}) {\n  _s3();\n  const [defects, setDefects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [center] = useState([20.5937, 78.9629]); // India center\n  const [zoom] = useState(6); // Country-wide zoom for India\n  const mapRef = useRef(null);\n\n  // Filters for the map\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [usersList, setUsersList] = useState([]);\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\n    pothole: true,\n    crack: true,\n    kerb: true\n  });\n\n  // Fetch all images with defects and coordinates\n  const fetchDefectData = async (forceRefresh = false) => {\n    try {\n      var _response$data$images;\n      setLoading(true);\n      setError(null); // Clear any previous errors\n\n      // Prepare query parameters\n      let params = {};\n      if (startDate) params.start_date = startDate;\n      if (endDate) params.end_date = endDate;\n      if (selectedUser) params.username = selectedUser;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Always add cache-busting parameter to ensure latest data\n      params._t = Date.now();\n\n      // Add additional cache-busting for force refresh\n      if (forceRefresh) {\n        params._force = 'true';\n        params._refresh = Math.random().toString(36).substring(7);\n      }\n      console.log('🔄 Fetching defect data with params:', params);\n      const response = await axios.get('/api/dashboard/image-stats', {\n        params,\n        // Disable axios caching\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      console.log('📊 API response received:', {\n        success: response.data.success,\n        totalImages: response.data.total_images,\n        imageCount: (_response$data$images = response.data.images) === null || _response$data$images === void 0 ? void 0 : _response$data$images.length\n      });\n      if (response.data.success) {\n        // Process the data to extract defect locations with type information\n        const processedDefects = [];\n        response.data.images.forEach(image => {\n          try {\n            // Only process images with valid coordinates\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\n            if (image.coordinates && image.coordinates !== 'Not Available') {\n              var _image$exif_data;\n              let lat, lng;\n\n              // First, try to use EXIF GPS coordinates if available (most accurate)\n              if ((_image$exif_data = image.exif_data) !== null && _image$exif_data !== void 0 && _image$exif_data.gps_coordinates) {\n                lat = image.exif_data.gps_coordinates.latitude;\n                lng = image.exif_data.gps_coordinates.longitude;\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              } else {\n                // Fallback to stored coordinates\n                // Handle different coordinate formats\n                if (typeof image.coordinates === 'string') {\n                  // Parse coordinates (expected format: \"lat,lng\")\n                  const coords = image.coordinates.split(',');\n                  if (coords.length === 2) {\n                    lat = parseFloat(coords[0].trim());\n                    lng = parseFloat(coords[1].trim());\n                  }\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\n                  // Handle array format [lat, lng]\n                  lat = parseFloat(image.coordinates[0]);\n                  lng = parseFloat(image.coordinates[1]);\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\n                  // Handle object format {lat: x, lng: y}\n                  lat = parseFloat(image.coordinates.lat);\n                  lng = parseFloat(image.coordinates.lng);\n                }\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              }\n\n              // Validate coordinates are within reasonable bounds\n              if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\n                processedDefects.push({\n                  id: image.id,\n                  image_id: image.image_id,\n                  type: image.type,\n                  position: [lat, lng],\n                  defect_count: image.defect_count,\n                  timestamp: new Date(image.timestamp).toLocaleString(),\n                  username: image.username,\n                  original_image_id: image.original_image_id,\n                  // For cracks, include type information if available\n                  type_counts: image.type_counts,\n                  // For kerbs, include condition information if available\n                  condition_counts: image.condition_counts,\n                  // EXIF and metadata information\n                  exif_data: image.exif_data || {},\n                  metadata: image.metadata || {},\n                  media_type: image.media_type || 'image',\n                  original_image_full_url: image.original_image_full_url\n                });\n              } else {\n                console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\n              }\n            } else {\n              console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\n            }\n          } catch (coordError) {\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\n          }\n        });\n        console.log('Processed defects:', processedDefects.length);\n        setDefects(processedDefects);\n\n        // If no defects were processed, show a helpful message\n        if (processedDefects.length === 0) {\n          setError('No defects found with valid coordinates for the selected date range');\n        }\n      } else {\n        console.error('API returned success: false', response.data);\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\n      }\n      setLoading(false);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error fetching defect data:', err);\n      setError('Failed to load defect data: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n      setLoading(false);\n    }\n  };\n\n  // Fetch the list of users for the filter dropdown\n  const fetchUsers = async () => {\n    try {\n      const params = {};\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n      const response = await axios.get('/api/users/all', {\n        params\n      });\n      if (response.data.success) {\n        setUsersList(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  // Initialize default dates for the last 30 days\n  useEffect(() => {\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    console.log('DefectMap component initialized');\n  }, []);\n\n  // Fetch data when component mounts and when filters change\n  useEffect(() => {\n    fetchDefectData();\n    fetchUsers();\n  }, [user]);\n\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\n  useEffect(() => {\n    const interval = setInterval(() => {\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\n      fetchDefectData(true); // Force refresh to get latest EXIF data\n    }, 30000); // 30 seconds\n\n    return () => {\n      console.log('🛑 Clearing auto-refresh interval');\n      clearInterval(interval);\n    };\n  }, [startDate, endDate, selectedUser, user === null || user === void 0 ? void 0 : user.role]);\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    fetchDefectData(true);\n  };\n\n  // Handle filter application\n  const handleApplyFilters = () => {\n    fetchDefectData();\n  };\n\n  // Handle resetting filters\n  const handleResetFilters = () => {\n    // Reset date filters to last 30 days\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    setSelectedUser('');\n    setDefectTypeFilters({\n      pothole: true,\n      crack: true,\n      kerb: true\n    });\n\n    // Refetch data with reset filters\n    fetchDefectData();\n  };\n\n  // Handle defect type filter changes\n  const handleDefectTypeFilterChange = type => {\n    setDefectTypeFilters(prevFilters => ({\n      ...prevFilters,\n      [type]: !prevFilters[type]\n    }));\n  };\n\n  // Filter defects based on applied filters\n  const filteredDefects = defects.filter(defect => defectTypeFilters[defect.type]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"shadow-sm dashboard-card mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-0\",\n        children: \"Defect Map View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Date Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: startDate,\n                    onChange: e => setStartDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: endDate,\n                    onChange: e => setEndDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"User Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Select User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedUser,\n                    onChange: e => setSelectedUser(e.target.value),\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: user.username,\n                      children: [user.username, \" (\", user.role, \")\"]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Defect Type Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls defect-type-filters\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"defect-type-filter-options\",\n              style: {\n                marginTop: \"0\",\n                paddingLeft: \"0\",\n                width: \"100%\",\n                minWidth: \"150px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"pothole-filter\",\n                label: \"Potholes\",\n                checked: defectTypeFilters.pothole,\n                onChange: () => handleDefectTypeFilterChange('pothole'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"crack-filter\",\n                label: \"Cracks\",\n                checked: defectTypeFilters.crack,\n                onChange: () => handleDefectTypeFilterChange('crack'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"kerb-filter\",\n                label: \"Kerbs\",\n                checked: defectTypeFilters.kerb,\n                onChange: () => handleDefectTypeFilterChange('kerb'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-actions-container\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              onClick: handleApplyFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Apply Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: handleResetFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              onClick: handleRefresh,\n              disabled: loading,\n              className: \"filter-btn\",\n              children: loading ? '🔄 Refreshing...' : '🔄 Refresh Map'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF7 Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Defects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF9 Videos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pothole Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Crack Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"map-container\",\n        style: {\n          height: '500px',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(MapContainer, {\n          center: center,\n          zoom: zoom,\n          style: {\n            height: '100%',\n            width: '100%'\n          },\n          scrollWheelZoom: true,\n          whenCreated: mapInstance => {\n            mapRef.current = mapInstance;\n          },\n          children: [/*#__PURE__*/_jsxDEV(MapSizeFix, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TileLayer, {\n            attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n            url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this), filteredDefects.map(defect => {\n            var _defect$exif_data, _defect$exif_data$gps, _defect$exif_data$gps2, _defect$exif_data2, _defect$exif_data3, _defect$exif_data4, _defect$metadata, _defect$metadata$form, _defect$metadata2, _defect$metadata2$bas, _defect$metadata3, _defect$metadata3$bas, _defect$metadata4, _defect$metadata4$for;\n            // Determine icon based on media type and defect type\n            const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\n            const selectedIcon = icons[iconKey] || icons[defect.type];\n            return /*#__PURE__*/_jsxDEV(Marker, {\n              position: defect.position,\n              icon: selectedIcon,\n              children: /*#__PURE__*/_jsxDEV(Popup, {\n                maxWidth: 400,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-popup\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: [defect.type.charAt(0).toUpperCase() + defect.type.slice(1), \" Defect\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3 text-center\",\n                    children: [defect.representative_frame ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: `data:image/jpeg;base64,${defect.representative_frame}`,\n                        alt: \"Video thumbnail\",\n                        className: \"img-fluid border rounded shadow-sm\",\n                        style: {\n                          maxHeight: '150px',\n                          maxWidth: '100%',\n                          objectFit: 'cover'\n                        },\n                        onError: e => {\n                          console.warn(`Failed to load representative frame for video ${defect.image_id}`);\n                          e.target.style.display = 'none';\n                          // Show fallback message\n                          const fallback = e.target.nextElementSibling;\n                          if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\n                            fallback.style.display = 'block';\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-video\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 705,\n                          columnNumber: 33\n                        }, this), \" Video thumbnail unavailable\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-muted small p-3 border rounded bg-light\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-video fa-2x mb-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: \"Video thumbnail not available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-info fw-bold\",\n                        children: \"\\uD83D\\uDCF9 Video Thumbnail\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 29\n                      }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted d-block\",\n                          children: [\"Video ID: \", defect.video_id]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 718,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this), defect.media_type !== 'video' && /*#__PURE__*/_jsxDEV(EnhancedMapImageDisplay, {\n                    defect: defect\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 727,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Basic Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Count:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 734,\n                          columnNumber: 31\n                        }, this), \" \", defect.defect_count]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Date:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 735,\n                          columnNumber: 31\n                        }, this), \" \", defect.timestamp]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 735,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Reported by:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 31\n                        }, this), \" \", defect.username]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 736,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Media Type:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 31\n                        }, this), \" \", defect.media_type]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 737,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"GPS:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 738,\n                          columnNumber: 31\n                        }, this), \" \", defect.position[0].toFixed(6), \", \", defect.position[1].toFixed(6)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 23\n                  }, this), defect.type === 'crack' && defect.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Crack Types\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: Object.entries(defect.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [type, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 748,\n                          columnNumber: 46\n                        }, this), \" \", count]\n                      }, type, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 25\n                  }, this), defect.type === 'kerb' && defect.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Kerb Conditions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: Object.entries(defect.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [condition, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 759,\n                          columnNumber: 51\n                        }, this), \" \", count]\n                      }, condition, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 759,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 25\n                  }, this), (defect.exif_data || defect.metadata) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"\\uD83D\\uDCCA Media Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 768,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small\",\n                      children: [((_defect$exif_data = defect.exif_data) === null || _defect$exif_data === void 0 ? void 0 : _defect$exif_data.gps_coordinates) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2 p-2 bg-light rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          className: \"text-success\",\n                          children: \"\\uD83C\\uDF0D GPS (EXIF):\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Lat: \", (_defect$exif_data$gps = defect.exif_data.gps_coordinates.latitude) === null || _defect$exif_data$gps === void 0 ? void 0 : _defect$exif_data$gps.toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 774,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Lng: \", (_defect$exif_data$gps2 = defect.exif_data.gps_coordinates.longitude) === null || _defect$exif_data$gps2 === void 0 ? void 0 : _defect$exif_data$gps2.toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 775,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data2 = defect.exif_data) === null || _defect$exif_data2 === void 0 ? void 0 : _defect$exif_data2.camera_info) && Object.keys(defect.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\uD83D\\uDCF7 Camera:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 782,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled ms-2\",\n                          children: [defect.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Make: \", defect.exif_data.camera_info.camera_make]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 785,\n                            columnNumber: 37\n                          }, this), defect.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Model: \", defect.exif_data.camera_info.camera_model]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 788,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 783,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data3 = defect.exif_data) === null || _defect$exif_data3 === void 0 ? void 0 : _defect$exif_data3.technical_info) && Object.keys(defect.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u2699\\uFE0F Technical:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled ms-2\",\n                          children: [defect.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"ISO: \", defect.exif_data.technical_info.iso]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 37\n                          }, this), defect.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Exposure: \", defect.exif_data.technical_info.exposure_time]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 803,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data4 = defect.exif_data) === null || _defect$exif_data4 === void 0 ? void 0 : _defect$exif_data4.basic_info) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\uD83D\\uDCD0 Dimensions:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 812,\n                          columnNumber: 33\n                        }, this), \" \", defect.exif_data.basic_info.width, \" \\xD7 \", defect.exif_data.basic_info.height, defect.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [\" (\", defect.exif_data.basic_info.format, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 814,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 811,\n                        columnNumber: 31\n                      }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"text-info\",\n                          children: \"\\uD83D\\uDCF9 Video Information\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled small\",\n                          children: [((_defect$metadata = defect.metadata) === null || _defect$metadata === void 0 ? void 0 : (_defect$metadata$form = _defect$metadata.format_info) === null || _defect$metadata$form === void 0 ? void 0 : _defect$metadata$form.duration) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Duration:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 825,\n                              columnNumber: 41\n                            }, this), \" \", Math.round(defect.metadata.format_info.duration), \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 37\n                          }, this), ((_defect$metadata2 = defect.metadata) === null || _defect$metadata2 === void 0 ? void 0 : (_defect$metadata2$bas = _defect$metadata2.basic_info) === null || _defect$metadata2$bas === void 0 ? void 0 : _defect$metadata2$bas.width) && ((_defect$metadata3 = defect.metadata) === null || _defect$metadata3 === void 0 ? void 0 : (_defect$metadata3$bas = _defect$metadata3.basic_info) === null || _defect$metadata3$bas === void 0 ? void 0 : _defect$metadata3$bas.height) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Resolution:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 828,\n                              columnNumber: 41\n                            }, this), \" \", defect.metadata.basic_info.width, \"x\", defect.metadata.basic_info.height]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 37\n                          }, this), ((_defect$metadata4 = defect.metadata) === null || _defect$metadata4 === void 0 ? void 0 : (_defect$metadata4$for = _defect$metadata4.format_info) === null || _defect$metadata4$for === void 0 ? void 0 : _defect$metadata4$for.format_name) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Format:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 831,\n                              columnNumber: 41\n                            }, this), \" \", defect.metadata.format_info.format_name.toUpperCase()]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 831,\n                            columnNumber: 37\n                          }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Video ID:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 834,\n                              columnNumber: 41\n                            }, this), \" \", defect.video_id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 834,\n                            columnNumber: 37\n                          }, this), defect.model_outputs && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Potholes Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 840,\n                                columnNumber: 45\n                              }, this), \" \", defect.model_outputs.potholes.length]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 840,\n                              columnNumber: 41\n                            }, this), defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Cracks Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 843,\n                                columnNumber: 45\n                              }, this), \" \", defect.model_outputs.cracks.length]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 843,\n                              columnNumber: 41\n                            }, this), defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Kerbs Detected:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 846,\n                                columnNumber: 45\n                              }, this), \" \", defect.model_outputs.kerbs.length]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 846,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 823,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: `/view/${defect.image_id}`,\n                      className: \"btn btn-sm btn-primary\",\n                      onClick: e => e.stopPropagation(),\n                      children: \"View Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 859,\n                      columnNumber: 25\n                    }, this), defect.media_type !== 'video' && defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: defect.original_image_full_url,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"btn btn-sm btn-outline-secondary\",\n                      onClick: e => e.stopPropagation(),\n                      children: \"View Original\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this)\n            }, defect.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 484,\n    columnNumber: 5\n  }, this);\n}\n_s3(DefectMap, \"va2KsqkWO8jHrIyKplfdw8m/S68=\");\n_c3 = DefectMap;\nexport default DefectMap;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MapSizeFix\");\n$RefreshReg$(_c2, \"EnhancedMapImageDisplay\");\n$RefreshReg$(_c3, \"DefectMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "useMap", "axios", "L", "Card", "Row", "Col", "Form", "<PERSON><PERSON>", "Spinner", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MapSizeFix", "_s", "map", "t1", "setTimeout", "invalidateSize", "_", "t2", "onResize", "window", "addEventListener", "on", "clearTimeout", "removeEventListener", "off", "_c", "EnhancedMapImageDisplay", "defect", "_s2", "currentImageUrl", "setCurrentImageUrl", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "isLoading", "setIsLoading", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "generateImageUrl", "defectData", "console", "log", "image_id", "original_image_presigned_url", "original_image_full_url", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "length", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "original_image_s3_url", "<PERSON><PERSON><PERSON>", "original_image_id", "warn", "url", "handleImageError", "e", "gridfsId", "gridfsUrl", "error", "handleImageLoad", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "minHeight", "animation", "size", "variant", "src", "alt", "maxHeight", "max<PERSON><PERSON><PERSON>", "onError", "onLoad", "_c2", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createCustomIcon", "color", "isVideo", "iconSize", "svgContent", "icon", "iconAnchor", "popupAnchor", "icons", "pothole", "crack", "kerb", "DefectMap", "user", "_s3", "defects", "setDefects", "loading", "setLoading", "setError", "center", "zoom", "mapRef", "startDate", "setStartDate", "endDate", "setEndDate", "selected<PERSON>ser", "setSelectedUser", "usersList", "setUsersList", "defectTypeFilters", "setDefectTypeFilters", "fetchDefectData", "forceRefresh", "_response$data$images", "params", "start_date", "end_date", "username", "role", "user_role", "_t", "Date", "now", "_force", "_refresh", "Math", "random", "toString", "substring", "response", "get", "headers", "success", "data", "totalImages", "total_images", "imageCount", "images", "processedDefects", "for<PERSON>ach", "image", "media_type", "coordinates", "type", "_image$exif_data", "lat", "lng", "exif_data", "gps_coordinates", "latitude", "longitude", "coords", "parseFloat", "trim", "Array", "isArray", "isNaN", "id", "push", "position", "defect_count", "timestamp", "toLocaleString", "type_counts", "condition_counts", "metadata", "coordError", "message", "err", "_err$response", "_err$response$data", "fetchUsers", "users", "currentDate", "thirtyDaysAgo", "setDate", "getDate", "toISOString", "interval", "setInterval", "clearInterval", "handleRefresh", "handleApplyFilters", "handleResetFilters", "handleDefectTypeFilterChange", "prevFilters", "filteredDefects", "filter", "Header", "Body", "lg", "Group", "Label", "Control", "value", "onChange", "target", "Select", "index", "marginTop", "paddingLeft", "width", "min<PERSON><PERSON><PERSON>", "Check", "label", "checked", "onClick", "disabled", "backgroundColor", "height", "scrollWheelZoom", "whenCreated", "mapInstance", "current", "attribution", "_defect$exif_data", "_defect$exif_data$gps", "_defect$exif_data$gps2", "_defect$exif_data2", "_defect$exif_data3", "_defect$exif_data4", "_defect$metadata", "_defect$metadata$form", "_defect$metadata2", "_defect$metadata2$bas", "_defect$metadata3", "_defect$metadata3$bas", "_defect$metadata4", "_defect$metadata4$for", "<PERSON><PERSON><PERSON>", "selectedIcon", "char<PERSON>t", "toUpperCase", "representative_frame", "objectFit", "display", "fallback", "nextElement<PERSON><PERSON>ling", "classList", "contains", "video_id", "toFixed", "Object", "entries", "count", "condition", "camera_info", "keys", "camera_make", "camera_model", "technical_info", "iso", "exposure_time", "basic_info", "format", "format_info", "duration", "round", "format_name", "model_outputs", "potholes", "cracks", "kerbs", "to", "stopPropagation", "href", "rel", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/DefectMap.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';\r\nimport axios from 'axios';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport { Card, Row, Col, Form, But<PERSON>, Spinner } from 'react-bootstrap';\r\nimport { Link } from 'react-router-dom';\r\n\r\n// Helper component to invalidate map size after mount and on tile load\r\nfunction MapSizeFix() {\r\n  const map = useMap();\r\n  useEffect(() => {\r\n    // Invalidate size shortly after mount\r\n    const t1 = setTimeout(() => {\r\n      try { map.invalidateSize(); } catch (_) {}\r\n    }, 50);\r\n    const t2 = setTimeout(() => {\r\n      try { map.invalidateSize(); } catch (_) {}\r\n    }, 250);\r\n\r\n    // Also on window resize\r\n    const onResize = () => {\r\n      try { map.invalidateSize(); } catch (_) {}\r\n    };\r\n    window.addEventListener('resize', onResize);\r\n\r\n    // Invalidate after zoom animations end\r\n    map.on('zoomend', onResize);\r\n    map.on('moveend', onResize);\r\n\r\n    return () => {\r\n      clearTimeout(t1);\r\n      clearTimeout(t2);\r\n      window.removeEventListener('resize', onResize);\r\n      map.off('zoomend', onResize);\r\n      map.off('moveend', onResize);\r\n    };\r\n  }, [map]);\r\n  return null;\r\n}\r\n\r\n/**\r\n * Enhanced Image Display Component for Map Popups\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\r\nconst EnhancedMapImageDisplay = ({ defect }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n\r\n  // Generate comprehensive image URL with multiple fallback options\r\n  const generateImageUrl = (defectData) => {\r\n    if (!defectData) return null;\r\n\r\n    console.log('🔍 Generating map image URL for:', defectData.image_id);\r\n\r\n    // Priority 1: Try pre-signed URL (most secure and reliable)\r\n    if (defectData.original_image_presigned_url) {\r\n      console.log('🔗 Using pre-signed URL for map image');\r\n      return defectData.original_image_presigned_url;\r\n    }\r\n\r\n    // Priority 2: Try S3 full URL with proxy\r\n    if (defectData.original_image_full_url) {\r\n      console.log('🔗 Using full URL field');\r\n      // Extract S3 key from full URL and use proxy endpoint\r\n      const urlParts = defectData.original_image_full_url.split('/');\r\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n        const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n        console.log('✅ Generated proxy URL from full URL:', proxyUrl);\r\n        return proxyUrl;\r\n      }\r\n    }\r\n\r\n    // Priority 3: Try S3 key with proxy endpoint\r\n    if (defectData.original_image_s3_url) {\r\n      console.log('🔗 Using S3 key field');\r\n      const s3Key = defectData.original_image_s3_url;\r\n      const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\r\n      console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n\r\n    // Priority 4: Try direct S3 URL (fallback)\r\n    if (defectData.original_image_full_url) {\r\n      console.log('🔗 Using direct S3 URL as fallback');\r\n      return defectData.original_image_full_url;\r\n    }\r\n\r\n    // Priority 5: Try GridFS endpoint\r\n    if (defectData.original_image_id) {\r\n      console.log('🗄️ Using GridFS endpoint');\r\n      return `/api/pavement/get-image/${defectData.original_image_id}`;\r\n    }\r\n\r\n    console.warn('❌ No valid image URL found for defect:', defectData.image_id);\r\n    return null;\r\n  };\r\n\r\n  // Initialize image URL\r\n  useEffect(() => {\r\n    const url = generateImageUrl(defect);\r\n    console.log('🖼️ Setting initial map image URL:', url);\r\n    setCurrentImageUrl(url);\r\n    setHasError(false);\r\n    setIsLoading(true);\r\n    setFallbackAttempts(0);\r\n  }, [defect]);\r\n\r\n  // Handle image load error with fallback attempts\r\n  const handleImageError = (e) => {\r\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\r\n\r\n    if (fallbackAttempts === 0) {\r\n      // First fallback: Try GridFS if we haven't already\r\n      const gridfsId = defect.original_image_id;\r\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\r\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\r\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\r\n        setCurrentImageUrl(gridfsUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks exhausted\r\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\r\n    setHasError(true);\r\n    setIsLoading(false);\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\r\n    setIsLoading(false);\r\n    setHasError(false);\r\n  };\r\n\r\n  // Don't render anything if no URL available\r\n  if (!currentImageUrl) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small\">\r\n          <i className=\"fas fa-image\"></i> Image not available\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '100px' }}>\r\n          <Spinner animation=\"border\" size=\"sm\" variant=\"primary\" />\r\n          <span className=\"ms-2 small\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (hasError) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small p-2 border rounded bg-light\">\r\n          <i className=\"fas fa-exclamation-triangle\"></i> Image unavailable\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render successful image\r\n  return (\r\n    <div className=\"mb-3 text-center\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt=\"Defect image\"\r\n        className=\"img-fluid border rounded\"\r\n        style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n        onError={handleImageError}\r\n        onLoad={handleImageLoad}\r\n      />\r\n      <div className=\"mt-1\">\r\n        <small className=\"text-primary fw-bold\">📷 Original Image</small>\r\n        {fallbackAttempts > 0 && (\r\n          <div>\r\n            <small className=\"text-warning\">(Fallback source)</small>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Fix for the Leaflet default icon issue\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\r\n});\r\n\r\n// Custom marker icons for different defect types using location icons\r\nconst createCustomIcon = (color, isVideo = false) => {\r\n  const iconSize = isVideo ? [36, 36] : [32, 32];\r\n\r\n  // Create different SVG content for video vs image markers\r\n  const svgContent = isVideo ?\r\n    // Video marker with camera icon made from SVG shapes (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\r\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\r\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\r\n    </svg>` :\r\n    // Image marker with standard location pin (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n    </svg>`;\r\n\r\n  return L.icon({\r\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\r\n    iconSize: iconSize,\r\n    iconAnchor: [iconSize[0]/2, iconSize[1]],\r\n    popupAnchor: [0, -iconSize[1]],\r\n  });\r\n};\r\n\r\nconst icons = {\r\n  pothole: createCustomIcon('#FF0000'), // Red for potholes\r\n  crack: createCustomIcon('#FFCC00'),   // Yellow for cracks (alligator cracks)\r\n  kerb: createCustomIcon('#0066FF'),    // Blue for kerb defects\r\n  // Video variants\r\n  'pothole-video': createCustomIcon('#FF0000', true), // Red for pothole videos\r\n  'crack-video': createCustomIcon('#FFCC00', true),   // Yellow for crack videos\r\n  'kerb-video': createCustomIcon('#0066FF', true),    // Blue for kerb videos\r\n};\r\n\r\nfunction DefectMap({ user }) {\r\n  const [defects, setDefects] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [center] = useState([20.5937, 78.9629]); // India center\r\n  const [zoom] = useState(6); // Country-wide zoom for India\r\n  const mapRef = useRef(null);\r\n  \r\n  // Filters for the map\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\r\n    pothole: true,\r\n    crack: true,\r\n    kerb: true,\r\n  });\r\n\r\n  // Fetch all images with defects and coordinates\r\n  const fetchDefectData = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Prepare query parameters\r\n      let params = {};\r\n      if (startDate) params.start_date = startDate;\r\n      if (endDate) params.end_date = endDate;\r\n      if (selectedUser) params.username = selectedUser;\r\n      if (user?.role) params.user_role = user.role;\r\n\r\n      // Always add cache-busting parameter to ensure latest data\r\n      params._t = Date.now();\r\n\r\n      // Add additional cache-busting for force refresh\r\n      if (forceRefresh) {\r\n        params._force = 'true';\r\n        params._refresh = Math.random().toString(36).substring(7);\r\n      }\r\n\r\n      console.log('🔄 Fetching defect data with params:', params);\r\n      const response = await axios.get('/api/dashboard/image-stats', {\r\n        params,\r\n        // Disable axios caching\r\n        headers: {\r\n          'Cache-Control': 'no-cache',\r\n          'Pragma': 'no-cache'\r\n        }\r\n      });\r\n      console.log('📊 API response received:', {\r\n        success: response.data.success,\r\n        totalImages: response.data.total_images,\r\n        imageCount: response.data.images?.length\r\n      });\r\n\r\n      if (response.data.success) {\r\n        // Process the data to extract defect locations with type information\r\n        const processedDefects = [];\r\n        \r\n        response.data.images.forEach(image => {\r\n          try {\r\n            // Only process images with valid coordinates\r\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\r\n            if (image.coordinates && image.coordinates !== 'Not Available') {\r\n              let lat, lng;\r\n\r\n              // First, try to use EXIF GPS coordinates if available (most accurate)\r\n              if (image.exif_data?.gps_coordinates) {\r\n                lat = image.exif_data.gps_coordinates.latitude;\r\n                lng = image.exif_data.gps_coordinates.longitude;\r\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              } else {\r\n                // Fallback to stored coordinates\r\n                // Handle different coordinate formats\r\n                if (typeof image.coordinates === 'string') {\r\n                  // Parse coordinates (expected format: \"lat,lng\")\r\n                  const coords = image.coordinates.split(',');\r\n                  if (coords.length === 2) {\r\n                    lat = parseFloat(coords[0].trim());\r\n                    lng = parseFloat(coords[1].trim());\r\n                  }\r\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\r\n                  // Handle array format [lat, lng]\r\n                  lat = parseFloat(image.coordinates[0]);\r\n                  lng = parseFloat(image.coordinates[1]);\r\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\r\n                  // Handle object format {lat: x, lng: y}\r\n                  lat = parseFloat(image.coordinates.lat);\r\n                  lng = parseFloat(image.coordinates.lng);\r\n                }\r\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              }\r\n\r\n            // Validate coordinates are within reasonable bounds\r\n            if (!isNaN(lat) && !isNaN(lng) &&\r\n                lat >= -90 && lat <= 90 &&\r\n                lng >= -180 && lng <= 180) {\r\n              console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\r\n              processedDefects.push({\r\n                id: image.id,\r\n                image_id: image.image_id,\r\n                type: image.type,\r\n                position: [lat, lng],\r\n                defect_count: image.defect_count,\r\n                timestamp: new Date(image.timestamp).toLocaleString(),\r\n                username: image.username,\r\n                original_image_id: image.original_image_id,\r\n                // For cracks, include type information if available\r\n                type_counts: image.type_counts,\r\n                // For kerbs, include condition information if available\r\n                condition_counts: image.condition_counts,\r\n                // EXIF and metadata information\r\n                exif_data: image.exif_data || {},\r\n                metadata: image.metadata || {},\r\n                media_type: image.media_type || 'image',\r\n                original_image_full_url: image.original_image_full_url\r\n              });\r\n            } else {\r\n              console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\r\n          }\r\n          } catch (coordError) {\r\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\r\n          }\r\n        });\r\n\r\n        console.log('Processed defects:', processedDefects.length);\r\n        setDefects(processedDefects);\r\n\r\n        // If no defects were processed, show a helpful message\r\n        if (processedDefects.length === 0) {\r\n          setError('No defects found with valid coordinates for the selected date range');\r\n        }\r\n      } else {\r\n        console.error('API returned success: false', response.data);\r\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching defect data:', err);\r\n      setError('Failed to load defect data: ' + (err.response?.data?.message || err.message));\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Fetch the list of users for the filter dropdown\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const params = {};\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      const response = await axios.get('/api/users/all', { params });\r\n      if (response.data.success) {\r\n        setUsersList(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  };\r\n\r\n  // Initialize default dates for the last 30 days\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n\r\n    console.log('DefectMap component initialized');\r\n  }, []);\r\n\r\n  // Fetch data when component mounts and when filters change\r\n  useEffect(() => {\r\n    fetchDefectData();\r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\r\n      fetchDefectData(true); // Force refresh to get latest EXIF data\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => {\r\n      console.log('🛑 Clearing auto-refresh interval');\r\n      clearInterval(interval);\r\n    };\r\n  }, [startDate, endDate, selectedUser, user?.role]);\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    fetchDefectData(true);\r\n  };\r\n\r\n  // Handle filter application\r\n  const handleApplyFilters = () => {\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle resetting filters\r\n  const handleResetFilters = () => {\r\n    // Reset date filters to last 30 days\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n    \r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n    setSelectedUser('');\r\n    setDefectTypeFilters({\r\n      pothole: true,\r\n      crack: true,\r\n      kerb: true,\r\n    });\r\n    \r\n    // Refetch data with reset filters\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle defect type filter changes\r\n  const handleDefectTypeFilterChange = (type) => {\r\n    setDefectTypeFilters(prevFilters => ({\r\n      ...prevFilters,\r\n      [type]: !prevFilters[type]\r\n    }));\r\n  };\r\n\r\n  // Filter defects based on applied filters\r\n  const filteredDefects = defects.filter(defect => \r\n    defectTypeFilters[defect.type]\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-sm dashboard-card mb-4\">\r\n      <Card.Header className=\"bg-primary text-white\">\r\n        <h5 className=\"mb-0\">Defect Map View</h5>\r\n      </Card.Header>\r\n      <Card.Body>\r\n        <Row className=\"mb-3\">\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Date Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Start Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={startDate}\r\n                      onChange={(e) => setStartDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">End Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={endDate}\r\n                      onChange={(e) => setEndDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>User Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Select User</Form.Label>\r\n                    <Form.Select\r\n                      value={selectedUser}\r\n                      onChange={(e) => setSelectedUser(e.target.value)}\r\n                      size=\"sm\"\r\n                    >\r\n                      <option value=\"\">All Users</option>\r\n                      {usersList.map((user, index) => (\r\n                        <option key={index} value={user.username}>\r\n                          {user.username} ({user.role})\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Defect Type Filter</h6>\r\n            <div className=\"filter-controls defect-type-filters\">\r\n              <div className=\"defect-type-filter-options\" style={{ marginTop: \"0\", paddingLeft: \"0\", width: \"100%\", minWidth: \"150px\" }}>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"pothole-filter\"\r\n                  label=\"Potholes\"\r\n                  checked={defectTypeFilters.pothole}\r\n                  onChange={() => handleDefectTypeFilterChange('pothole')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"crack-filter\"\r\n                  label=\"Cracks\"\r\n                  checked={defectTypeFilters.crack}\r\n                  onChange={() => handleDefectTypeFilterChange('crack')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"kerb-filter\"\r\n                  label=\"Kerbs\"\r\n                  checked={defectTypeFilters.kerb}\r\n                  onChange={() => handleDefectTypeFilterChange('kerb')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        <Row className=\"mb-3\">\r\n          <Col className=\"text-center\">\r\n            <div className=\"filter-actions-container\">\r\n              <Button \r\n                variant=\"primary\" \r\n                size=\"sm\" \r\n                onClick={handleApplyFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Apply Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={handleResetFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Reset Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading}\r\n                className=\"filter-btn\"\r\n              >\r\n                {loading ? '🔄 Refreshing...' : '🔄 Refresh Map'}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Row>\r\n          <Col>\r\n            <div className=\"map-legend mb-3\">\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📷 Images</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FF0000' }}></div>\r\n                  <span>Potholes</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FFCC00' }}></div>\r\n                  <span>Cracks</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#0066FF' }}></div>\r\n                  <span>Kerb Defects</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📹 Videos</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FF0000' }}>📹</div>\r\n                  <span>Pothole Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FFCC00' }}>📹</div>\r\n                  <span>Crack Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#0066FF' }}>📹</div>\r\n                  <span>Kerb Videos</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center p-5\">\r\n            <div className=\"spinner-border text-primary\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"alert alert-danger\">{error}</div>\r\n        ) : (\r\n          <div className=\"map-container\" style={{ height: '500px', width: '100%' }}>\r\n            <MapContainer \r\n              center={center} \r\n              zoom={zoom} \r\n              style={{ height: '100%', width: '100%' }}\r\n              scrollWheelZoom={true}\r\n              whenCreated={(mapInstance) => { mapRef.current = mapInstance; }}\r\n            >\r\n              {/* Ensure Leaflet recalculates size after mount/visibility */}\r\n              <MapSizeFix />\r\n              <TileLayer\r\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              />\r\n              \r\n              {filteredDefects.map((defect) => {\r\n                // Determine icon based on media type and defect type\r\n                const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\r\n                const selectedIcon = icons[iconKey] || icons[defect.type];\r\n\r\n                return (\r\n                <Marker\r\n                  key={defect.id}\r\n                  position={defect.position}\r\n                  icon={selectedIcon}\r\n                >\r\n                  <Popup maxWidth={400}>\r\n                    <div className=\"defect-popup\">\r\n                      <h6>{defect.type.charAt(0).toUpperCase() + defect.type.slice(1)} Defect</h6>\r\n\r\n                      {/* Video Thumbnail */}\r\n                      {defect.media_type === 'video' && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          {defect.representative_frame ? (\r\n                            <>\r\n                              <img\r\n                                src={`data:image/jpeg;base64,${defect.representative_frame}`}\r\n                                alt=\"Video thumbnail\"\r\n                                className=\"img-fluid border rounded shadow-sm\"\r\n                                style={{ maxHeight: '150px', maxWidth: '100%', objectFit: 'cover' }}\r\n                                onError={(e) => {\r\n                                  console.warn(`Failed to load representative frame for video ${defect.image_id}`);\r\n                                  e.target.style.display = 'none';\r\n                                  // Show fallback message\r\n                                  const fallback = e.target.nextElementSibling;\r\n                                  if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\r\n                                    fallback.style.display = 'block';\r\n                                  }\r\n                                }}\r\n                              />\r\n                              <div className=\"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\" style={{ display: 'none' }}>\r\n                                <i className=\"fas fa-video\"></i> Video thumbnail unavailable\r\n                              </div>\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"text-muted small p-3 border rounded bg-light\">\r\n                              <i className=\"fas fa-video fa-2x mb-2\"></i>\r\n                              <div>Video thumbnail not available</div>\r\n                            </div>\r\n                          )}\r\n                          <div className=\"mt-2\">\r\n                            <small className=\"text-info fw-bold\">📹 Video Thumbnail</small>\r\n                            {defect.video_id && (\r\n                              <div>\r\n                                <small className=\"text-muted d-block\">Video ID: {defect.video_id}</small>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Enhanced Image Display with Fallbacks */}\r\n                      {defect.media_type !== 'video' && (\r\n                        <EnhancedMapImageDisplay defect={defect} />\r\n                      )}\r\n\r\n                      {/* Basic Information */}\r\n                      <div className=\"mb-3\">\r\n                        <h6 className=\"text-primary\">Basic Information</h6>\r\n                        <ul className=\"list-unstyled small\">\r\n                          <li><strong>Count:</strong> {defect.defect_count}</li>\r\n                          <li><strong>Date:</strong> {defect.timestamp}</li>\r\n                          <li><strong>Reported by:</strong> {defect.username}</li>\r\n                          <li><strong>Media Type:</strong> {defect.media_type}</li>\r\n                          <li><strong>GPS:</strong> {defect.position[0].toFixed(6)}, {defect.position[1].toFixed(6)}</li>\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Defect-specific Information */}\r\n                      {defect.type === 'crack' && defect.type_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Crack Types</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.type_counts).map(([type, count]) => (\r\n                              <li key={type}><strong>{type}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {defect.type === 'kerb' && defect.condition_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Kerb Conditions</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.condition_counts).map(([condition, count]) => (\r\n                              <li key={condition}><strong>{condition}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* EXIF/Metadata Information */}\r\n                      {(defect.exif_data || defect.metadata) && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">📊 Media Information</h6>\r\n                          <div className=\"small\">\r\n                            {/* GPS Information - Show first as it's most important for mapping */}\r\n                            {defect.exif_data?.gps_coordinates && (\r\n                              <div className=\"mb-2 p-2 bg-light rounded\">\r\n                                <strong className=\"text-success\">🌍 GPS (EXIF):</strong>\r\n                                <div>Lat: {defect.exif_data.gps_coordinates.latitude?.toFixed(6)}</div>\r\n                                <div>Lng: {defect.exif_data.gps_coordinates.longitude?.toFixed(6)}</div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Camera Information */}\r\n                            {defect.exif_data?.camera_info && Object.keys(defect.exif_data.camera_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📷 Camera:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.camera_info.camera_make && (\r\n                                    <li>Make: {defect.exif_data.camera_info.camera_make}</li>\r\n                                  )}\r\n                                  {defect.exif_data.camera_info.camera_model && (\r\n                                    <li>Model: {defect.exif_data.camera_info.camera_model}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Technical Information */}\r\n                            {defect.exif_data?.technical_info && Object.keys(defect.exif_data.technical_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>⚙️ Technical:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.technical_info.iso && (\r\n                                    <li>ISO: {defect.exif_data.technical_info.iso}</li>\r\n                                  )}\r\n                                  {defect.exif_data.technical_info.exposure_time && (\r\n                                    <li>Exposure: {defect.exif_data.technical_info.exposure_time}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Basic Media Info */}\r\n                            {defect.exif_data?.basic_info && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📐 Dimensions:</strong> {defect.exif_data.basic_info.width} × {defect.exif_data.basic_info.height}\r\n                                {defect.exif_data.basic_info.format && (\r\n                                  <span> ({defect.exif_data.basic_info.format})</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Video-specific metadata */}\r\n                            {defect.media_type === 'video' && (\r\n                              <div className=\"mb-3\">\r\n                                <h6 className=\"text-info\">📹 Video Information</h6>\r\n                                <ul className=\"list-unstyled small\">\r\n                                  {defect.metadata?.format_info?.duration && (\r\n                                    <li><strong>Duration:</strong> {Math.round(defect.metadata.format_info.duration)}s</li>\r\n                                  )}\r\n                                  {defect.metadata?.basic_info?.width && defect.metadata?.basic_info?.height && (\r\n                                    <li><strong>Resolution:</strong> {defect.metadata.basic_info.width}x{defect.metadata.basic_info.height}</li>\r\n                                  )}\r\n                                  {defect.metadata?.format_info?.format_name && (\r\n                                    <li><strong>Format:</strong> {defect.metadata.format_info.format_name.toUpperCase()}</li>\r\n                                  )}\r\n                                  {defect.video_id && (\r\n                                    <li><strong>Video ID:</strong> {defect.video_id}</li>\r\n                                  )}\r\n                                  {/* Show detection counts for videos */}\r\n                                  {defect.model_outputs && (\r\n                                    <>\r\n                                      {defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && (\r\n                                        <li><strong>Potholes Detected:</strong> {defect.model_outputs.potholes.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && (\r\n                                        <li><strong>Cracks Detected:</strong> {defect.model_outputs.cracks.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && (\r\n                                        <li><strong>Kerbs Detected:</strong> {defect.model_outputs.kerbs.length}</li>\r\n                                      )}\r\n                                    </>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Action Buttons */}\r\n                      <div className=\"d-flex gap-2\">\r\n                        <Link\r\n                          to={`/view/${defect.image_id}`}\r\n                          className=\"btn btn-sm btn-primary\"\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                        {/* Only show 'View Original' button for non-video entries */}\r\n                        {defect.media_type !== 'video' && defect.original_image_full_url && (\r\n                          <a\r\n                            href={defect.original_image_full_url}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"btn btn-sm btn-outline-secondary\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            View Original\r\n                          </a>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Popup>\r\n                </Marker>\r\n                );\r\n              })}\r\n            </MapContainer>\r\n          </div>\r\n        )}\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DefectMap; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACvE,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,GAAG,GAAGhB,MAAM,CAAC,CAAC;EACpBP,SAAS,CAAC,MAAM;IACd;IACA,MAAMwB,EAAE,GAAGC,UAAU,CAAC,MAAM;MAC1B,IAAI;QAAEF,GAAG,CAACG,cAAc,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;IAC3C,CAAC,EAAE,EAAE,CAAC;IACN,MAAMC,EAAE,GAAGH,UAAU,CAAC,MAAM;MAC1B,IAAI;QAAEF,GAAG,CAACG,cAAc,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;IAC3C,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA,MAAME,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI;QAAEN,GAAG,CAACG,cAAc,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;IAC3C,CAAC;IACDG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;;IAE3C;IACAN,GAAG,CAACS,EAAE,CAAC,SAAS,EAAEH,QAAQ,CAAC;IAC3BN,GAAG,CAACS,EAAE,CAAC,SAAS,EAAEH,QAAQ,CAAC;IAE3B,OAAO,MAAM;MACXI,YAAY,CAACT,EAAE,CAAC;MAChBS,YAAY,CAACL,EAAE,CAAC;MAChBE,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEL,QAAQ,CAAC;MAC9CN,GAAG,CAACY,GAAG,CAAC,SAAS,EAAEN,QAAQ,CAAC;MAC5BN,GAAG,CAACY,GAAG,CAAC,SAAS,EAAEN,QAAQ,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAACN,GAAG,CAAC,CAAC;EACT,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AAHAD,EAAA,CAhCSD,UAAU;EAAA,QACLd,MAAM;AAAA;AAAA6B,EAAA,GADXf,UAAU;AAoCnB,MAAMgB,uBAAuB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAM+C,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAE5BC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEF,UAAU,CAACG,QAAQ,CAAC;;IAEpE;IACA,IAAIH,UAAU,CAACI,4BAA4B,EAAE;MAC3CH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,OAAOF,UAAU,CAACI,4BAA4B;IAChD;;IAEA;IACA,IAAIJ,UAAU,CAACK,uBAAuB,EAAE;MACtCJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC;MACA,MAAMI,QAAQ,GAAGN,UAAU,CAACK,uBAAuB,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9D,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;MACrE,IAAIH,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAE;QAC3D,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,KAAK,CAACN,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;QACvD,MAAMC,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;QAC1EZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEc,QAAQ,CAAC;QAC7D,OAAOA,QAAQ;MACjB;IACF;;IAEA;IACA,IAAIhB,UAAU,CAACkB,qBAAqB,EAAE;MACpCjB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,MAAMW,KAAK,GAAGb,UAAU,CAACkB,qBAAqB;MAC9C,MAAMC,UAAU,GAAGN,KAAK,CAACN,KAAK,CAAC,GAAG,CAAC,CAACjC,GAAG,CAACoC,IAAI,IAAIO,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;MACnF,MAAMC,QAAQ,GAAG,8BAA8BG,UAAU,EAAE;MAC3DlB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEc,QAAQ,CAAC;MAC3D,OAAOA,QAAQ;IACjB;;IAEA;IACA,IAAIhB,UAAU,CAACK,uBAAuB,EAAE;MACtCJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAOF,UAAU,CAACK,uBAAuB;IAC3C;;IAEA;IACA,IAAIL,UAAU,CAACoB,iBAAiB,EAAE;MAChCnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,OAAO,2BAA2BF,UAAU,CAACoB,iBAAiB,EAAE;IAClE;IAEAnB,OAAO,CAACoB,IAAI,CAAC,wCAAwC,EAAErB,UAAU,CAACG,QAAQ,CAAC;IAC3E,OAAO,IAAI;EACb,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACd,MAAMuE,GAAG,GAAGvB,gBAAgB,CAACV,MAAM,CAAC;IACpCY,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoB,GAAG,CAAC;IACtD9B,kBAAkB,CAAC8B,GAAG,CAAC;IACvB5B,WAAW,CAAC,KAAK,CAAC;IAClBE,YAAY,CAAC,IAAI,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMkC,gBAAgB,GAAIC,CAAC,IAAK;IAC9BvB,OAAO,CAACoB,IAAI,CAAC,oCAAoCxB,gBAAgB,GAAG,CAAC,IAAI,EAAEN,eAAe,CAAC;IAE3F,IAAIM,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAM4B,QAAQ,GAAGpC,MAAM,CAAC+B,iBAAiB;MACzC,IAAIK,QAAQ,IAAI,CAAClC,eAAe,CAACoB,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtD,MAAMe,SAAS,GAAG,2BAA2BD,QAAQ,EAAE;QACvDxB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwB,SAAS,CAAC;QACpDlC,kBAAkB,CAACkC,SAAS,CAAC;QAC7B5B,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA;IACAG,OAAO,CAAC0B,KAAK,CAAC,0CAA0C,EAAEtC,MAAM,CAACc,QAAQ,CAAC;IAC1ET,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMgC,eAAe,GAAGA,CAAA,KAAM;IAC5B3B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEX,eAAe,CAAC;IAChEK,YAAY,CAAC,KAAK,CAAC;IACnBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAI,CAACH,eAAe,EAAE;IACpB,oBACEtB,OAAA;MAAK4D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B7D,OAAA;QAAK4D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7D,OAAA;UAAG4D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,wBAClC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIvC,SAAS,EAAE;IACb,oBACE1B,OAAA;MAAK4D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B7D,OAAA;QAAK4D,SAAS,EAAC,kDAAkD;QAACM,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAN,QAAA,gBAC9F7D,OAAA,CAACH,OAAO;UAACuE,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,IAAI;UAACC,OAAO,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DjE,OAAA;UAAM4D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIzC,QAAQ,EAAE;IACZ,oBACExB,OAAA;MAAK4D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B7D,OAAA;QAAK4D,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3D7D,OAAA;UAAG4D,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sBACjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEjE,OAAA;IAAK4D,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B7D,OAAA;MACEuE,GAAG,EAAEjD,eAAgB;MACrBkD,GAAG,EAAC,cAAc;MAClBZ,SAAS,EAAC,0BAA0B;MACpCM,KAAK,EAAE;QAAEO,SAAS,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAChDC,OAAO,EAAErB,gBAAiB;MAC1BsB,MAAM,EAAEjB;IAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFjE,OAAA;MAAK4D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB7D,OAAA;QAAO4D,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChErC,gBAAgB,GAAG,CAAC,iBACnB5B,OAAA;QAAA6D,QAAA,eACE7D,OAAA;UAAO4D,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA5C,GAAA,CAzJMF,uBAAuB;AAAA0D,GAAA,GAAvB1D,uBAAuB;AA0J7B,OAAO5B,CAAC,CAACuF,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3C1F,CAAC,CAACuF,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,KAAK;EACnD,MAAMC,QAAQ,GAAGD,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAME,UAAU,GAAGF,OAAO;EACxB;EACA,qEAAqED,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA,8CAA8CF,KAAK;AACnD,sDAAsDA,KAAK;AAC3D,WAAW;EACP;EACA,qEAAqEA,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA,WAAW;EAET,OAAOlG,CAAC,CAACoG,IAAI,CAAC;IACZP,OAAO,EAAE,oCAAoCpC,kBAAkB,CAAC0C,UAAU,CAAC,EAAE;IAC7ED,QAAQ,EAAEA,QAAQ;IAClBG,UAAU,EAAE,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxCI,WAAW,EAAE,CAAC,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,KAAK,GAAG;EACZC,OAAO,EAAET,gBAAgB,CAAC,SAAS,CAAC;EAAE;EACtCU,KAAK,EAAEV,gBAAgB,CAAC,SAAS,CAAC;EAAI;EACtCW,IAAI,EAAEX,gBAAgB,CAAC,SAAS,CAAC;EAAK;EACtC;EACA,eAAe,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAE;EACpD,aAAa,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAI;EACpD,YAAY,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAK;AACtD,CAAC;AAED,SAASY,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,GAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwH,OAAO,EAAEC,UAAU,CAAC,GAAGzH,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2E,KAAK,EAAE+C,QAAQ,CAAC,GAAG1H,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2H,MAAM,CAAC,GAAG3H,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC4H,IAAI,CAAC,GAAG5H,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM6H,MAAM,GAAG5H,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAAC6H,SAAS,EAAEC,YAAY,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgI,OAAO,EAAEC,UAAU,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkI,YAAY,EAAEC,eAAe,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoI,SAAS,EAAEC,YAAY,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvI,QAAQ,CAAC;IACzDgH,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMsB,eAAe,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACtD,IAAI;MAAA,IAAAC,qBAAA;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIiB,MAAM,GAAG,CAAC,CAAC;MACf,IAAIb,SAAS,EAAEa,MAAM,CAACC,UAAU,GAAGd,SAAS;MAC5C,IAAIE,OAAO,EAAEW,MAAM,CAACE,QAAQ,GAAGb,OAAO;MACtC,IAAIE,YAAY,EAAES,MAAM,CAACG,QAAQ,GAAGZ,YAAY;MAChD,IAAId,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG5B,IAAI,CAAC2B,IAAI;;MAE5C;MACAJ,MAAM,CAACM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIV,YAAY,EAAE;QAChBE,MAAM,CAACS,MAAM,GAAG,MAAM;QACtBT,MAAM,CAACU,QAAQ,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3D;MAEAxG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEyF,MAAM,CAAC;MAC3D,MAAMe,QAAQ,GAAG,MAAMnJ,KAAK,CAACoJ,GAAG,CAAC,4BAA4B,EAAE;QAC7DhB,MAAM;QACN;QACAiB,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACF3G,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC2G,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;QAC9BE,WAAW,EAAEL,QAAQ,CAACI,IAAI,CAACE,YAAY;QACvCC,UAAU,GAAAvB,qBAAA,GAAEgB,QAAQ,CAACI,IAAI,CAACI,MAAM,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsB9E;MACpC,CAAC,CAAC;MAEF,IAAI8F,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzB;QACA,MAAMM,gBAAgB,GAAG,EAAE;QAE3BT,QAAQ,CAACI,IAAI,CAACI,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;UACpC,IAAI;YACF;YACApH,OAAO,CAACC,GAAG,CAAC,iBAAiBmH,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAAClH,QAAQ,iBAAiBkH,KAAK,CAACE,WAAW,UAAUF,KAAK,CAACG,IAAI,EAAE,CAAC;YACnI,IAAIH,KAAK,CAACE,WAAW,IAAIF,KAAK,CAACE,WAAW,KAAK,eAAe,EAAE;cAAA,IAAAE,gBAAA;cAC9D,IAAIC,GAAG,EAAEC,GAAG;;cAEZ;cACA,KAAAF,gBAAA,GAAIJ,KAAK,CAACO,SAAS,cAAAH,gBAAA,eAAfA,gBAAA,CAAiBI,eAAe,EAAE;gBACpCH,GAAG,GAAGL,KAAK,CAACO,SAAS,CAACC,eAAe,CAACC,QAAQ;gBAC9CH,GAAG,GAAGN,KAAK,CAACO,SAAS,CAACC,eAAe,CAACE,SAAS;gBAC/C9H,OAAO,CAACC,GAAG,CAAC,qCAAqCmH,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAAClH,QAAQ,MAAMuH,GAAG,KAAKC,GAAG,GAAG,CAAC;cACrH,CAAC,MAAM;gBACL;gBACA;gBACA,IAAI,OAAON,KAAK,CAACE,WAAW,KAAK,QAAQ,EAAE;kBACzC;kBACA,MAAMS,MAAM,GAAGX,KAAK,CAACE,WAAW,CAAChH,KAAK,CAAC,GAAG,CAAC;kBAC3C,IAAIyH,MAAM,CAACpH,MAAM,KAAK,CAAC,EAAE;oBACvB8G,GAAG,GAAGO,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;oBAClCP,GAAG,GAAGM,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;kBACpC;gBACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACf,KAAK,CAACE,WAAW,CAAC,IAAIF,KAAK,CAACE,WAAW,CAAC3G,MAAM,KAAK,CAAC,EAAE;kBAC7E;kBACA8G,GAAG,GAAGO,UAAU,CAACZ,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC;kBACtCI,GAAG,GAAGM,UAAU,CAACZ,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,MAAM,IAAI,OAAOF,KAAK,CAACE,WAAW,KAAK,QAAQ,IAAIF,KAAK,CAACE,WAAW,CAACG,GAAG,IAAIL,KAAK,CAACE,WAAW,CAACI,GAAG,EAAE;kBAClG;kBACAD,GAAG,GAAGO,UAAU,CAACZ,KAAK,CAACE,WAAW,CAACG,GAAG,CAAC;kBACvCC,GAAG,GAAGM,UAAU,CAACZ,KAAK,CAACE,WAAW,CAACI,GAAG,CAAC;gBACzC;gBACA1H,OAAO,CAACC,GAAG,CAAC,mCAAmCmH,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAAClH,QAAQ,MAAMuH,GAAG,KAAKC,GAAG,GAAG,CAAC;cACnH;;cAEF;cACA,IAAI,CAACU,KAAK,CAACX,GAAG,CAAC,IAAI,CAACW,KAAK,CAACV,GAAG,CAAC,IAC1BD,GAAG,IAAI,CAAC,EAAE,IAAIA,GAAG,IAAI,EAAE,IACvBC,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;gBAC7B1H,OAAO,CAACC,GAAG,CAAC,2BAA2BmH,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACiB,EAAE,MAAMZ,GAAG,KAAKC,GAAG,mBAAmB,CAAC;gBACnHR,gBAAgB,CAACoB,IAAI,CAAC;kBACpBD,EAAE,EAAEjB,KAAK,CAACiB,EAAE;kBACZnI,QAAQ,EAAEkH,KAAK,CAAClH,QAAQ;kBACxBqH,IAAI,EAAEH,KAAK,CAACG,IAAI;kBAChBgB,QAAQ,EAAE,CAACd,GAAG,EAAEC,GAAG,CAAC;kBACpBc,YAAY,EAAEpB,KAAK,CAACoB,YAAY;kBAChCC,SAAS,EAAE,IAAIxC,IAAI,CAACmB,KAAK,CAACqB,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;kBACrD7C,QAAQ,EAAEuB,KAAK,CAACvB,QAAQ;kBACxB1E,iBAAiB,EAAEiG,KAAK,CAACjG,iBAAiB;kBAC1C;kBACAwH,WAAW,EAAEvB,KAAK,CAACuB,WAAW;kBAC9B;kBACAC,gBAAgB,EAAExB,KAAK,CAACwB,gBAAgB;kBACxC;kBACAjB,SAAS,EAAEP,KAAK,CAACO,SAAS,IAAI,CAAC,CAAC;kBAChCkB,QAAQ,EAAEzB,KAAK,CAACyB,QAAQ,IAAI,CAAC,CAAC;kBAC9BxB,UAAU,EAAED,KAAK,CAACC,UAAU,IAAI,OAAO;kBACvCjH,uBAAuB,EAAEgH,KAAK,CAAChH;gBACjC,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLJ,OAAO,CAACoB,IAAI,CAAC,6BAA6BgG,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACiB,EAAE,GAAG,EAAEjB,KAAK,CAACE,WAAW,EAAE,eAAeG,GAAG,SAASC,GAAG,EAAE,CAAC;cAC5I;YACF,CAAC,MAAM;cACL1H,OAAO,CAACC,GAAG,CAAC,eAAemH,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACiB,EAAE,iBAAiBjB,KAAK,CAACE,WAAW,EAAE,CAAC;YACzG;UACA,CAAC,CAAC,OAAOwB,UAAU,EAAE;YACnB9I,OAAO,CAAC0B,KAAK,CAAC,4CAA4C0F,KAAK,CAACiB,EAAE,GAAG,EAAES,UAAU,EAAE1B,KAAK,CAACE,WAAW,CAAC;UACvG;QACF,CAAC,CAAC;QAEFtH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiH,gBAAgB,CAACvG,MAAM,CAAC;QAC1D2D,UAAU,CAAC4C,gBAAgB,CAAC;;QAE5B;QACA,IAAIA,gBAAgB,CAACvG,MAAM,KAAK,CAAC,EAAE;UACjC8D,QAAQ,CAAC,qEAAqE,CAAC;QACjF;MACF,CAAC,MAAM;QACLzE,OAAO,CAAC0B,KAAK,CAAC,6BAA6B,EAAE+E,QAAQ,CAACI,IAAI,CAAC;QAC3DpC,QAAQ,CAAC,8BAA8B,IAAIgC,QAAQ,CAACI,IAAI,CAACkC,OAAO,IAAI,eAAe,CAAC,CAAC;MACvF;MAEAvE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOwE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZlJ,OAAO,CAAC0B,KAAK,CAAC,6BAA6B,EAAEsH,GAAG,CAAC;MACjDvE,QAAQ,CAAC,8BAA8B,IAAI,EAAAwE,aAAA,GAAAD,GAAG,CAACvC,QAAQ,cAAAwC,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcpC,IAAI,cAAAqC,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;MACvFvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2E,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMzD,MAAM,GAAG,CAAC,CAAC;MACjB,IAAIvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG5B,IAAI,CAAC2B,IAAI;MAE5C,MAAMW,QAAQ,GAAG,MAAMnJ,KAAK,CAACoJ,GAAG,CAAC,gBAAgB,EAAE;QAAEhB;MAAO,CAAC,CAAC;MAC9D,IAAIe,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzBxB,YAAY,CAACqB,QAAQ,CAACI,IAAI,CAACuC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC,OAAO1H,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA5E,SAAS,CAAC,MAAM;IACd,MAAMuM,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDwE,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvDN,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnD,SAAS,CAAC,MAAM;IACdyI,eAAe,CAAC,CAAC;IACjB4D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAChF,IAAI,CAAC,CAAC;;EAEV;EACArH,SAAS,CAAC,MAAM;IACd,MAAM4M,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC3J,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5EsF,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM;MACXvF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD2J,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAAC7E,SAAS,EAAEE,OAAO,EAAEE,YAAY,EAAEd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,CAAC,CAAC;;EAElD;EACA,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvE,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMV,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDwE,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAACnJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD4E,eAAe,CAAC,EAAE,CAAC;IACnBI,oBAAoB,CAAC;MACnBvB,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACAsB,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyE,4BAA4B,GAAIzC,IAAI,IAAK;IAC7CjC,oBAAoB,CAAC2E,WAAW,KAAK;MACnC,GAAGA,WAAW;MACd,CAAC1C,IAAI,GAAG,CAAC0C,WAAW,CAAC1C,IAAI;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM2C,eAAe,GAAG7F,OAAO,CAAC8F,MAAM,CAAC/K,MAAM,IAC3CiG,iBAAiB,CAACjG,MAAM,CAACmI,IAAI,CAC/B,CAAC;EAED,oBACEvJ,OAAA,CAACR,IAAI;IAACoE,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC7C7D,OAAA,CAACR,IAAI,CAAC4M,MAAM;MAACxI,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5C7D,OAAA;QAAI4D,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdjE,OAAA,CAACR,IAAI,CAAC6M,IAAI;MAAAxI,QAAA,gBACR7D,OAAA,CAACP,GAAG;QAACmE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB7D,OAAA,CAACN,GAAG;UAAC4M,EAAE,EAAE,CAAE;UAAC1I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpC7D,OAAA;YAAA6D,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBjE,OAAA;YAAK4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B7D,OAAA;cAAK4D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC7D,OAAA;gBAAK4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B7D,OAAA,CAACL,IAAI,CAAC4M,KAAK;kBAAA1I,QAAA,gBACT7D,OAAA,CAACL,IAAI,CAAC6M,KAAK;oBAAC5I,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrDjE,OAAA,CAACL,IAAI,CAAC8M,OAAO;oBACXlD,IAAI,EAAC,MAAM;oBACXmD,KAAK,EAAE7F,SAAU;oBACjB8F,QAAQ,EAAGpJ,CAAC,IAAKuD,YAAY,CAACvD,CAAC,CAACqJ,MAAM,CAACF,KAAK,CAAE;oBAC9CrI,IAAI,EAAC;kBAAI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B7D,OAAA,CAACL,IAAI,CAAC4M,KAAK;kBAAA1I,QAAA,gBACT7D,OAAA,CAACL,IAAI,CAAC6M,KAAK;oBAAC5I,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnDjE,OAAA,CAACL,IAAI,CAAC8M,OAAO;oBACXlD,IAAI,EAAC,MAAM;oBACXmD,KAAK,EAAE3F,OAAQ;oBACf4F,QAAQ,EAAGpJ,CAAC,IAAKyD,UAAU,CAACzD,CAAC,CAACqJ,MAAM,CAACF,KAAK,CAAE;oBAC5CrI,IAAI,EAAC;kBAAI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjE,OAAA,CAACN,GAAG;UAAC4M,EAAE,EAAE,CAAE;UAAC1I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpC7D,OAAA;YAAA6D,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBjE,OAAA;YAAK4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B7D,OAAA;cAAK4D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrC7D,OAAA;gBAAK4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B7D,OAAA,CAACL,IAAI,CAAC4M,KAAK;kBAAA1I,QAAA,gBACT7D,OAAA,CAACL,IAAI,CAAC6M,KAAK;oBAAC5I,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDjE,OAAA,CAACL,IAAI,CAACkN,MAAM;oBACVH,KAAK,EAAEzF,YAAa;oBACpB0F,QAAQ,EAAGpJ,CAAC,IAAK2D,eAAe,CAAC3D,CAAC,CAACqJ,MAAM,CAACF,KAAK,CAAE;oBACjDrI,IAAI,EAAC,IAAI;oBAAAR,QAAA,gBAET7D,OAAA;sBAAQ0M,KAAK,EAAC,EAAE;sBAAA7I,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAClCkD,SAAS,CAAC9G,GAAG,CAAC,CAAC8F,IAAI,EAAE2G,KAAK,kBACzB9M,OAAA;sBAAoB0M,KAAK,EAAEvG,IAAI,CAAC0B,QAAS;sBAAAhE,QAAA,GACtCsC,IAAI,CAAC0B,QAAQ,EAAC,IAAE,EAAC1B,IAAI,CAAC2B,IAAI,EAAC,GAC9B;oBAAA,GAFagF,KAAK;sBAAAhJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjE,OAAA,CAACN,GAAG;UAAC4M,EAAE,EAAE,CAAE;UAAC1I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpC7D,OAAA;YAAA6D,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BjE,OAAA;YAAK4D,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD7D,OAAA;cAAK4D,SAAS,EAAC,4BAA4B;cAACM,KAAK,EAAE;gBAAE6I,SAAS,EAAE,GAAG;gBAAEC,WAAW,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAArJ,QAAA,gBACxH7D,OAAA,CAACL,IAAI,CAACwN,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,gBAAgB;gBACnB+C,KAAK,EAAC,UAAU;gBAChBC,OAAO,EAAEhG,iBAAiB,CAACtB,OAAQ;gBACnC4G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,SAAS,CAAE;gBACxDpI,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFjE,OAAA,CAACL,IAAI,CAACwN,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,cAAc;gBACjB+C,KAAK,EAAC,QAAQ;gBACdC,OAAO,EAAEhG,iBAAiB,CAACrB,KAAM;gBACjC2G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,OAAO,CAAE;gBACtDpI,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFjE,OAAA,CAACL,IAAI,CAACwN,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,aAAa;gBAChB+C,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAEhG,iBAAiB,CAACpB,IAAK;gBAChC0G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,MAAM,CAAE;gBACrDpI,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA,CAACP,GAAG;QAACmE,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB7D,OAAA,CAACN,GAAG;UAACkE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B7D,OAAA;YAAK4D,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC7D,OAAA,CAACJ,MAAM;cACL0E,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTiJ,OAAO,EAAExB,kBAAmB;cAC5BlI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAACJ,MAAM;cACL0E,OAAO,EAAC,mBAAmB;cAC3BD,IAAI,EAAC,IAAI;cACTiJ,OAAO,EAAEvB,kBAAmB;cAC5BnI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAACJ,MAAM;cACL0E,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTiJ,OAAO,EAAEzB,aAAc;cACvB0B,QAAQ,EAAEhH,OAAQ;cAClB3C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAErB0C,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA,CAACP,GAAG;QAAAoE,QAAA,eACF7D,OAAA,CAACN,GAAG;UAAAmE,QAAA,eACF7D,OAAA;YAAK4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B7D,OAAA;cAAK4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7D,OAAA;gBAAI4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CjE,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7D,OAAA;kBAAK4D,SAAS,EAAC,eAAe;kBAACM,KAAK,EAAE;oBAAEsJ,eAAe,EAAE;kBAAU;gBAAE;kBAAA1J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EjE,OAAA;kBAAA6D,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7D,OAAA;kBAAK4D,SAAS,EAAC,eAAe;kBAACM,KAAK,EAAE;oBAAEsJ,eAAe,EAAE;kBAAU;gBAAE;kBAAA1J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EjE,OAAA;kBAAA6D,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7D,OAAA;kBAAK4D,SAAS,EAAC,eAAe;kBAACM,KAAK,EAAE;oBAAEsJ,eAAe,EAAE;kBAAU;gBAAE;kBAAA1J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EjE,OAAA;kBAAA6D,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjE,OAAA;cAAK4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7D,OAAA;gBAAI4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CjE,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7D,OAAA;kBAAK4D,SAAS,EAAC,4BAA4B;kBAACM,KAAK,EAAE;oBAAEsJ,eAAe,EAAE;kBAAU,CAAE;kBAAA3J,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FjE,OAAA;kBAAA6D,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7D,OAAA;kBAAK4D,SAAS,EAAC,4BAA4B;kBAACM,KAAK,EAAE;oBAAEsJ,eAAe,EAAE;kBAAU,CAAE;kBAAA3J,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FjE,OAAA;kBAAA6D,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7D,OAAA;kBAAK4D,SAAS,EAAC,4BAA4B;kBAACM,KAAK,EAAE;oBAAEsJ,eAAe,EAAE;kBAAU,CAAE;kBAAA3J,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FjE,OAAA;kBAAA6D,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELsC,OAAO,gBACNvG,OAAA;QAAK4D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B7D,OAAA;UAAK4D,SAAS,EAAC,6BAA6B;UAACkE,IAAI,EAAC,QAAQ;UAAAjE,QAAA,eACxD7D,OAAA;YAAM4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJP,KAAK,gBACP1D,OAAA;QAAK4D,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEjDjE,OAAA;QAAK4D,SAAS,EAAC,eAAe;QAACM,KAAK,EAAE;UAAEuJ,MAAM,EAAE,OAAO;UAAER,KAAK,EAAE;QAAO,CAAE;QAAApJ,QAAA,eACvE7D,OAAA,CAACf,YAAY;UACXyH,MAAM,EAAEA,MAAO;UACfC,IAAI,EAAEA,IAAK;UACXzC,KAAK,EAAE;YAAEuJ,MAAM,EAAE,MAAM;YAAER,KAAK,EAAE;UAAO,CAAE;UACzCS,eAAe,EAAE,IAAK;UACtBC,WAAW,EAAGC,WAAW,IAAK;YAAEhH,MAAM,CAACiH,OAAO,GAAGD,WAAW;UAAE,CAAE;UAAA/J,QAAA,gBAGhE7D,OAAA,CAACG,UAAU;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACdjE,OAAA,CAACd,SAAS;YACR4O,WAAW,EAAC,yFAAyF;YACrGzK,GAAG,EAAC;UAAoD;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,EAEDiI,eAAe,CAAC7L,GAAG,CAAEe,MAAM,IAAK;YAAA,IAAA2M,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;YAC/B;YACA,MAAMC,OAAO,GAAGzN,MAAM,CAACiI,UAAU,KAAK,OAAO,GAAG,GAAGjI,MAAM,CAACmI,IAAI,QAAQ,GAAGnI,MAAM,CAACmI,IAAI;YACpF,MAAMuF,YAAY,GAAGhJ,KAAK,CAAC+I,OAAO,CAAC,IAAI/I,KAAK,CAAC1E,MAAM,CAACmI,IAAI,CAAC;YAEzD,oBACAvJ,OAAA,CAACb,MAAM;cAELoL,QAAQ,EAAEnJ,MAAM,CAACmJ,QAAS;cAC1B5E,IAAI,EAAEmJ,YAAa;cAAAjL,QAAA,eAEnB7D,OAAA,CAACZ,KAAK;gBAACsF,QAAQ,EAAE,GAAI;gBAAAb,QAAA,eACnB7D,OAAA;kBAAK4D,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7D,OAAA;oBAAA6D,QAAA,GAAKzC,MAAM,CAACmI,IAAI,CAACwF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG5N,MAAM,CAACmI,IAAI,CAAC1G,KAAK,CAAC,CAAC,CAAC,EAAC,SAAO;kBAAA;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAG3E7C,MAAM,CAACiI,UAAU,KAAK,OAAO,iBAC5BrJ,OAAA;oBAAK4D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAC9BzC,MAAM,CAAC6N,oBAAoB,gBAC1BjP,OAAA,CAAAE,SAAA;sBAAA2D,QAAA,gBACE7D,OAAA;wBACEuE,GAAG,EAAE,0BAA0BnD,MAAM,CAAC6N,oBAAoB,EAAG;wBAC7DzK,GAAG,EAAC,iBAAiB;wBACrBZ,SAAS,EAAC,oCAAoC;wBAC9CM,KAAK,EAAE;0BAAEO,SAAS,EAAE,OAAO;0BAAEC,QAAQ,EAAE,MAAM;0BAAEwK,SAAS,EAAE;wBAAQ,CAAE;wBACpEvK,OAAO,EAAGpB,CAAC,IAAK;0BACdvB,OAAO,CAACoB,IAAI,CAAC,iDAAiDhC,MAAM,CAACc,QAAQ,EAAE,CAAC;0BAChFqB,CAAC,CAACqJ,MAAM,CAAC1I,KAAK,CAACiL,OAAO,GAAG,MAAM;0BAC/B;0BACA,MAAMC,QAAQ,GAAG7L,CAAC,CAACqJ,MAAM,CAACyC,kBAAkB;0BAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,CAACC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;4BACvEH,QAAQ,CAAClL,KAAK,CAACiL,OAAO,GAAG,OAAO;0BAClC;wBACF;sBAAE;wBAAArL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFjE,OAAA;wBAAK4D,SAAS,EAAC,uEAAuE;wBAACM,KAAK,EAAE;0BAAEiL,OAAO,EAAE;wBAAO,CAAE;wBAAAtL,QAAA,gBAChH7D,OAAA;0BAAG4D,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,gCAClC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,eACN,CAAC,gBAEHjE,OAAA;sBAAK4D,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,gBAC3D7D,OAAA;wBAAG4D,SAAS,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3CjE,OAAA;wBAAA6D,QAAA,EAAK;sBAA6B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CACN,eACDjE,OAAA;sBAAK4D,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnB7D,OAAA;wBAAO4D,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EAC9D7C,MAAM,CAACoO,QAAQ,iBACdxP,OAAA;wBAAA6D,QAAA,eACE7D,OAAA;0BAAO4D,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAAC,YAAU,EAACzC,MAAM,CAACoO,QAAQ;wBAAA;0BAAA1L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA7C,MAAM,CAACiI,UAAU,KAAK,OAAO,iBAC5BrJ,OAAA,CAACmB,uBAAuB;oBAACC,MAAM,EAAEA;kBAAO;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC3C,eAGDjE,OAAA;oBAAK4D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7D,OAAA;sBAAI4D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnDjE,OAAA;sBAAI4D,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBACjC7D,OAAA;wBAAA6D,QAAA,gBAAI7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACoJ,YAAY;sBAAA;wBAAA1G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtDjE,OAAA;wBAAA6D,QAAA,gBAAI7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACqJ,SAAS;sBAAA;wBAAA3G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClDjE,OAAA;wBAAA6D,QAAA,gBAAI7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACyG,QAAQ;sBAAA;wBAAA/D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxDjE,OAAA;wBAAA6D,QAAA,gBAAI7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAW;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACiI,UAAU;sBAAA;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzDjE,OAAA;wBAAA6D,QAAA,gBAAI7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACmJ,QAAQ,CAAC,CAAC,CAAC,CAACkF,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACrO,MAAM,CAACmJ,QAAQ,CAAC,CAAC,CAAC,CAACkF,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAA3L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EAGL7C,MAAM,CAACmI,IAAI,KAAK,OAAO,IAAInI,MAAM,CAACuJ,WAAW,iBAC5C3K,OAAA;oBAAK4D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7D,OAAA;sBAAI4D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7CjE,OAAA;sBAAI4D,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAChC6L,MAAM,CAACC,OAAO,CAACvO,MAAM,CAACuJ,WAAW,CAAC,CAACtK,GAAG,CAAC,CAAC,CAACkJ,IAAI,EAAEqG,KAAK,CAAC,kBACpD5P,OAAA;wBAAA6D,QAAA,gBAAe7D,OAAA;0BAAA6D,QAAA,GAAS0F,IAAI,EAAC,GAAC;wBAAA;0BAAAzF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC2L,KAAK;sBAAA,GAArCrG,IAAI;wBAAAzF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAsC,CACpD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,EAEA7C,MAAM,CAACmI,IAAI,KAAK,MAAM,IAAInI,MAAM,CAACwJ,gBAAgB,iBAChD5K,OAAA;oBAAK4D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7D,OAAA;sBAAI4D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDjE,OAAA;sBAAI4D,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAChC6L,MAAM,CAACC,OAAO,CAACvO,MAAM,CAACwJ,gBAAgB,CAAC,CAACvK,GAAG,CAAC,CAAC,CAACwP,SAAS,EAAED,KAAK,CAAC,kBAC9D5P,OAAA;wBAAA6D,QAAA,gBAAoB7D,OAAA;0BAAA6D,QAAA,GAASgM,SAAS,EAAC,GAAC;wBAAA;0BAAA/L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC2L,KAAK;sBAAA,GAA/CC,SAAS;wBAAA/L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAA2C,CAC9D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,EAGA,CAAC7C,MAAM,CAACuI,SAAS,IAAIvI,MAAM,CAACyJ,QAAQ,kBACnC7K,OAAA;oBAAK4D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7D,OAAA;sBAAI4D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtDjE,OAAA;sBAAK4D,SAAS,EAAC,OAAO;sBAAAC,QAAA,GAEnB,EAAAkK,iBAAA,GAAA3M,MAAM,CAACuI,SAAS,cAAAoE,iBAAA,uBAAhBA,iBAAA,CAAkBnE,eAAe,kBAChC5J,OAAA;wBAAK4D,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBACxC7D,OAAA;0BAAQ4D,SAAS,EAAC,cAAc;0BAAAC,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxDjE,OAAA;0BAAA6D,QAAA,GAAK,OAAK,GAAAmK,qBAAA,GAAC5M,MAAM,CAACuI,SAAS,CAACC,eAAe,CAACC,QAAQ,cAAAmE,qBAAA,uBAAzCA,qBAAA,CAA2CyB,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAA3L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvEjE,OAAA;0BAAA6D,QAAA,GAAK,OAAK,GAAAoK,sBAAA,GAAC7M,MAAM,CAACuI,SAAS,CAACC,eAAe,CAACE,SAAS,cAAAmE,sBAAA,uBAA1CA,sBAAA,CAA4CwB,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAA3L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CACN,EAGA,EAAAiK,kBAAA,GAAA9M,MAAM,CAACuI,SAAS,cAAAuE,kBAAA,uBAAhBA,kBAAA,CAAkB4B,WAAW,KAAIJ,MAAM,CAACK,IAAI,CAAC3O,MAAM,CAACuI,SAAS,CAACmG,WAAW,CAAC,CAACnN,MAAM,GAAG,CAAC,iBACpF3C,OAAA;wBAAK4D,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC3BjE,OAAA;0BAAI4D,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAC/BzC,MAAM,CAACuI,SAAS,CAACmG,WAAW,CAACE,WAAW,iBACvChQ,OAAA;4BAAA6D,QAAA,GAAI,QAAM,EAACzC,MAAM,CAACuI,SAAS,CAACmG,WAAW,CAACE,WAAW;0BAAA;4BAAAlM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACzD,EACA7C,MAAM,CAACuI,SAAS,CAACmG,WAAW,CAACG,YAAY,iBACxCjQ,OAAA;4BAAA6D,QAAA,GAAI,SAAO,EAACzC,MAAM,CAACuI,SAAS,CAACmG,WAAW,CAACG,YAAY;0BAAA;4BAAAnM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAC3D;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN,EAGA,EAAAkK,kBAAA,GAAA/M,MAAM,CAACuI,SAAS,cAAAwE,kBAAA,uBAAhBA,kBAAA,CAAkB+B,cAAc,KAAIR,MAAM,CAACK,IAAI,CAAC3O,MAAM,CAACuI,SAAS,CAACuG,cAAc,CAAC,CAACvN,MAAM,GAAG,CAAC,iBAC1F3C,OAAA;wBAAK4D,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC9BjE,OAAA;0BAAI4D,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAC/BzC,MAAM,CAACuI,SAAS,CAACuG,cAAc,CAACC,GAAG,iBAClCnQ,OAAA;4BAAA6D,QAAA,GAAI,OAAK,EAACzC,MAAM,CAACuI,SAAS,CAACuG,cAAc,CAACC,GAAG;0BAAA;4BAAArM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACnD,EACA7C,MAAM,CAACuI,SAAS,CAACuG,cAAc,CAACE,aAAa,iBAC5CpQ,OAAA;4BAAA6D,QAAA,GAAI,YAAU,EAACzC,MAAM,CAACuI,SAAS,CAACuG,cAAc,CAACE,aAAa;0BAAA;4BAAAtM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAClE;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN,EAGA,EAAAmK,kBAAA,GAAAhN,MAAM,CAACuI,SAAS,cAAAyE,kBAAA,uBAAhBA,kBAAA,CAAkBiC,UAAU,kBAC3BrQ,OAAA;wBAAK4D,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB7D,OAAA;0BAAA6D,QAAA,EAAQ;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACuI,SAAS,CAAC0G,UAAU,CAACpD,KAAK,EAAC,QAAG,EAAC7L,MAAM,CAACuI,SAAS,CAAC0G,UAAU,CAAC5C,MAAM,EACxGrM,MAAM,CAACuI,SAAS,CAAC0G,UAAU,CAACC,MAAM,iBACjCtQ,OAAA;0BAAA6D,QAAA,GAAM,IAAE,EAACzC,MAAM,CAACuI,SAAS,CAAC0G,UAAU,CAACC,MAAM,EAAC,GAAC;wBAAA;0BAAAxM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACpD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CACN,EAGA7C,MAAM,CAACiI,UAAU,KAAK,OAAO,iBAC5BrJ,OAAA;wBAAK4D,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnB7D,OAAA;0BAAI4D,SAAS,EAAC,WAAW;0BAAAC,QAAA,EAAC;wBAAoB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnDjE,OAAA;0BAAI4D,SAAS,EAAC,qBAAqB;0BAAAC,QAAA,GAChC,EAAAwK,gBAAA,GAAAjN,MAAM,CAACyJ,QAAQ,cAAAwD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBkC,WAAW,cAAAjC,qBAAA,uBAA5BA,qBAAA,CAA8BkC,QAAQ,kBACrCxQ,OAAA;4BAAA6D,QAAA,gBAAI7D,OAAA;8BAAA6D,QAAA,EAAQ;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACoE,IAAI,CAACoI,KAAK,CAACrP,MAAM,CAACyJ,QAAQ,CAAC0F,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;0BAAA;4BAAA1M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACvF,EACA,EAAAsK,iBAAA,GAAAnN,MAAM,CAACyJ,QAAQ,cAAA0D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB8B,UAAU,cAAA7B,qBAAA,uBAA3BA,qBAAA,CAA6BvB,KAAK,OAAAwB,iBAAA,GAAIrN,MAAM,CAACyJ,QAAQ,cAAA4D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB4B,UAAU,cAAA3B,qBAAA,uBAA3BA,qBAAA,CAA6BjB,MAAM,kBACxEzN,OAAA;4BAAA6D,QAAA,gBAAI7D,OAAA;8BAAA6D,QAAA,EAAQ;4BAAW;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACyJ,QAAQ,CAACwF,UAAU,CAACpD,KAAK,EAAC,GAAC,EAAC7L,MAAM,CAACyJ,QAAQ,CAACwF,UAAU,CAAC5C,MAAM;0BAAA;4BAAA3J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAC5G,EACA,EAAA0K,iBAAA,GAAAvN,MAAM,CAACyJ,QAAQ,cAAA8D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB4B,WAAW,cAAA3B,qBAAA,uBAA5BA,qBAAA,CAA8B8B,WAAW,kBACxC1Q,OAAA;4BAAA6D,QAAA,gBAAI7D,OAAA;8BAAA6D,QAAA,EAAQ;4BAAO;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACyJ,QAAQ,CAAC0F,WAAW,CAACG,WAAW,CAAC1B,WAAW,CAAC,CAAC;0BAAA;4BAAAlL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACzF,EACA7C,MAAM,CAACoO,QAAQ,iBACdxP,OAAA;4BAAA6D,QAAA,gBAAI7D,OAAA;8BAAA6D,QAAA,EAAQ;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACoO,QAAQ;0BAAA;4BAAA1L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACrD,EAEA7C,MAAM,CAACuP,aAAa,iBACnB3Q,OAAA,CAAAE,SAAA;4BAAA2D,QAAA,GACGzC,MAAM,CAACuP,aAAa,CAACC,QAAQ,IAAIxP,MAAM,CAACuP,aAAa,CAACC,QAAQ,CAACjO,MAAM,GAAG,CAAC,iBACxE3C,OAAA;8BAAA6D,QAAA,gBAAI7D,OAAA;gCAAA6D,QAAA,EAAQ;8BAAkB;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACuP,aAAa,CAACC,QAAQ,CAACjO,MAAM;4BAAA;8BAAAmB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACnF,EACA7C,MAAM,CAACuP,aAAa,CAACE,MAAM,IAAIzP,MAAM,CAACuP,aAAa,CAACE,MAAM,CAAClO,MAAM,GAAG,CAAC,iBACpE3C,OAAA;8BAAA6D,QAAA,gBAAI7D,OAAA;gCAAA6D,QAAA,EAAQ;8BAAgB;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACuP,aAAa,CAACE,MAAM,CAAClO,MAAM;4BAAA;8BAAAmB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC/E,EACA7C,MAAM,CAACuP,aAAa,CAACG,KAAK,IAAI1P,MAAM,CAACuP,aAAa,CAACG,KAAK,CAACnO,MAAM,GAAG,CAAC,iBAClE3C,OAAA;8BAAA6D,QAAA,gBAAI7D,OAAA;gCAAA6D,QAAA,EAAQ;8BAAe;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC7C,MAAM,CAACuP,aAAa,CAACG,KAAK,CAACnO,MAAM;4BAAA;8BAAAmB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC7E;0BAAA,eACD,CACH;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGDjE,OAAA;oBAAK4D,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B7D,OAAA,CAACF,IAAI;sBACHiR,EAAE,EAAE,SAAS3P,MAAM,CAACc,QAAQ,EAAG;sBAC/B0B,SAAS,EAAC,wBAAwB;sBAClC0J,OAAO,EAAG/J,CAAC,IAAKA,CAAC,CAACyN,eAAe,CAAC,CAAE;sBAAAnN,QAAA,EACrC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAEN7C,MAAM,CAACiI,UAAU,KAAK,OAAO,IAAIjI,MAAM,CAACgB,uBAAuB,iBAC9DpC,OAAA;sBACEiR,IAAI,EAAE7P,MAAM,CAACgB,uBAAwB;sBACrCwK,MAAM,EAAC,QAAQ;sBACfsE,GAAG,EAAC,qBAAqB;sBACzBtN,SAAS,EAAC,kCAAkC;sBAC5C0J,OAAO,EAAG/J,CAAC,IAAKA,CAAC,CAACyN,eAAe,CAAC,CAAE;sBAAAnN,QAAA,EACrC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GA5MH7C,MAAM,CAACiJ,EAAE;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6MR,CAAC;UAEX,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX;AAACmC,GAAA,CAroBQF,SAAS;AAAAiL,GAAA,GAATjL,SAAS;AAuoBlB,eAAeA,SAAS;AAAC,IAAAhF,EAAA,EAAA2D,GAAA,EAAAsM,GAAA;AAAAC,YAAA,CAAAlQ,EAAA;AAAAkQ,YAAA,CAAAvM,GAAA;AAAAuM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}