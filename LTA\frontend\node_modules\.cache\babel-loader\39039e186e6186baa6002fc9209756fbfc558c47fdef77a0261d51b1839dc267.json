{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\DefectMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, useMap } from 'react-leaflet';\nimport axios from 'axios';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\n// Error Boundary for Map Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass MapErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('Map Error Boundary caught an error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Map Loading Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"There was an issue loading the map. Please refresh the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm\",\n          onClick: () => window.location.reload(),\n          children: \"Refresh Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// Helper component to invalidate map size after mount and on tile load\nfunction MapSizeFix({\n  mapRef\n}) {\n  _s();\n  const map = useMap();\n  useEffect(() => {\n    // Store map reference for parent component\n    if (mapRef) {\n      mapRef.current = map;\n    }\n\n    // Invalidate size shortly after mount with better error handling\n    const t1 = setTimeout(() => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map invalidateSize error (t1):', error);\n      }\n    }, 100);\n    const t2 = setTimeout(() => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map invalidateSize error (t2):', error);\n      }\n    }, 500);\n\n    // Also on window resize with error handling\n    const onResize = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map resize error:', error);\n      }\n    };\n    window.addEventListener('resize', onResize);\n\n    // Invalidate after zoom animations end with error handling\n    const onZoomEnd = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map zoom end error:', error);\n      }\n    };\n    const onMoveEnd = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map move end error:', error);\n      }\n    };\n    if (map) {\n      map.on('zoomend', onZoomEnd);\n      map.on('moveend', onMoveEnd);\n    }\n    return () => {\n      clearTimeout(t1);\n      clearTimeout(t2);\n      window.removeEventListener('resize', onResize);\n      if (map) {\n        try {\n          map.off('zoomend', onZoomEnd);\n          map.off('moveend', onMoveEnd);\n        } catch (error) {\n          console.warn('Map cleanup error:', error);\n        }\n      }\n    };\n  }, [map, mapRef]);\n  return null;\n}\n\n/**\r\n * Image URL Resolution Logic - Same as Dashboard\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n */\n_s(MapSizeFix, \"IoceErwr5KVGS9kN4RQ1bOkYMAg=\", false, function () {\n  return [useMap];\n});\n_c = MapSizeFix;\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\n  console.log('🔍 Map getImageUrlForDisplay called:', {\n    imageData,\n    imageType\n  });\n  if (!imageData) {\n    console.log('❌ No imageData provided');\n    return null;\n  }\n\n  // Check if this is video data with representative frame\n  if (imageData.media_type === 'video' && imageData.representative_frame) {\n    console.log('📹 Using representative frame for video data');\n    return `data:image/jpeg;base64,${imageData.representative_frame}`;\n  }\n\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\n  const fullUrlField = `${imageType}_image_full_url`;\n  if (imageData[fullUrlField]) {\n    console.log('🔗 Using full URL field:', fullUrlField, imageData[fullUrlField]);\n    // Extract S3 key from full URL and use proxy endpoint\n    const urlParts = imageData[fullUrlField].split('/');\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n      console.log('✅ Generated proxy URL from full URL:', proxyUrl);\n      return proxyUrl;\n    }\n  }\n\n  // Try S3 key with proxy endpoint (new images)\n  const s3KeyField = `${imageType}_image_s3_url`;\n  if (imageData[s3KeyField]) {\n    console.log('🔗 Using S3 key field:', s3KeyField, imageData[s3KeyField]);\n    const s3Key = imageData[s3KeyField];\n    const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n    console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\n    return proxyUrl;\n  }\n\n  // Try GridFS endpoint (legacy images)\n  const gridfsIdField = `${imageType}_image_id`;\n  if (imageData[gridfsIdField]) {\n    console.log('🗄️ Using GridFS endpoint:', gridfsIdField, imageData[gridfsIdField]);\n    return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\n  }\n  console.warn('❌ No valid image URL found for:', imageType, imageData.image_id);\n  return null;\n};\n\n/**\r\n * Enhanced Image Display Component - Same as Dashboard\r\n * Supports both original and processed images with toggle\r\n */\nconst EnhancedMapImageDisplay = ({\n  defect\n}) => {\n  _s2();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n  const [isOriginal, setIsOriginal] = useState(false); // Start with processed image\n\n  useEffect(() => {\n    // Reset state when defect or image type changes\n    setHasError(false);\n    setIsLoading(true);\n    setFallbackAttempts(0);\n\n    // Get image URL using same logic as Dashboard\n    const imageUrl = getImageUrlForDisplay(defect, isOriginal ? 'original' : 'processed');\n    console.log('🖼️ Map Enhanced Image Display Debug:', {\n      imageType: isOriginal ? 'original' : 'processed',\n      imageId: defect === null || defect === void 0 ? void 0 : defect.image_id,\n      generatedUrl: imageUrl,\n      s3Key: defect === null || defect === void 0 ? void 0 : defect[`${isOriginal ? 'original' : 'processed'}_image_s3_url`],\n      fullUrl: defect === null || defect === void 0 ? void 0 : defect[`${isOriginal ? 'original' : 'processed'}_image_full_url`],\n      gridfsId: defect === null || defect === void 0 ? void 0 : defect[`${isOriginal ? 'original' : 'processed'}_image_id`]\n    });\n    setCurrentImageUrl(imageUrl);\n  }, [defect, isOriginal]);\n  const getFallbackImageUrl = (imageData, imageType) => {\n    console.log('🔄 Getting fallback URL for:', imageType, imageData);\n\n    // Try direct S3 URL if we have the full URL field\n    const fullUrlField = `${imageType}_image_full_url`;\n    if (imageData[fullUrlField]) {\n      console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\n      return imageData[fullUrlField];\n    }\n\n    // Try GridFS if S3 failed\n    const gridfsIdField = `${imageType}_image_id`;\n    if (imageData[gridfsIdField]) {\n      console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\n      return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\n    }\n\n    // Try alternative S3 proxy with different encoding\n    const s3KeyField = `${imageType}_image_s3_url`;\n    if (imageData[s3KeyField]) {\n      console.log('🔄 Trying alternative S3 proxy encoding');\n      const s3Key = imageData[s3KeyField];\n      const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n      console.log('🔄 Alternative proxy URL:', alternativeUrl);\n      return alternativeUrl;\n    }\n    console.log('❌ No fallback URL available');\n    return null;\n  };\n  const handleImageError = () => {\n    console.error('❌ Map image loading failed:', currentImageUrl);\n    setIsLoading(false);\n\n    // Try fallback URLs\n    if (fallbackAttempts === 0) {\n      const currentImageType = isOriginal ? 'original' : 'processed';\n      const fallbackUrl = getFallbackImageUrl(defect, currentImageType);\n      if (fallbackUrl && fallbackUrl !== currentImageUrl) {\n        console.log('🔄 Trying fallback URL:', fallbackUrl);\n        setCurrentImageUrl(fallbackUrl);\n        setFallbackAttempts(1);\n        return;\n      }\n    }\n    if (fallbackAttempts === 1) {\n      // Second fallback: try alternative image type\n      const alternativeType = isOriginal ? 'processed' : 'original';\n      const alternativeUrl = getImageUrlForDisplay(defect, alternativeType);\n      if (alternativeUrl && alternativeUrl !== currentImageUrl) {\n        console.log('🔄 Trying alternative image type:', alternativeType);\n        setCurrentImageUrl(alternativeUrl);\n        setFallbackAttempts(2);\n        return;\n      }\n    }\n\n    // All fallbacks exhausted\n    console.error('❌ All fallbacks exhausted for map image');\n    setHasError(true);\n  };\n\n  // Don't render anything if no URL available\n  if (!currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), \" Image not available\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          minHeight: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          variant: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2 small\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render error state\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small p-2 border rounded bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), \" Image unavailable\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render successful image\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-3 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: \"Defect image\",\n      className: \"img-fluid border rounded\",\n      style: {\n        maxHeight: '150px',\n        maxWidth: '100%'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-primary fw-bold\",\n        children: \"\\uD83D\\uDCF7 Original Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), fallbackAttempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-warning\",\n          children: \"(Fallback source)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 320,\n    columnNumber: 5\n  }, this);\n};\n\n// Fix for the Leaflet default icon issue\n_s2(EnhancedMapImageDisplay, \"8olssMCAXG7H0c82dX7Ck1C5W44=\");\n_c2 = EnhancedMapImageDisplay;\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Custom marker icons for different defect types using location icons\nconst createCustomIcon = (color, isVideo = false) => {\n  const iconSize = isVideo ? [36, 36] : [32, 32];\n\n  // Create different SVG content for video vs image markers\n  const svgContent = isVideo ?\n  // Video marker with camera icon made from SVG shapes (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\n    </svg>` :\n  // Image marker with standard location pin (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>`;\n  return L.icon({\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\n    iconSize: iconSize,\n    iconAnchor: [iconSize[0] / 2, iconSize[1]],\n    popupAnchor: [0, -iconSize[1]]\n  });\n};\nconst icons = {\n  pothole: createCustomIcon('#FF0000'),\n  // Red for potholes\n  crack: createCustomIcon('#FFCC00'),\n  // Yellow for cracks (alligator cracks)\n  kerb: createCustomIcon('#0066FF'),\n  // Blue for kerb defects\n  // Video variants\n  'pothole-video': createCustomIcon('#FF0000', true),\n  // Red for pothole videos\n  'crack-video': createCustomIcon('#FFCC00', true),\n  // Yellow for crack videos\n  'kerb-video': createCustomIcon('#0066FF', true) // Blue for kerb videos\n};\nfunction DefectMap({\n  user\n}) {\n  _s3();\n  const [defects, setDefects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [center] = useState([20.5937, 78.9629]); // India center\n  const [zoom] = useState(6); // Country-wide zoom for India\n  const mapRef = useRef(null);\n\n  // Filters for the map\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [usersList, setUsersList] = useState([]);\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\n    pothole: true,\n    crack: true,\n    kerb: true\n  });\n\n  // Fetch all images with defects and coordinates\n  const fetchDefectData = async (forceRefresh = false) => {\n    try {\n      var _response$data$images;\n      setLoading(true);\n      setError(null); // Clear any previous errors\n\n      // Prepare query parameters\n      let params = {};\n      if (startDate) params.start_date = startDate;\n      if (endDate) params.end_date = endDate;\n      if (selectedUser) params.username = selectedUser;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Always add cache-busting parameter to ensure latest data\n      params._t = Date.now();\n\n      // Add additional cache-busting for force refresh\n      if (forceRefresh) {\n        params._force = 'true';\n        params._refresh = Math.random().toString(36).substring(7);\n      }\n      console.log('🔄 Fetching defect data with params:', params);\n      const response = await axios.get('/api/dashboard/image-stats', {\n        params,\n        // Disable axios caching\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      console.log('📊 API response received:', {\n        success: response.data.success,\n        totalImages: response.data.total_images,\n        imageCount: (_response$data$images = response.data.images) === null || _response$data$images === void 0 ? void 0 : _response$data$images.length\n      });\n      if (response.data.success) {\n        // Process the data to extract defect locations with type information\n        const processedDefects = [];\n        response.data.images.forEach(image => {\n          try {\n            // Only process images with valid coordinates\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\n            if (image.coordinates && image.coordinates !== 'Not Available') {\n              var _image$exif_data;\n              let lat, lng;\n\n              // First, try to use EXIF GPS coordinates if available (most accurate)\n              if ((_image$exif_data = image.exif_data) !== null && _image$exif_data !== void 0 && _image$exif_data.gps_coordinates) {\n                lat = image.exif_data.gps_coordinates.latitude;\n                lng = image.exif_data.gps_coordinates.longitude;\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              } else {\n                // Fallback to stored coordinates\n                // Handle different coordinate formats\n                if (typeof image.coordinates === 'string') {\n                  // Parse coordinates (expected format: \"lat,lng\")\n                  const coords = image.coordinates.split(',');\n                  if (coords.length === 2) {\n                    lat = parseFloat(coords[0].trim());\n                    lng = parseFloat(coords[1].trim());\n                  }\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\n                  // Handle array format [lat, lng]\n                  lat = parseFloat(image.coordinates[0]);\n                  lng = parseFloat(image.coordinates[1]);\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\n                  // Handle object format {lat: x, lng: y}\n                  lat = parseFloat(image.coordinates.lat);\n                  lng = parseFloat(image.coordinates.lng);\n                }\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              }\n\n              // Validate coordinates are within reasonable bounds\n              if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\n                processedDefects.push({\n                  id: image.id,\n                  image_id: image.image_id,\n                  type: image.type,\n                  position: [lat, lng],\n                  defect_count: image.defect_count,\n                  timestamp: new Date(image.timestamp).toLocaleString(),\n                  username: image.username,\n                  original_image_id: image.original_image_id,\n                  // For cracks, include type information if available\n                  type_counts: image.type_counts,\n                  // For kerbs, include condition information if available\n                  condition_counts: image.condition_counts,\n                  // EXIF and metadata information\n                  exif_data: image.exif_data || {},\n                  metadata: image.metadata || {},\n                  media_type: image.media_type || 'image',\n                  original_image_full_url: image.original_image_full_url\n                });\n              } else {\n                console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\n              }\n            } else {\n              console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\n            }\n          } catch (coordError) {\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\n          }\n        });\n        console.log('Processed defects:', processedDefects.length);\n        setDefects(processedDefects);\n\n        // If no defects were processed, show a helpful message\n        if (processedDefects.length === 0) {\n          setError('No defects found with valid coordinates for the selected date range');\n        }\n      } else {\n        console.error('API returned success: false', response.data);\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\n      }\n      setLoading(false);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error fetching defect data:', err);\n      setError('Failed to load defect data: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n      setLoading(false);\n    }\n  };\n\n  // Fetch the list of users for the filter dropdown\n  const fetchUsers = async () => {\n    try {\n      const params = {};\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n      const response = await axios.get('/api/users/all', {\n        params\n      });\n      if (response.data.success) {\n        setUsersList(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  // Initialize default dates for the last 30 days\n  useEffect(() => {\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    console.log('DefectMap component initialized');\n  }, []);\n\n  // Fetch data when component mounts and when filters change\n  useEffect(() => {\n    fetchDefectData();\n    fetchUsers();\n  }, [user]);\n\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\n  useEffect(() => {\n    const interval = setInterval(() => {\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\n      fetchDefectData(true); // Force refresh to get latest EXIF data\n    }, 30000); // 30 seconds\n\n    return () => {\n      console.log('🛑 Clearing auto-refresh interval');\n      clearInterval(interval);\n    };\n  }, [startDate, endDate, selectedUser, user === null || user === void 0 ? void 0 : user.role]);\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    fetchDefectData(true);\n  };\n\n  // Handle filter application\n  const handleApplyFilters = () => {\n    fetchDefectData();\n  };\n\n  // Handle resetting filters\n  const handleResetFilters = () => {\n    // Reset date filters to last 30 days\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    setSelectedUser('');\n    setDefectTypeFilters({\n      pothole: true,\n      crack: true,\n      kerb: true\n    });\n\n    // Refetch data with reset filters\n    fetchDefectData();\n  };\n\n  // Handle defect type filter changes\n  const handleDefectTypeFilterChange = type => {\n    setDefectTypeFilters(prevFilters => ({\n      ...prevFilters,\n      [type]: !prevFilters[type]\n    }));\n  };\n\n  // Filter defects based on applied filters\n  const filteredDefects = defects.filter(defect => defectTypeFilters[defect.type]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"shadow-sm dashboard-card mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-0\",\n        children: \"Defect Map View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Date Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: startDate,\n                    onChange: e => setStartDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: endDate,\n                    onChange: e => setEndDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"User Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Select User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedUser,\n                    onChange: e => setSelectedUser(e.target.value),\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 23\n                    }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: user.username,\n                      children: [user.username, \" (\", user.role, \")\"]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Defect Type Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls defect-type-filters\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"defect-type-filter-options\",\n              style: {\n                marginTop: \"0\",\n                paddingLeft: \"0\",\n                width: \"100%\",\n                minWidth: \"150px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"pothole-filter\",\n                label: \"Potholes\",\n                checked: defectTypeFilters.pothole,\n                onChange: () => handleDefectTypeFilterChange('pothole'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"crack-filter\",\n                label: \"Cracks\",\n                checked: defectTypeFilters.crack,\n                onChange: () => handleDefectTypeFilterChange('crack'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"kerb-filter\",\n                label: \"Kerbs\",\n                checked: defectTypeFilters.kerb,\n                onChange: () => handleDefectTypeFilterChange('kerb'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-actions-container\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              onClick: handleApplyFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Apply Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: handleResetFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              onClick: handleRefresh,\n              disabled: loading,\n              className: \"filter-btn\",\n              children: loading ? '🔄 Refreshing...' : '🔄 Refresh Map'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF7 Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Defects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF9 Videos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pothole Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Crack Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"map-container\",\n        style: {\n          height: '500px',\n          width: '100%',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(MapErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(MapContainer, {\n            center: center,\n            zoom: zoom,\n            style: {\n              height: '100%',\n              width: '100%'\n            },\n            scrollWheelZoom: true,\n            children: [/*#__PURE__*/_jsxDEV(MapSizeFix, {\n              mapRef: mapRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TileLayer, {\n              attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n              url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), filteredDefects.map(defect => {\n              var _defect$exif_data, _defect$exif_data$gps, _defect$exif_data$gps2, _defect$exif_data2, _defect$exif_data3, _defect$exif_data4, _defect$metadata, _defect$metadata$form, _defect$metadata2, _defect$metadata2$bas, _defect$metadata3, _defect$metadata3$bas, _defect$metadata4, _defect$metadata4$for;\n              // Defensive programming: ensure defect has required properties\n              if (!defect || !defect.position || !Array.isArray(defect.position) || defect.position.length !== 2) {\n                console.warn('Invalid defect data:', defect);\n                return null;\n              }\n\n              // Determine icon based on media type and defect type\n              const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\n              const selectedIcon = icons[iconKey] || icons[defect.type] || icons['pothole']; // fallback icon\n\n              return /*#__PURE__*/_jsxDEV(Marker, {\n                position: defect.position,\n                icon: selectedIcon,\n                children: /*#__PURE__*/_jsxDEV(Popup, {\n                  maxWidth: 400,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"defect-popup\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: [defect.type.charAt(0).toUpperCase() + defect.type.slice(1), \" Defect\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 23\n                    }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 text-center\",\n                      children: [defect.representative_frame ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: `data:image/jpeg;base64,${defect.representative_frame}`,\n                          alt: \"Video thumbnail\",\n                          className: \"img-fluid border rounded shadow-sm\",\n                          style: {\n                            maxHeight: '150px',\n                            maxWidth: '100%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            console.warn(`Failed to load representative frame for video ${defect.image_id}`);\n                            e.target.style.display = 'none';\n                            // Show fallback message\n                            const fallback = e.target.nextElementSibling;\n                            if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\n                              fallback.style.display = 'block';\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 838,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\",\n                          style: {\n                            display: 'none'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-video\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 854,\n                            columnNumber: 33\n                          }, this), \" Video thumbnail unavailable\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 853,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted small p-3 border rounded bg-light\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-video fa-2x mb-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 859,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: \"Video thumbnail not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 860,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-info fw-bold\",\n                          children: \"\\uD83D\\uDCF9 Video Thumbnail\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 864,\n                          columnNumber: 29\n                        }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted d-block\",\n                            children: [\"Video ID: \", defect.video_id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 867,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 866,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 863,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 25\n                    }, this), defect.media_type !== 'video' && /*#__PURE__*/_jsxDEV(EnhancedMapImageDisplay, {\n                      defect: defect\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Basic Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 881,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Count:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 883,\n                            columnNumber: 31\n                          }, this), \" \", defect.defect_count]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 883,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Date:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 884,\n                            columnNumber: 31\n                          }, this), \" \", defect.timestamp]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 884,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Reported by:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 885,\n                            columnNumber: 31\n                          }, this), \" \", defect.username]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 885,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Media Type:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 886,\n                            columnNumber: 31\n                          }, this), \" \", defect.media_type]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 886,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"GPS:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 887,\n                            columnNumber: 31\n                          }, this), \" \", defect.position[0].toFixed(6), \", \", defect.position[1].toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 887,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 882,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 23\n                    }, this), defect.type === 'crack' && defect.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Crack Types\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 894,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: Object.entries(defect.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [type, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 897,\n                            columnNumber: 46\n                          }, this), \" \", count]\n                        }, type, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 897,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 895,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 893,\n                      columnNumber: 25\n                    }, this), defect.type === 'kerb' && defect.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Kerb Conditions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 905,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: Object.entries(defect.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [condition, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 908,\n                            columnNumber: 51\n                          }, this), \" \", count]\n                        }, condition, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 908,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 906,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 904,\n                      columnNumber: 25\n                    }, this), (defect.exif_data || defect.metadata) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"\\uD83D\\uDCCA Media Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 917,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"small\",\n                        children: [((_defect$exif_data = defect.exif_data) === null || _defect$exif_data === void 0 ? void 0 : _defect$exif_data.gps_coordinates) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2 p-2 bg-light rounded\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"text-success\",\n                            children: \"\\uD83C\\uDF0D GPS (EXIF):\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 922,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Lat: \", (_defect$exif_data$gps = defect.exif_data.gps_coordinates.latitude) === null || _defect$exif_data$gps === void 0 ? void 0 : _defect$exif_data$gps.toFixed(6)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 923,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Lng: \", (_defect$exif_data$gps2 = defect.exif_data.gps_coordinates.longitude) === null || _defect$exif_data$gps2 === void 0 ? void 0 : _defect$exif_data$gps2.toFixed(6)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 924,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 921,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data2 = defect.exif_data) === null || _defect$exif_data2 === void 0 ? void 0 : _defect$exif_data2.camera_info) && Object.keys(defect.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\uD83D\\uDCF7 Camera:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 931,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled ms-2\",\n                            children: [defect.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Make: \", defect.exif_data.camera_info.camera_make]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 934,\n                              columnNumber: 37\n                            }, this), defect.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Model: \", defect.exif_data.camera_info.camera_model]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 937,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 932,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 930,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data3 = defect.exif_data) === null || _defect$exif_data3 === void 0 ? void 0 : _defect$exif_data3.technical_info) && Object.keys(defect.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u2699\\uFE0F Technical:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 946,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled ms-2\",\n                            children: [defect.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"ISO: \", defect.exif_data.technical_info.iso]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 949,\n                              columnNumber: 37\n                            }, this), defect.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Exposure: \", defect.exif_data.technical_info.exposure_time]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 952,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 947,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 945,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data4 = defect.exif_data) === null || _defect$exif_data4 === void 0 ? void 0 : _defect$exif_data4.basic_info) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\uD83D\\uDCD0 Dimensions:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 961,\n                            columnNumber: 33\n                          }, this), \" \", defect.exif_data.basic_info.width, \" \\xD7 \", defect.exif_data.basic_info.height, defect.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [\" (\", defect.exif_data.basic_info.format, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 963,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 960,\n                          columnNumber: 31\n                        }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"text-info\",\n                            children: \"\\uD83D\\uDCF9 Video Information\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 971,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled small\",\n                            children: [((_defect$metadata = defect.metadata) === null || _defect$metadata === void 0 ? void 0 : (_defect$metadata$form = _defect$metadata.format_info) === null || _defect$metadata$form === void 0 ? void 0 : _defect$metadata$form.duration) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Duration:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 974,\n                                columnNumber: 41\n                              }, this), \" \", Math.round(defect.metadata.format_info.duration), \"s\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 974,\n                              columnNumber: 37\n                            }, this), ((_defect$metadata2 = defect.metadata) === null || _defect$metadata2 === void 0 ? void 0 : (_defect$metadata2$bas = _defect$metadata2.basic_info) === null || _defect$metadata2$bas === void 0 ? void 0 : _defect$metadata2$bas.width) && ((_defect$metadata3 = defect.metadata) === null || _defect$metadata3 === void 0 ? void 0 : (_defect$metadata3$bas = _defect$metadata3.basic_info) === null || _defect$metadata3$bas === void 0 ? void 0 : _defect$metadata3$bas.height) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Resolution:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 977,\n                                columnNumber: 41\n                              }, this), \" \", defect.metadata.basic_info.width, \"x\", defect.metadata.basic_info.height]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 977,\n                              columnNumber: 37\n                            }, this), ((_defect$metadata4 = defect.metadata) === null || _defect$metadata4 === void 0 ? void 0 : (_defect$metadata4$for = _defect$metadata4.format_info) === null || _defect$metadata4$for === void 0 ? void 0 : _defect$metadata4$for.format_name) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Format:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 980,\n                                columnNumber: 41\n                              }, this), \" \", defect.metadata.format_info.format_name.toUpperCase()]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 980,\n                              columnNumber: 37\n                            }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Video ID:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 983,\n                                columnNumber: 41\n                              }, this), \" \", defect.video_id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 983,\n                              columnNumber: 37\n                            }, this), defect.model_outputs && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Potholes Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 989,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.potholes.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 989,\n                                columnNumber: 41\n                              }, this), defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Cracks Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 992,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.cracks.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 992,\n                                columnNumber: 41\n                              }, this), defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Kerbs Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 995,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.kerbs.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 995,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 972,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 970,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 918,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 916,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        to: `/view/${defect.image_id}`,\n                        className: \"btn btn-sm btn-primary\",\n                        onClick: e => e.stopPropagation(),\n                        children: \"View Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1008,\n                        columnNumber: 25\n                      }, this), defect.media_type !== 'video' && defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: defect.original_image_full_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"btn btn-sm btn-outline-secondary\",\n                        onClick: e => e.stopPropagation(),\n                        children: \"View Original\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1017,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1007,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this)\n              }, defect.id || `defect-${Math.random()}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this);\n            })]\n          }, `map-${center[0]}-${center[1]}-${zoom}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 626,\n    columnNumber: 5\n  }, this);\n}\n_s3(DefectMap, \"va2KsqkWO8jHrIyKplfdw8m/S68=\");\n_c3 = DefectMap;\nexport default DefectMap;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MapSizeFix\");\n$RefreshReg$(_c2, \"EnhancedMapImageDisplay\");\n$RefreshReg$(_c3, \"DefectMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "useMap", "axios", "L", "Card", "Row", "Col", "Form", "<PERSON><PERSON>", "Spinner", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MapErrorBoundary", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "MapSizeFix", "mapRef", "_s", "map", "current", "t1", "setTimeout", "invalidateSize", "warn", "t2", "onResize", "addEventListener", "onZoomEnd", "onMoveEnd", "on", "clearTimeout", "removeEventListener", "off", "_c", "getImageUrlForDisplay", "imageData", "imageType", "log", "media_type", "representative_frame", "fullUrlField", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "length", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "s3KeyField", "gridfsIdField", "image_id", "EnhancedMapImageDisplay", "defect", "_s2", "currentImageUrl", "setCurrentImageUrl", "setHasError", "isLoading", "setIsLoading", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "isOriginal", "setIsOriginal", "imageUrl", "imageId", "generatedUrl", "fullUrl", "gridfsId", "getFallbackImageUrl", "alternativeUrl", "handleImageError", "currentImageType", "fallbackUrl", "alternativeType", "style", "minHeight", "animation", "size", "variant", "src", "alt", "maxHeight", "max<PERSON><PERSON><PERSON>", "onError", "onLoad", "handleImageLoad", "_c2", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createCustomIcon", "color", "isVideo", "iconSize", "svgContent", "icon", "iconAnchor", "popupAnchor", "icons", "pothole", "crack", "kerb", "DefectMap", "user", "_s3", "defects", "setDefects", "loading", "setLoading", "setError", "center", "zoom", "startDate", "setStartDate", "endDate", "setEndDate", "selected<PERSON>ser", "setSelectedUser", "usersList", "setUsersList", "defectTypeFilters", "setDefectTypeFilters", "fetchDefectData", "forceRefresh", "_response$data$images", "params", "start_date", "end_date", "username", "role", "user_role", "_t", "Date", "now", "_force", "_refresh", "Math", "random", "toString", "substring", "response", "get", "headers", "success", "data", "totalImages", "total_images", "imageCount", "images", "processedDefects", "for<PERSON>ach", "image", "coordinates", "type", "_image$exif_data", "lat", "lng", "exif_data", "gps_coordinates", "latitude", "longitude", "coords", "parseFloat", "trim", "Array", "isArray", "isNaN", "id", "push", "position", "defect_count", "timestamp", "toLocaleString", "original_image_id", "type_counts", "condition_counts", "metadata", "original_image_full_url", "coordError", "message", "err", "_err$response", "_err$response$data", "fetchUsers", "users", "currentDate", "thirtyDaysAgo", "setDate", "getDate", "toISOString", "interval", "setInterval", "clearInterval", "handleRefresh", "handleApplyFilters", "handleResetFilters", "handleDefectTypeFilterChange", "prevFilters", "filteredDefects", "filter", "Header", "Body", "lg", "Group", "Label", "Control", "value", "onChange", "e", "target", "Select", "index", "marginTop", "paddingLeft", "width", "min<PERSON><PERSON><PERSON>", "Check", "label", "checked", "disabled", "backgroundColor", "height", "scrollWheelZoom", "attribution", "url", "_defect$exif_data", "_defect$exif_data$gps", "_defect$exif_data$gps2", "_defect$exif_data2", "_defect$exif_data3", "_defect$exif_data4", "_defect$metadata", "_defect$metadata$form", "_defect$metadata2", "_defect$metadata2$bas", "_defect$metadata3", "_defect$metadata3$bas", "_defect$metadata4", "_defect$metadata4$for", "<PERSON><PERSON><PERSON>", "selectedIcon", "char<PERSON>t", "toUpperCase", "objectFit", "display", "fallback", "nextElement<PERSON><PERSON>ling", "classList", "contains", "video_id", "toFixed", "Object", "entries", "count", "condition", "camera_info", "keys", "camera_make", "camera_model", "technical_info", "iso", "exposure_time", "basic_info", "format", "format_info", "duration", "round", "format_name", "model_outputs", "potholes", "cracks", "kerbs", "to", "stopPropagation", "href", "rel", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/DefectMap.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';\r\nimport axios from 'axios';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\r\nimport { Link } from 'react-router-dom';\r\n\r\n// Error Boundary for Map Component\r\nclass MapErrorBoundary extends React.Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = { hasError: false, error: null };\r\n  }\r\n\r\n  static getDerivedStateFromError(error) {\r\n    return { hasError: true, error };\r\n  }\r\n\r\n  componentDidCatch(error, errorInfo) {\r\n    console.error('Map Error Boundary caught an error:', error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      return (\r\n        <div className=\"alert alert-danger\">\r\n          <h6>Map Loading Error</h6>\r\n          <p>There was an issue loading the map. Please refresh the page.</p>\r\n          <button\r\n            className=\"btn btn-primary btn-sm\"\r\n            onClick={() => window.location.reload()}\r\n          >\r\n            Refresh Page\r\n          </button>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\n// Helper component to invalidate map size after mount and on tile load\r\nfunction MapSizeFix({ mapRef }) {\r\n  const map = useMap();\r\n\r\n  useEffect(() => {\r\n    // Store map reference for parent component\r\n    if (mapRef) {\r\n      mapRef.current = map;\r\n    }\r\n\r\n    // Invalidate size shortly after mount with better error handling\r\n    const t1 = setTimeout(() => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map invalidateSize error (t1):', error);\r\n      }\r\n    }, 100);\r\n\r\n    const t2 = setTimeout(() => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map invalidateSize error (t2):', error);\r\n      }\r\n    }, 500);\r\n\r\n    // Also on window resize with error handling\r\n    const onResize = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map resize error:', error);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', onResize);\r\n\r\n    // Invalidate after zoom animations end with error handling\r\n    const onZoomEnd = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map zoom end error:', error);\r\n      }\r\n    };\r\n\r\n    const onMoveEnd = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map move end error:', error);\r\n      }\r\n    };\r\n\r\n    if (map) {\r\n      map.on('zoomend', onZoomEnd);\r\n      map.on('moveend', onMoveEnd);\r\n    }\r\n\r\n    return () => {\r\n      clearTimeout(t1);\r\n      clearTimeout(t2);\r\n      window.removeEventListener('resize', onResize);\r\n      if (map) {\r\n        try {\r\n          map.off('zoomend', onZoomEnd);\r\n          map.off('moveend', onMoveEnd);\r\n        } catch (error) {\r\n          console.warn('Map cleanup error:', error);\r\n        }\r\n      }\r\n    };\r\n  }, [map, mapRef]);\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Image URL Resolution Logic - Same as Dashboard\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n */\r\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\r\n  console.log('🔍 Map getImageUrlForDisplay called:', { imageData, imageType });\r\n\r\n  if (!imageData) {\r\n    console.log('❌ No imageData provided');\r\n    return null;\r\n  }\r\n\r\n  // Check if this is video data with representative frame\r\n  if (imageData.media_type === 'video' && imageData.representative_frame) {\r\n    console.log('📹 Using representative frame for video data');\r\n    return `data:image/jpeg;base64,${imageData.representative_frame}`;\r\n  }\r\n\r\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\r\n  const fullUrlField = `${imageType}_image_full_url`;\r\n  if (imageData[fullUrlField]) {\r\n    console.log('🔗 Using full URL field:', fullUrlField, imageData[fullUrlField]);\r\n    // Extract S3 key from full URL and use proxy endpoint\r\n    const urlParts = imageData[fullUrlField].split('/');\r\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('✅ Generated proxy URL from full URL:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n  }\r\n\r\n  // Try S3 key with proxy endpoint (new images)\r\n  const s3KeyField = `${imageType}_image_s3_url`;\r\n  if (imageData[s3KeyField]) {\r\n    console.log('🔗 Using S3 key field:', s3KeyField, imageData[s3KeyField]);\r\n    const s3Key = imageData[s3KeyField];\r\n    const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n    console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\r\n    return proxyUrl;\r\n  }\r\n\r\n  // Try GridFS endpoint (legacy images)\r\n  const gridfsIdField = `${imageType}_image_id`;\r\n  if (imageData[gridfsIdField]) {\r\n    console.log('🗄️ Using GridFS endpoint:', gridfsIdField, imageData[gridfsIdField]);\r\n    return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n  }\r\n\r\n  console.warn('❌ No valid image URL found for:', imageType, imageData.image_id);\r\n  return null;\r\n};\r\n\r\n/**\r\n * Enhanced Image Display Component - Same as Dashboard\r\n * Supports both original and processed images with toggle\r\n */\r\nconst EnhancedMapImageDisplay = ({ defect }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n  const [isOriginal, setIsOriginal] = useState(false); // Start with processed image\r\n\r\n  useEffect(() => {\r\n    // Reset state when defect or image type changes\r\n    setHasError(false);\r\n    setIsLoading(true);\r\n    setFallbackAttempts(0);\r\n\r\n    // Get image URL using same logic as Dashboard\r\n    const imageUrl = getImageUrlForDisplay(defect, isOriginal ? 'original' : 'processed');\r\n\r\n    console.log('🖼️ Map Enhanced Image Display Debug:', {\r\n      imageType: isOriginal ? 'original' : 'processed',\r\n      imageId: defect?.image_id,\r\n      generatedUrl: imageUrl,\r\n      s3Key: defect?.[`${isOriginal ? 'original' : 'processed'}_image_s3_url`],\r\n      fullUrl: defect?.[`${isOriginal ? 'original' : 'processed'}_image_full_url`],\r\n      gridfsId: defect?.[`${isOriginal ? 'original' : 'processed'}_image_id`]\r\n    });\r\n\r\n    setCurrentImageUrl(imageUrl);\r\n  }, [defect, isOriginal]);\r\n\r\n  const getFallbackImageUrl = (imageData, imageType) => {\r\n    console.log('🔄 Getting fallback URL for:', imageType, imageData);\r\n\r\n    // Try direct S3 URL if we have the full URL field\r\n    const fullUrlField = `${imageType}_image_full_url`;\r\n    if (imageData[fullUrlField]) {\r\n      console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\r\n      return imageData[fullUrlField];\r\n    }\r\n\r\n    // Try GridFS if S3 failed\r\n    const gridfsIdField = `${imageType}_image_id`;\r\n    if (imageData[gridfsIdField]) {\r\n      console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\r\n      return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n    }\r\n\r\n    // Try alternative S3 proxy with different encoding\r\n    const s3KeyField = `${imageType}_image_s3_url`;\r\n    if (imageData[s3KeyField]) {\r\n      console.log('🔄 Trying alternative S3 proxy encoding');\r\n      const s3Key = imageData[s3KeyField];\r\n      const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('🔄 Alternative proxy URL:', alternativeUrl);\r\n      return alternativeUrl;\r\n    }\r\n\r\n    console.log('❌ No fallback URL available');\r\n    return null;\r\n  };\r\n\r\n  const handleImageError = () => {\r\n    console.error('❌ Map image loading failed:', currentImageUrl);\r\n    setIsLoading(false);\r\n\r\n    // Try fallback URLs\r\n    if (fallbackAttempts === 0) {\r\n      const currentImageType = isOriginal ? 'original' : 'processed';\r\n      const fallbackUrl = getFallbackImageUrl(defect, currentImageType);\r\n\r\n      if (fallbackUrl && fallbackUrl !== currentImageUrl) {\r\n        console.log('🔄 Trying fallback URL:', fallbackUrl);\r\n        setCurrentImageUrl(fallbackUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    if (fallbackAttempts === 1) {\r\n      // Second fallback: try alternative image type\r\n      const alternativeType = isOriginal ? 'processed' : 'original';\r\n      const alternativeUrl = getImageUrlForDisplay(defect, alternativeType);\r\n\r\n      if (alternativeUrl && alternativeUrl !== currentImageUrl) {\r\n        console.log('🔄 Trying alternative image type:', alternativeType);\r\n        setCurrentImageUrl(alternativeUrl);\r\n        setFallbackAttempts(2);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks exhausted\r\n    console.error('❌ All fallbacks exhausted for map image');\r\n    setHasError(true);\r\n  };\r\n\r\n  // Don't render anything if no URL available\r\n  if (!currentImageUrl) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small\">\r\n          <i className=\"fas fa-image\"></i> Image not available\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '100px' }}>\r\n          <Spinner animation=\"border\" size=\"sm\" variant=\"primary\" />\r\n          <span className=\"ms-2 small\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (hasError) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small p-2 border rounded bg-light\">\r\n          <i className=\"fas fa-exclamation-triangle\"></i> Image unavailable\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render successful image\r\n  return (\r\n    <div className=\"mb-3 text-center\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt=\"Defect image\"\r\n        className=\"img-fluid border rounded\"\r\n        style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n        onError={handleImageError}\r\n        onLoad={handleImageLoad}\r\n      />\r\n      <div className=\"mt-1\">\r\n        <small className=\"text-primary fw-bold\">📷 Original Image</small>\r\n        {fallbackAttempts > 0 && (\r\n          <div>\r\n            <small className=\"text-warning\">(Fallback source)</small>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Fix for the Leaflet default icon issue\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\r\n});\r\n\r\n// Custom marker icons for different defect types using location icons\r\nconst createCustomIcon = (color, isVideo = false) => {\r\n  const iconSize = isVideo ? [36, 36] : [32, 32];\r\n\r\n  // Create different SVG content for video vs image markers\r\n  const svgContent = isVideo ?\r\n    // Video marker with camera icon made from SVG shapes (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\r\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\r\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\r\n    </svg>` :\r\n    // Image marker with standard location pin (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n    </svg>`;\r\n\r\n  return L.icon({\r\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\r\n    iconSize: iconSize,\r\n    iconAnchor: [iconSize[0]/2, iconSize[1]],\r\n    popupAnchor: [0, -iconSize[1]],\r\n  });\r\n};\r\n\r\nconst icons = {\r\n  pothole: createCustomIcon('#FF0000'), // Red for potholes\r\n  crack: createCustomIcon('#FFCC00'),   // Yellow for cracks (alligator cracks)\r\n  kerb: createCustomIcon('#0066FF'),    // Blue for kerb defects\r\n  // Video variants\r\n  'pothole-video': createCustomIcon('#FF0000', true), // Red for pothole videos\r\n  'crack-video': createCustomIcon('#FFCC00', true),   // Yellow for crack videos\r\n  'kerb-video': createCustomIcon('#0066FF', true),    // Blue for kerb videos\r\n};\r\n\r\nfunction DefectMap({ user }) {\r\n  const [defects, setDefects] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [center] = useState([20.5937, 78.9629]); // India center\r\n  const [zoom] = useState(6); // Country-wide zoom for India\r\n  const mapRef = useRef(null);\r\n  \r\n  // Filters for the map\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\r\n    pothole: true,\r\n    crack: true,\r\n    kerb: true,\r\n  });\r\n\r\n  // Fetch all images with defects and coordinates\r\n  const fetchDefectData = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Prepare query parameters\r\n      let params = {};\r\n      if (startDate) params.start_date = startDate;\r\n      if (endDate) params.end_date = endDate;\r\n      if (selectedUser) params.username = selectedUser;\r\n      if (user?.role) params.user_role = user.role;\r\n\r\n      // Always add cache-busting parameter to ensure latest data\r\n      params._t = Date.now();\r\n\r\n      // Add additional cache-busting for force refresh\r\n      if (forceRefresh) {\r\n        params._force = 'true';\r\n        params._refresh = Math.random().toString(36).substring(7);\r\n      }\r\n\r\n      console.log('🔄 Fetching defect data with params:', params);\r\n      const response = await axios.get('/api/dashboard/image-stats', {\r\n        params,\r\n        // Disable axios caching\r\n        headers: {\r\n          'Cache-Control': 'no-cache',\r\n          'Pragma': 'no-cache'\r\n        }\r\n      });\r\n      console.log('📊 API response received:', {\r\n        success: response.data.success,\r\n        totalImages: response.data.total_images,\r\n        imageCount: response.data.images?.length\r\n      });\r\n\r\n      if (response.data.success) {\r\n        // Process the data to extract defect locations with type information\r\n        const processedDefects = [];\r\n        \r\n        response.data.images.forEach(image => {\r\n          try {\r\n            // Only process images with valid coordinates\r\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\r\n            if (image.coordinates && image.coordinates !== 'Not Available') {\r\n              let lat, lng;\r\n\r\n              // First, try to use EXIF GPS coordinates if available (most accurate)\r\n              if (image.exif_data?.gps_coordinates) {\r\n                lat = image.exif_data.gps_coordinates.latitude;\r\n                lng = image.exif_data.gps_coordinates.longitude;\r\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              } else {\r\n                // Fallback to stored coordinates\r\n                // Handle different coordinate formats\r\n                if (typeof image.coordinates === 'string') {\r\n                  // Parse coordinates (expected format: \"lat,lng\")\r\n                  const coords = image.coordinates.split(',');\r\n                  if (coords.length === 2) {\r\n                    lat = parseFloat(coords[0].trim());\r\n                    lng = parseFloat(coords[1].trim());\r\n                  }\r\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\r\n                  // Handle array format [lat, lng]\r\n                  lat = parseFloat(image.coordinates[0]);\r\n                  lng = parseFloat(image.coordinates[1]);\r\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\r\n                  // Handle object format {lat: x, lng: y}\r\n                  lat = parseFloat(image.coordinates.lat);\r\n                  lng = parseFloat(image.coordinates.lng);\r\n                }\r\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              }\r\n\r\n            // Validate coordinates are within reasonable bounds\r\n            if (!isNaN(lat) && !isNaN(lng) &&\r\n                lat >= -90 && lat <= 90 &&\r\n                lng >= -180 && lng <= 180) {\r\n              console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\r\n              processedDefects.push({\r\n                id: image.id,\r\n                image_id: image.image_id,\r\n                type: image.type,\r\n                position: [lat, lng],\r\n                defect_count: image.defect_count,\r\n                timestamp: new Date(image.timestamp).toLocaleString(),\r\n                username: image.username,\r\n                original_image_id: image.original_image_id,\r\n                // For cracks, include type information if available\r\n                type_counts: image.type_counts,\r\n                // For kerbs, include condition information if available\r\n                condition_counts: image.condition_counts,\r\n                // EXIF and metadata information\r\n                exif_data: image.exif_data || {},\r\n                metadata: image.metadata || {},\r\n                media_type: image.media_type || 'image',\r\n                original_image_full_url: image.original_image_full_url\r\n              });\r\n            } else {\r\n              console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\r\n          }\r\n          } catch (coordError) {\r\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\r\n          }\r\n        });\r\n\r\n        console.log('Processed defects:', processedDefects.length);\r\n        setDefects(processedDefects);\r\n\r\n        // If no defects were processed, show a helpful message\r\n        if (processedDefects.length === 0) {\r\n          setError('No defects found with valid coordinates for the selected date range');\r\n        }\r\n      } else {\r\n        console.error('API returned success: false', response.data);\r\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching defect data:', err);\r\n      setError('Failed to load defect data: ' + (err.response?.data?.message || err.message));\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Fetch the list of users for the filter dropdown\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const params = {};\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      const response = await axios.get('/api/users/all', { params });\r\n      if (response.data.success) {\r\n        setUsersList(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  };\r\n\r\n  // Initialize default dates for the last 30 days\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n\r\n    console.log('DefectMap component initialized');\r\n  }, []);\r\n\r\n  // Fetch data when component mounts and when filters change\r\n  useEffect(() => {\r\n    fetchDefectData();\r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\r\n      fetchDefectData(true); // Force refresh to get latest EXIF data\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => {\r\n      console.log('🛑 Clearing auto-refresh interval');\r\n      clearInterval(interval);\r\n    };\r\n  }, [startDate, endDate, selectedUser, user?.role]);\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    fetchDefectData(true);\r\n  };\r\n\r\n  // Handle filter application\r\n  const handleApplyFilters = () => {\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle resetting filters\r\n  const handleResetFilters = () => {\r\n    // Reset date filters to last 30 days\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n    \r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n    setSelectedUser('');\r\n    setDefectTypeFilters({\r\n      pothole: true,\r\n      crack: true,\r\n      kerb: true,\r\n    });\r\n    \r\n    // Refetch data with reset filters\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle defect type filter changes\r\n  const handleDefectTypeFilterChange = (type) => {\r\n    setDefectTypeFilters(prevFilters => ({\r\n      ...prevFilters,\r\n      [type]: !prevFilters[type]\r\n    }));\r\n  };\r\n\r\n  // Filter defects based on applied filters\r\n  const filteredDefects = defects.filter(defect => \r\n    defectTypeFilters[defect.type]\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-sm dashboard-card mb-4\">\r\n      <Card.Header className=\"bg-primary text-white\">\r\n        <h5 className=\"mb-0\">Defect Map View</h5>\r\n      </Card.Header>\r\n      <Card.Body>\r\n        <Row className=\"mb-3\">\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Date Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Start Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={startDate}\r\n                      onChange={(e) => setStartDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">End Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={endDate}\r\n                      onChange={(e) => setEndDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>User Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Select User</Form.Label>\r\n                    <Form.Select\r\n                      value={selectedUser}\r\n                      onChange={(e) => setSelectedUser(e.target.value)}\r\n                      size=\"sm\"\r\n                    >\r\n                      <option value=\"\">All Users</option>\r\n                      {usersList.map((user, index) => (\r\n                        <option key={index} value={user.username}>\r\n                          {user.username} ({user.role})\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Defect Type Filter</h6>\r\n            <div className=\"filter-controls defect-type-filters\">\r\n              <div className=\"defect-type-filter-options\" style={{ marginTop: \"0\", paddingLeft: \"0\", width: \"100%\", minWidth: \"150px\" }}>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"pothole-filter\"\r\n                  label=\"Potholes\"\r\n                  checked={defectTypeFilters.pothole}\r\n                  onChange={() => handleDefectTypeFilterChange('pothole')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"crack-filter\"\r\n                  label=\"Cracks\"\r\n                  checked={defectTypeFilters.crack}\r\n                  onChange={() => handleDefectTypeFilterChange('crack')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"kerb-filter\"\r\n                  label=\"Kerbs\"\r\n                  checked={defectTypeFilters.kerb}\r\n                  onChange={() => handleDefectTypeFilterChange('kerb')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        <Row className=\"mb-3\">\r\n          <Col className=\"text-center\">\r\n            <div className=\"filter-actions-container\">\r\n              <Button \r\n                variant=\"primary\" \r\n                size=\"sm\" \r\n                onClick={handleApplyFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Apply Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={handleResetFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Reset Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading}\r\n                className=\"filter-btn\"\r\n              >\r\n                {loading ? '🔄 Refreshing...' : '🔄 Refresh Map'}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Row>\r\n          <Col>\r\n            <div className=\"map-legend mb-3\">\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📷 Images</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FF0000' }}></div>\r\n                  <span>Potholes</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FFCC00' }}></div>\r\n                  <span>Cracks</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#0066FF' }}></div>\r\n                  <span>Kerb Defects</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📹 Videos</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FF0000' }}>📹</div>\r\n                  <span>Pothole Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FFCC00' }}>📹</div>\r\n                  <span>Crack Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#0066FF' }}>📹</div>\r\n                  <span>Kerb Videos</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center p-5\">\r\n            <div className=\"spinner-border text-primary\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"alert alert-danger\">{error}</div>\r\n        ) : (\r\n          <div className=\"map-container\" style={{ height: '500px', width: '100%', position: 'relative' }}>\r\n            <MapErrorBoundary>\r\n              <MapContainer\r\n              center={center}\r\n              zoom={zoom}\r\n              style={{ height: '100%', width: '100%' }}\r\n              scrollWheelZoom={true}\r\n              key={`map-${center[0]}-${center[1]}-${zoom}`} // Force remount on center/zoom change\r\n            >\r\n              {/* Ensure Leaflet recalculates size after mount/visibility */}\r\n              <MapSizeFix mapRef={mapRef} />\r\n              <TileLayer\r\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              />\r\n              \r\n              {filteredDefects.map((defect) => {\r\n                // Defensive programming: ensure defect has required properties\r\n                if (!defect || !defect.position || !Array.isArray(defect.position) || defect.position.length !== 2) {\r\n                  console.warn('Invalid defect data:', defect);\r\n                  return null;\r\n                }\r\n\r\n                // Determine icon based on media type and defect type\r\n                const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\r\n                const selectedIcon = icons[iconKey] || icons[defect.type] || icons['pothole']; // fallback icon\r\n\r\n                return (\r\n                <Marker\r\n                  key={defect.id || `defect-${Math.random()}`}\r\n                  position={defect.position}\r\n                  icon={selectedIcon}\r\n                >\r\n                  <Popup maxWidth={400}>\r\n                    <div className=\"defect-popup\">\r\n                      <h6>{defect.type.charAt(0).toUpperCase() + defect.type.slice(1)} Defect</h6>\r\n\r\n                      {/* Video Thumbnail */}\r\n                      {defect.media_type === 'video' && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          {defect.representative_frame ? (\r\n                            <>\r\n                              <img\r\n                                src={`data:image/jpeg;base64,${defect.representative_frame}`}\r\n                                alt=\"Video thumbnail\"\r\n                                className=\"img-fluid border rounded shadow-sm\"\r\n                                style={{ maxHeight: '150px', maxWidth: '100%', objectFit: 'cover' }}\r\n                                onError={(e) => {\r\n                                  console.warn(`Failed to load representative frame for video ${defect.image_id}`);\r\n                                  e.target.style.display = 'none';\r\n                                  // Show fallback message\r\n                                  const fallback = e.target.nextElementSibling;\r\n                                  if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\r\n                                    fallback.style.display = 'block';\r\n                                  }\r\n                                }}\r\n                              />\r\n                              <div className=\"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\" style={{ display: 'none' }}>\r\n                                <i className=\"fas fa-video\"></i> Video thumbnail unavailable\r\n                              </div>\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"text-muted small p-3 border rounded bg-light\">\r\n                              <i className=\"fas fa-video fa-2x mb-2\"></i>\r\n                              <div>Video thumbnail not available</div>\r\n                            </div>\r\n                          )}\r\n                          <div className=\"mt-2\">\r\n                            <small className=\"text-info fw-bold\">📹 Video Thumbnail</small>\r\n                            {defect.video_id && (\r\n                              <div>\r\n                                <small className=\"text-muted d-block\">Video ID: {defect.video_id}</small>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Enhanced Image Display with Fallbacks */}\r\n                      {defect.media_type !== 'video' && (\r\n                        <EnhancedMapImageDisplay defect={defect} />\r\n                      )}\r\n\r\n                      {/* Basic Information */}\r\n                      <div className=\"mb-3\">\r\n                        <h6 className=\"text-primary\">Basic Information</h6>\r\n                        <ul className=\"list-unstyled small\">\r\n                          <li><strong>Count:</strong> {defect.defect_count}</li>\r\n                          <li><strong>Date:</strong> {defect.timestamp}</li>\r\n                          <li><strong>Reported by:</strong> {defect.username}</li>\r\n                          <li><strong>Media Type:</strong> {defect.media_type}</li>\r\n                          <li><strong>GPS:</strong> {defect.position[0].toFixed(6)}, {defect.position[1].toFixed(6)}</li>\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Defect-specific Information */}\r\n                      {defect.type === 'crack' && defect.type_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Crack Types</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.type_counts).map(([type, count]) => (\r\n                              <li key={type}><strong>{type}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {defect.type === 'kerb' && defect.condition_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Kerb Conditions</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.condition_counts).map(([condition, count]) => (\r\n                              <li key={condition}><strong>{condition}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* EXIF/Metadata Information */}\r\n                      {(defect.exif_data || defect.metadata) && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">📊 Media Information</h6>\r\n                          <div className=\"small\">\r\n                            {/* GPS Information - Show first as it's most important for mapping */}\r\n                            {defect.exif_data?.gps_coordinates && (\r\n                              <div className=\"mb-2 p-2 bg-light rounded\">\r\n                                <strong className=\"text-success\">🌍 GPS (EXIF):</strong>\r\n                                <div>Lat: {defect.exif_data.gps_coordinates.latitude?.toFixed(6)}</div>\r\n                                <div>Lng: {defect.exif_data.gps_coordinates.longitude?.toFixed(6)}</div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Camera Information */}\r\n                            {defect.exif_data?.camera_info && Object.keys(defect.exif_data.camera_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📷 Camera:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.camera_info.camera_make && (\r\n                                    <li>Make: {defect.exif_data.camera_info.camera_make}</li>\r\n                                  )}\r\n                                  {defect.exif_data.camera_info.camera_model && (\r\n                                    <li>Model: {defect.exif_data.camera_info.camera_model}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Technical Information */}\r\n                            {defect.exif_data?.technical_info && Object.keys(defect.exif_data.technical_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>⚙️ Technical:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.technical_info.iso && (\r\n                                    <li>ISO: {defect.exif_data.technical_info.iso}</li>\r\n                                  )}\r\n                                  {defect.exif_data.technical_info.exposure_time && (\r\n                                    <li>Exposure: {defect.exif_data.technical_info.exposure_time}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Basic Media Info */}\r\n                            {defect.exif_data?.basic_info && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📐 Dimensions:</strong> {defect.exif_data.basic_info.width} × {defect.exif_data.basic_info.height}\r\n                                {defect.exif_data.basic_info.format && (\r\n                                  <span> ({defect.exif_data.basic_info.format})</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Video-specific metadata */}\r\n                            {defect.media_type === 'video' && (\r\n                              <div className=\"mb-3\">\r\n                                <h6 className=\"text-info\">📹 Video Information</h6>\r\n                                <ul className=\"list-unstyled small\">\r\n                                  {defect.metadata?.format_info?.duration && (\r\n                                    <li><strong>Duration:</strong> {Math.round(defect.metadata.format_info.duration)}s</li>\r\n                                  )}\r\n                                  {defect.metadata?.basic_info?.width && defect.metadata?.basic_info?.height && (\r\n                                    <li><strong>Resolution:</strong> {defect.metadata.basic_info.width}x{defect.metadata.basic_info.height}</li>\r\n                                  )}\r\n                                  {defect.metadata?.format_info?.format_name && (\r\n                                    <li><strong>Format:</strong> {defect.metadata.format_info.format_name.toUpperCase()}</li>\r\n                                  )}\r\n                                  {defect.video_id && (\r\n                                    <li><strong>Video ID:</strong> {defect.video_id}</li>\r\n                                  )}\r\n                                  {/* Show detection counts for videos */}\r\n                                  {defect.model_outputs && (\r\n                                    <>\r\n                                      {defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && (\r\n                                        <li><strong>Potholes Detected:</strong> {defect.model_outputs.potholes.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && (\r\n                                        <li><strong>Cracks Detected:</strong> {defect.model_outputs.cracks.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && (\r\n                                        <li><strong>Kerbs Detected:</strong> {defect.model_outputs.kerbs.length}</li>\r\n                                      )}\r\n                                    </>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Action Buttons */}\r\n                      <div className=\"d-flex gap-2\">\r\n                        <Link\r\n                          to={`/view/${defect.image_id}`}\r\n                          className=\"btn btn-sm btn-primary\"\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                        {/* Only show 'View Original' button for non-video entries */}\r\n                        {defect.media_type !== 'video' && defect.original_image_full_url && (\r\n                          <a\r\n                            href={defect.original_image_full_url}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"btn btn-sm btn-outline-secondary\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            View Original\r\n                          </a>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Popup>\r\n                </Marker>\r\n                );\r\n              })}\r\n            </MapContainer>\r\n            </MapErrorBoundary>\r\n          </div>\r\n        )}\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DefectMap; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACvE,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,SAAStB,KAAK,CAACuB,SAAS,CAAC;EAC7CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC;EAC/C;EAEA,OAAOC,wBAAwBA,CAACD,KAAK,EAAE;IACrC,OAAO;MAAED,QAAQ,EAAE,IAAI;MAAEC;IAAM,CAAC;EAClC;EAEAE,iBAAiBA,CAACF,KAAK,EAAEG,SAAS,EAAE;IAClCC,OAAO,CAACJ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,EAAEG,SAAS,CAAC;EACxE;EAEAE,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACER,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BpB,OAAA;UAAAgB,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnEpB,OAAA;UACEe,SAAS,EAAC,wBAAwB;UAClCM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAR,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEV;IAEA,OAAO,IAAI,CAACd,KAAK,CAACU,QAAQ;EAC5B;AACF;;AAEA;AACA,SAASS,UAAUA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC9B,MAAMC,GAAG,GAAGvC,MAAM,CAAC,CAAC;EAEpBP,SAAS,CAAC,MAAM;IACd;IACA,IAAI4C,MAAM,EAAE;MACVA,MAAM,CAACG,OAAO,GAAGD,GAAG;IACtB;;IAEA;IACA,MAAME,EAAE,GAAGC,UAAU,CAAC,MAAM;MAC1B,IAAI;QACF,IAAIH,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,gCAAgC,EAAExB,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,MAAMyB,EAAE,GAAGH,UAAU,CAAC,MAAM;MAC1B,IAAI;QACF,IAAIH,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,gCAAgC,EAAExB,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA,MAAM0B,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI;QACF,IAAIP,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,mBAAmB,EAAExB,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDa,MAAM,CAACc,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;;IAE3C;IACA,MAAME,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIT,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,qBAAqB,EAAExB,KAAK,CAAC;MAC5C;IACF,CAAC;IAED,MAAM6B,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIV,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,qBAAqB,EAAExB,KAAK,CAAC;MAC5C;IACF,CAAC;IAED,IAAImB,GAAG,EAAE;MACPA,GAAG,CAACW,EAAE,CAAC,SAAS,EAAEF,SAAS,CAAC;MAC5BT,GAAG,CAACW,EAAE,CAAC,SAAS,EAAED,SAAS,CAAC;IAC9B;IAEA,OAAO,MAAM;MACXE,YAAY,CAACV,EAAE,CAAC;MAChBU,YAAY,CAACN,EAAE,CAAC;MAChBZ,MAAM,CAACmB,mBAAmB,CAAC,QAAQ,EAAEN,QAAQ,CAAC;MAC9C,IAAIP,GAAG,EAAE;QACP,IAAI;UACFA,GAAG,CAACc,GAAG,CAAC,SAAS,EAAEL,SAAS,CAAC;UAC7BT,GAAG,CAACc,GAAG,CAAC,SAAS,EAAEJ,SAAS,CAAC;QAC/B,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdI,OAAO,CAACoB,IAAI,CAAC,oBAAoB,EAAExB,KAAK,CAAC;QAC3C;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACmB,GAAG,EAAEF,MAAM,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AAHAC,EAAA,CAvFSF,UAAU;EAAA,QACLpC,MAAM;AAAA;AAAAsD,EAAA,GADXlB,UAAU;AA2FnB,MAAMmB,qBAAqB,GAAGA,CAACC,SAAS,EAAEC,SAAS,GAAG,UAAU,KAAK;EACnEjC,OAAO,CAACkC,GAAG,CAAC,sCAAsC,EAAE;IAAEF,SAAS;IAAEC;EAAU,CAAC,CAAC;EAE7E,IAAI,CAACD,SAAS,EAAE;IACdhC,OAAO,CAACkC,GAAG,CAAC,yBAAyB,CAAC;IACtC,OAAO,IAAI;EACb;;EAEA;EACA,IAAIF,SAAS,CAACG,UAAU,KAAK,OAAO,IAAIH,SAAS,CAACI,oBAAoB,EAAE;IACtEpC,OAAO,CAACkC,GAAG,CAAC,8CAA8C,CAAC;IAC3D,OAAO,0BAA0BF,SAAS,CAACI,oBAAoB,EAAE;EACnE;;EAEA;EACA,MAAMC,YAAY,GAAG,GAAGJ,SAAS,iBAAiB;EAClD,IAAID,SAAS,CAACK,YAAY,CAAC,EAAE;IAC3BrC,OAAO,CAACkC,GAAG,CAAC,0BAA0B,EAAEG,YAAY,EAAEL,SAAS,CAACK,YAAY,CAAC,CAAC;IAC9E;IACA,MAAMC,QAAQ,GAAGN,SAAS,CAACK,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;IACnD,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,IAAIH,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAE;MAC3D,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,KAAK,CAACN,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;MACvD,MAAMC,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;MAC1E7C,OAAO,CAACkC,GAAG,CAAC,sCAAsC,EAAEc,QAAQ,CAAC;MAC7D,OAAOA,QAAQ;IACjB;EACF;;EAEA;EACA,MAAME,UAAU,GAAG,GAAGjB,SAAS,eAAe;EAC9C,IAAID,SAAS,CAACkB,UAAU,CAAC,EAAE;IACzBlD,OAAO,CAACkC,GAAG,CAAC,wBAAwB,EAAEgB,UAAU,EAAElB,SAAS,CAACkB,UAAU,CAAC,CAAC;IACxE,MAAML,KAAK,GAAGb,SAAS,CAACkB,UAAU,CAAC;IACnC,MAAMF,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;IAC1E7C,OAAO,CAACkC,GAAG,CAAC,oCAAoC,EAAEc,QAAQ,CAAC;IAC3D,OAAOA,QAAQ;EACjB;;EAEA;EACA,MAAMG,aAAa,GAAG,GAAGlB,SAAS,WAAW;EAC7C,IAAID,SAAS,CAACmB,aAAa,CAAC,EAAE;IAC5BnD,OAAO,CAACkC,GAAG,CAAC,4BAA4B,EAAEiB,aAAa,EAAEnB,SAAS,CAACmB,aAAa,CAAC,CAAC;IAClF,OAAO,2BAA2BnB,SAAS,CAACmB,aAAa,CAAC,EAAE;EAC9D;EAEAnD,OAAO,CAACoB,IAAI,CAAC,iCAAiC,EAAEa,SAAS,EAAED,SAAS,CAACoB,QAAQ,CAAC;EAC9E,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyB,QAAQ,EAAE+D,WAAW,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyF,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErDD,SAAS,CAAC,MAAM;IACd;IACAyF,WAAW,CAAC,KAAK,CAAC;IAClBE,YAAY,CAAC,IAAI,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;;IAEtB;IACA,MAAMG,QAAQ,GAAGlC,qBAAqB,CAACuB,MAAM,EAAES,UAAU,GAAG,UAAU,GAAG,WAAW,CAAC;IAErF/D,OAAO,CAACkC,GAAG,CAAC,uCAAuC,EAAE;MACnDD,SAAS,EAAE8B,UAAU,GAAG,UAAU,GAAG,WAAW;MAChDG,OAAO,EAAEZ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEF,QAAQ;MACzBe,YAAY,EAAEF,QAAQ;MACtBpB,KAAK,EAAES,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGS,UAAU,GAAG,UAAU,GAAG,WAAW,eAAe,CAAC;MACxEK,OAAO,EAAEd,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGS,UAAU,GAAG,UAAU,GAAG,WAAW,iBAAiB,CAAC;MAC5EM,QAAQ,EAAEf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGS,UAAU,GAAG,UAAU,GAAG,WAAW,WAAW;IACxE,CAAC,CAAC;IAEFN,kBAAkB,CAACQ,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAACX,MAAM,EAAES,UAAU,CAAC,CAAC;EAExB,MAAMO,mBAAmB,GAAGA,CAACtC,SAAS,EAAEC,SAAS,KAAK;IACpDjC,OAAO,CAACkC,GAAG,CAAC,8BAA8B,EAAED,SAAS,EAAED,SAAS,CAAC;;IAEjE;IACA,MAAMK,YAAY,GAAG,GAAGJ,SAAS,iBAAiB;IAClD,IAAID,SAAS,CAACK,YAAY,CAAC,EAAE;MAC3BrC,OAAO,CAACkC,GAAG,CAAC,0BAA0B,EAAEF,SAAS,CAACK,YAAY,CAAC,CAAC;MAChE,OAAOL,SAAS,CAACK,YAAY,CAAC;IAChC;;IAEA;IACA,MAAMc,aAAa,GAAG,GAAGlB,SAAS,WAAW;IAC7C,IAAID,SAAS,CAACmB,aAAa,CAAC,EAAE;MAC5BnD,OAAO,CAACkC,GAAG,CAAC,uBAAuB,EAAEF,SAAS,CAACmB,aAAa,CAAC,CAAC;MAC9D,OAAO,2BAA2BnB,SAAS,CAACmB,aAAa,CAAC,EAAE;IAC9D;;IAEA;IACA,MAAMD,UAAU,GAAG,GAAGjB,SAAS,eAAe;IAC9C,IAAID,SAAS,CAACkB,UAAU,CAAC,EAAE;MACzBlD,OAAO,CAACkC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMW,KAAK,GAAGb,SAAS,CAACkB,UAAU,CAAC;MACnC,MAAMqB,cAAc,GAAG,8BAA8BtB,kBAAkB,CAACJ,KAAK,CAAC,EAAE;MAChF7C,OAAO,CAACkC,GAAG,CAAC,2BAA2B,EAAEqC,cAAc,CAAC;MACxD,OAAOA,cAAc;IACvB;IAEAvE,OAAO,CAACkC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,OAAO,IAAI;EACb,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxE,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAE4D,eAAe,CAAC;IAC7DI,YAAY,CAAC,KAAK,CAAC;;IAEnB;IACA,IAAIC,gBAAgB,KAAK,CAAC,EAAE;MAC1B,MAAMY,gBAAgB,GAAGV,UAAU,GAAG,UAAU,GAAG,WAAW;MAC9D,MAAMW,WAAW,GAAGJ,mBAAmB,CAAChB,MAAM,EAAEmB,gBAAgB,CAAC;MAEjE,IAAIC,WAAW,IAAIA,WAAW,KAAKlB,eAAe,EAAE;QAClDxD,OAAO,CAACkC,GAAG,CAAC,yBAAyB,EAAEwC,WAAW,CAAC;QACnDjB,kBAAkB,CAACiB,WAAW,CAAC;QAC/BZ,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;IAEA,IAAID,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAMc,eAAe,GAAGZ,UAAU,GAAG,WAAW,GAAG,UAAU;MAC7D,MAAMQ,cAAc,GAAGxC,qBAAqB,CAACuB,MAAM,EAAEqB,eAAe,CAAC;MAErE,IAAIJ,cAAc,IAAIA,cAAc,KAAKf,eAAe,EAAE;QACxDxD,OAAO,CAACkC,GAAG,CAAC,mCAAmC,EAAEyC,eAAe,CAAC;QACjElB,kBAAkB,CAACc,cAAc,CAAC;QAClCT,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA;IACA9D,OAAO,CAACJ,KAAK,CAAC,yCAAyC,CAAC;IACxD8D,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,IAAI,CAACF,eAAe,EAAE;IACpB,oBACErE,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhB,OAAA;UAAGe,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,wBAClC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIoD,SAAS,EAAE;IACb,oBACExE,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,kDAAkD;QAAC0E,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAA1E,QAAA,gBAC9FhB,OAAA,CAACH,OAAO;UAAC8F,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,IAAI;UAACC,OAAO,EAAC;QAAS;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DpB,OAAA;UAAMe,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIZ,QAAQ,EAAE;IACZ,oBACER,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DhB,OAAA;UAAGe,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sBACjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BhB,OAAA;MACE8F,GAAG,EAAEzB,eAAgB;MACrB0B,GAAG,EAAC,cAAc;MAClBhF,SAAS,EAAC,0BAA0B;MACpC0E,KAAK,EAAE;QAAEO,SAAS,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAChDC,OAAO,EAAEb,gBAAiB;MAC1Bc,MAAM,EAAEC;IAAgB;MAAAnF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFpB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhB,OAAA;QAAOe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEsD,gBAAgB,GAAG,CAAC,iBACnB1E,OAAA;QAAAgB,QAAA,eACEhB,OAAA;UAAOe,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAgD,GAAA,CAvJMF,uBAAuB;AAAAmC,GAAA,GAAvBnC,uBAAuB;AAwJ7B,OAAO3E,CAAC,CAAC+G,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3ClH,CAAC,CAAC+G,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,KAAK;EACnD,MAAMC,QAAQ,GAAGD,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAME,UAAU,GAAGF,OAAO;EACxB;EACA,qEAAqED,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA,8CAA8CF,KAAK;AACnD,sDAAsDA,KAAK;AAC3D,WAAW;EACP;EACA,qEAAqEA,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA,WAAW;EAET,OAAO1H,CAAC,CAAC4H,IAAI,CAAC;IACZP,OAAO,EAAE,oCAAoC9C,kBAAkB,CAACoD,UAAU,CAAC,EAAE;IAC7ED,QAAQ,EAAEA,QAAQ;IAClBG,UAAU,EAAE,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxCI,WAAW,EAAE,CAAC,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,KAAK,GAAG;EACZC,OAAO,EAAET,gBAAgB,CAAC,SAAS,CAAC;EAAE;EACtCU,KAAK,EAAEV,gBAAgB,CAAC,SAAS,CAAC;EAAI;EACtCW,IAAI,EAAEX,gBAAgB,CAAC,SAAS,CAAC;EAAK;EACtC;EACA,eAAe,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAE;EACpD,aAAa,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAI;EACpD,YAAY,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAK;AACtD,CAAC;AAED,SAASY,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,GAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgJ,OAAO,EAAEC,UAAU,CAAC,GAAGjJ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEwH,QAAQ,CAAC,GAAGlJ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmJ,MAAM,CAAC,GAAGnJ,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoJ,IAAI,CAAC,GAAGpJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM2C,MAAM,GAAG1C,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAACoJ,SAAS,EAAEC,YAAY,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuJ,OAAO,EAAEC,UAAU,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyJ,YAAY,EAAEC,eAAe,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2J,SAAS,EAAEC,YAAY,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9J,QAAQ,CAAC;IACzDwI,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMqB,eAAe,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACtD,IAAI;MAAA,IAAAC,qBAAA;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIgB,MAAM,GAAG,CAAC,CAAC;MACf,IAAIb,SAAS,EAAEa,MAAM,CAACC,UAAU,GAAGd,SAAS;MAC5C,IAAIE,OAAO,EAAEW,MAAM,CAACE,QAAQ,GAAGb,OAAO;MACtC,IAAIE,YAAY,EAAES,MAAM,CAACG,QAAQ,GAAGZ,YAAY;MAChD,IAAIb,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;;MAE5C;MACAJ,MAAM,CAACM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIV,YAAY,EAAE;QAChBE,MAAM,CAACS,MAAM,GAAG,MAAM;QACtBT,MAAM,CAACU,QAAQ,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3D;MAEAlJ,OAAO,CAACkC,GAAG,CAAC,sCAAsC,EAAEkG,MAAM,CAAC;MAC3D,MAAMe,QAAQ,GAAG,MAAM1K,KAAK,CAAC2K,GAAG,CAAC,4BAA4B,EAAE;QAC7DhB,MAAM;QACN;QACAiB,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACFrJ,OAAO,CAACkC,GAAG,CAAC,2BAA2B,EAAE;QACvCoH,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;QAC9BE,WAAW,EAAEL,QAAQ,CAACI,IAAI,CAACE,YAAY;QACvCC,UAAU,GAAAvB,qBAAA,GAAEgB,QAAQ,CAACI,IAAI,CAACI,MAAM,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsBvF;MACpC,CAAC,CAAC;MAEF,IAAIuG,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzB;QACA,MAAMM,gBAAgB,GAAG,EAAE;QAE3BT,QAAQ,CAACI,IAAI,CAACI,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;UACpC,IAAI;YACF;YACA9J,OAAO,CAACkC,GAAG,CAAC,iBAAiB4H,KAAK,CAAC3H,UAAU,IAAI,OAAO,IAAI2H,KAAK,CAAC1G,QAAQ,iBAAiB0G,KAAK,CAACC,WAAW,UAAUD,KAAK,CAACE,IAAI,EAAE,CAAC;YACnI,IAAIF,KAAK,CAACC,WAAW,IAAID,KAAK,CAACC,WAAW,KAAK,eAAe,EAAE;cAAA,IAAAE,gBAAA;cAC9D,IAAIC,GAAG,EAAEC,GAAG;;cAEZ;cACA,KAAAF,gBAAA,GAAIH,KAAK,CAACM,SAAS,cAAAH,gBAAA,eAAfA,gBAAA,CAAiBI,eAAe,EAAE;gBACpCH,GAAG,GAAGJ,KAAK,CAACM,SAAS,CAACC,eAAe,CAACC,QAAQ;gBAC9CH,GAAG,GAAGL,KAAK,CAACM,SAAS,CAACC,eAAe,CAACE,SAAS;gBAC/CvK,OAAO,CAACkC,GAAG,CAAC,qCAAqC4H,KAAK,CAAC3H,UAAU,IAAI,OAAO,IAAI2H,KAAK,CAAC1G,QAAQ,MAAM8G,GAAG,KAAKC,GAAG,GAAG,CAAC;cACrH,CAAC,MAAM;gBACL;gBACA;gBACA,IAAI,OAAOL,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;kBACzC;kBACA,MAAMS,MAAM,GAAGV,KAAK,CAACC,WAAW,CAACxH,KAAK,CAAC,GAAG,CAAC;kBAC3C,IAAIiI,MAAM,CAAC5H,MAAM,KAAK,CAAC,EAAE;oBACvBsH,GAAG,GAAGO,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;oBAClCP,GAAG,GAAGM,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;kBACpC;gBACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACd,KAAK,CAACC,WAAW,CAAC,IAAID,KAAK,CAACC,WAAW,CAACnH,MAAM,KAAK,CAAC,EAAE;kBAC7E;kBACAsH,GAAG,GAAGO,UAAU,CAACX,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;kBACtCI,GAAG,GAAGM,UAAU,CAACX,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,MAAM,IAAI,OAAOD,KAAK,CAACC,WAAW,KAAK,QAAQ,IAAID,KAAK,CAACC,WAAW,CAACG,GAAG,IAAIJ,KAAK,CAACC,WAAW,CAACI,GAAG,EAAE;kBAClG;kBACAD,GAAG,GAAGO,UAAU,CAACX,KAAK,CAACC,WAAW,CAACG,GAAG,CAAC;kBACvCC,GAAG,GAAGM,UAAU,CAACX,KAAK,CAACC,WAAW,CAACI,GAAG,CAAC;gBACzC;gBACAnK,OAAO,CAACkC,GAAG,CAAC,mCAAmC4H,KAAK,CAAC3H,UAAU,IAAI,OAAO,IAAI2H,KAAK,CAAC1G,QAAQ,MAAM8G,GAAG,KAAKC,GAAG,GAAG,CAAC;cACnH;;cAEF;cACA,IAAI,CAACU,KAAK,CAACX,GAAG,CAAC,IAAI,CAACW,KAAK,CAACV,GAAG,CAAC,IAC1BD,GAAG,IAAI,CAAC,EAAE,IAAIA,GAAG,IAAI,EAAE,IACvBC,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;gBAC7BnK,OAAO,CAACkC,GAAG,CAAC,2BAA2B4H,KAAK,CAAC3H,UAAU,IAAI,OAAO,IAAI2H,KAAK,CAACgB,EAAE,MAAMZ,GAAG,KAAKC,GAAG,mBAAmB,CAAC;gBACnHP,gBAAgB,CAACmB,IAAI,CAAC;kBACpBD,EAAE,EAAEhB,KAAK,CAACgB,EAAE;kBACZ1H,QAAQ,EAAE0G,KAAK,CAAC1G,QAAQ;kBACxB4G,IAAI,EAAEF,KAAK,CAACE,IAAI;kBAChBgB,QAAQ,EAAE,CAACd,GAAG,EAAEC,GAAG,CAAC;kBACpBc,YAAY,EAAEnB,KAAK,CAACmB,YAAY;kBAChCC,SAAS,EAAE,IAAIvC,IAAI,CAACmB,KAAK,CAACoB,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;kBACrD5C,QAAQ,EAAEuB,KAAK,CAACvB,QAAQ;kBACxB6C,iBAAiB,EAAEtB,KAAK,CAACsB,iBAAiB;kBAC1C;kBACAC,WAAW,EAAEvB,KAAK,CAACuB,WAAW;kBAC9B;kBACAC,gBAAgB,EAAExB,KAAK,CAACwB,gBAAgB;kBACxC;kBACAlB,SAAS,EAAEN,KAAK,CAACM,SAAS,IAAI,CAAC,CAAC;kBAChCmB,QAAQ,EAAEzB,KAAK,CAACyB,QAAQ,IAAI,CAAC,CAAC;kBAC9BpJ,UAAU,EAAE2H,KAAK,CAAC3H,UAAU,IAAI,OAAO;kBACvCqJ,uBAAuB,EAAE1B,KAAK,CAAC0B;gBACjC,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLxL,OAAO,CAACoB,IAAI,CAAC,6BAA6B0I,KAAK,CAAC3H,UAAU,IAAI,OAAO,IAAI2H,KAAK,CAACgB,EAAE,GAAG,EAAEhB,KAAK,CAACC,WAAW,EAAE,eAAeG,GAAG,SAASC,GAAG,EAAE,CAAC;cAC5I;YACF,CAAC,MAAM;cACLnK,OAAO,CAACkC,GAAG,CAAC,eAAe4H,KAAK,CAAC3H,UAAU,IAAI,OAAO,IAAI2H,KAAK,CAACgB,EAAE,iBAAiBhB,KAAK,CAACC,WAAW,EAAE,CAAC;YACzG;UACA,CAAC,CAAC,OAAO0B,UAAU,EAAE;YACnBzL,OAAO,CAACJ,KAAK,CAAC,4CAA4CkK,KAAK,CAACgB,EAAE,GAAG,EAAEW,UAAU,EAAE3B,KAAK,CAACC,WAAW,CAAC;UACvG;QACF,CAAC,CAAC;QAEF/J,OAAO,CAACkC,GAAG,CAAC,oBAAoB,EAAE0H,gBAAgB,CAAChH,MAAM,CAAC;QAC1DqE,UAAU,CAAC2C,gBAAgB,CAAC;;QAE5B;QACA,IAAIA,gBAAgB,CAAChH,MAAM,KAAK,CAAC,EAAE;UACjCwE,QAAQ,CAAC,qEAAqE,CAAC;QACjF;MACF,CAAC,MAAM;QACLpH,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAEuJ,QAAQ,CAACI,IAAI,CAAC;QAC3DnC,QAAQ,CAAC,8BAA8B,IAAI+B,QAAQ,CAACI,IAAI,CAACmC,OAAO,IAAI,eAAe,CAAC,CAAC;MACvF;MAEAvE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOwE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZ7L,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAE+L,GAAG,CAAC;MACjDvE,QAAQ,CAAC,8BAA8B,IAAI,EAAAwE,aAAA,GAAAD,GAAG,CAACxC,QAAQ,cAAAyC,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcrC,IAAI,cAAAsC,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;MACvFvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2E,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAM1D,MAAM,GAAG,CAAC,CAAC;MACjB,IAAItB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;MAE5C,MAAMW,QAAQ,GAAG,MAAM1K,KAAK,CAAC2K,GAAG,CAAC,gBAAgB,EAAE;QAAEhB;MAAO,CAAC,CAAC;MAC9D,IAAIe,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzBxB,YAAY,CAACqB,QAAQ,CAACI,IAAI,CAACwC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC,OAAOnM,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM+N,WAAW,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAC9B,MAAMsD,aAAa,GAAG,IAAItD,IAAI,CAAC,CAAC;IAChCsD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDzE,UAAU,CAACsE,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC7J,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDiF,YAAY,CAACyE,aAAa,CAACG,WAAW,CAAC,CAAC,CAAC7J,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvDvC,OAAO,CAACkC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACdgK,eAAe,CAAC,CAAC;IACjB6D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAChF,IAAI,CAAC,CAAC;;EAEV;EACA7I,SAAS,CAAC,MAAM;IACd,MAAMoO,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCtM,OAAO,CAACkC,GAAG,CAAC,+DAA+D,CAAC;MAC5E+F,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM;MACXjI,OAAO,CAACkC,GAAG,CAAC,mCAAmC,CAAC;MAChDqK,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAAC9E,SAAS,EAAEE,OAAO,EAAEE,YAAY,EAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC,CAAC;;EAElD;EACA,MAAMgE,aAAa,GAAGA,CAAA,KAAM;IAC1BvE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxE,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMV,WAAW,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAC9B,MAAMsD,aAAa,GAAG,IAAItD,IAAI,CAAC,CAAC;IAChCsD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDzE,UAAU,CAACsE,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC7J,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDiF,YAAY,CAACyE,aAAa,CAACG,WAAW,CAAC,CAAC,CAAC7J,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvDqF,eAAe,CAAC,EAAE,CAAC;IACnBI,oBAAoB,CAAC;MACnBtB,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACAqB,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAM0E,4BAA4B,GAAI3C,IAAI,IAAK;IAC7ChC,oBAAoB,CAAC4E,WAAW,KAAK;MACnC,GAAGA,WAAW;MACd,CAAC5C,IAAI,GAAG,CAAC4C,WAAW,CAAC5C,IAAI;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM6C,eAAe,GAAG7F,OAAO,CAAC8F,MAAM,CAACxJ,MAAM,IAC3CyE,iBAAiB,CAACzE,MAAM,CAAC0G,IAAI,CAC/B,CAAC;EAED,oBACE7K,OAAA,CAACR,IAAI;IAACuB,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC7ChB,OAAA,CAACR,IAAI,CAACoO,MAAM;MAAC7M,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5ChB,OAAA;QAAIe,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdpB,OAAA,CAACR,IAAI,CAACqO,IAAI;MAAA7M,QAAA,gBACRhB,OAAA,CAACP,GAAG;QAACsB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA,CAACN,GAAG;UAACoO,EAAE,EAAE,CAAE;UAAC/M,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAKe,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrChB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAACoO,KAAK;kBAAA/M,QAAA,gBACThB,OAAA,CAACL,IAAI,CAACqO,KAAK;oBAACjN,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrDpB,OAAA,CAACL,IAAI,CAACsO,OAAO;oBACXpD,IAAI,EAAC,MAAM;oBACXqD,KAAK,EAAE9F,SAAU;oBACjB+F,QAAQ,EAAGC,CAAC,IAAK/F,YAAY,CAAC+F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9CtI,IAAI,EAAC;kBAAI;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAACoO,KAAK;kBAAA/M,QAAA,gBACThB,OAAA,CAACL,IAAI,CAACqO,KAAK;oBAACjN,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnDpB,OAAA,CAACL,IAAI,CAACsO,OAAO;oBACXpD,IAAI,EAAC,MAAM;oBACXqD,KAAK,EAAE5F,OAAQ;oBACf6F,QAAQ,EAAGC,CAAC,IAAK7F,UAAU,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC5CtI,IAAI,EAAC;kBAAI;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA,CAACN,GAAG;UAACoO,EAAE,EAAE,CAAE;UAAC/M,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAKe,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrChB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAACoO,KAAK;kBAAA/M,QAAA,gBACThB,OAAA,CAACL,IAAI,CAACqO,KAAK;oBAACjN,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDpB,OAAA,CAACL,IAAI,CAAC2O,MAAM;oBACVJ,KAAK,EAAE1F,YAAa;oBACpB2F,QAAQ,EAAGC,CAAC,IAAK3F,eAAe,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDtI,IAAI,EAAC,IAAI;oBAAA5E,QAAA,gBAEThB,OAAA;sBAAQkO,KAAK,EAAC,EAAE;sBAAAlN,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAClCsH,SAAS,CAAC9G,GAAG,CAAC,CAAC+F,IAAI,EAAE4G,KAAK,kBACzBvO,OAAA;sBAAoBkO,KAAK,EAAEvG,IAAI,CAACyB,QAAS;sBAAApI,QAAA,GACtC2G,IAAI,CAACyB,QAAQ,EAAC,IAAE,EAACzB,IAAI,CAAC0B,IAAI,EAAC,GAC9B;oBAAA,GAFakF,KAAK;sBAAAtN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA,CAACN,GAAG;UAACoO,EAAE,EAAE,CAAE;UAAC/M,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BpB,OAAA;YAAKe,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDhB,OAAA;cAAKe,SAAS,EAAC,4BAA4B;cAAC0E,KAAK,EAAE;gBAAE+I,SAAS,EAAE,GAAG;gBAAEC,WAAW,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAA3N,QAAA,gBACxHhB,OAAA,CAACL,IAAI,CAACiP,KAAK;gBACT/D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,gBAAgB;gBACnBkD,KAAK,EAAC,UAAU;gBAChBC,OAAO,EAAElG,iBAAiB,CAACrB,OAAQ;gBACnC4G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,SAAS,CAAE;gBACxDzM,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFpB,OAAA,CAACL,IAAI,CAACiP,KAAK;gBACT/D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,cAAc;gBACjBkD,KAAK,EAAC,QAAQ;gBACdC,OAAO,EAAElG,iBAAiB,CAACpB,KAAM;gBACjC2G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,OAAO,CAAE;gBACtDzM,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFpB,OAAA,CAACL,IAAI,CAACiP,KAAK;gBACT/D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,aAAa;gBAChBkD,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAElG,iBAAiB,CAACnB,IAAK;gBAChC0G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,MAAM,CAAE;gBACrDzM,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACP,GAAG;QAACsB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhB,OAAA,CAACN,GAAG;UAACqB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BhB,OAAA;YAAKe,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvChB,OAAA,CAACJ,MAAM;cACLiG,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTvE,OAAO,EAAEiM,kBAAmB;cAC5BvM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACJ,MAAM;cACLiG,OAAO,EAAC,mBAAmB;cAC3BD,IAAI,EAAC,IAAI;cACTvE,OAAO,EAAEkM,kBAAmB;cAC5BxM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACJ,MAAM;cACLiG,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACTvE,OAAO,EAAEgM,aAAc;cACvB0B,QAAQ,EAAEhH,OAAQ;cAClBhH,SAAS,EAAC,YAAY;cAAAC,QAAA,EAErB+G,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACP,GAAG;QAAAuB,QAAA,eACFhB,OAAA,CAACN,GAAG;UAAAsB,QAAA,eACFhB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BhB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhB,OAAA;gBAAIe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAAC0E,KAAK,EAAE;oBAAEuJ,eAAe,EAAE;kBAAU;gBAAE;kBAAA/N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAAC0E,KAAK,EAAE;oBAAEuJ,eAAe,EAAE;kBAAU;gBAAE;kBAAA/N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAAC0E,KAAK,EAAE;oBAAEuJ,eAAe,EAAE;kBAAU;gBAAE;kBAAA/N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhB,OAAA;gBAAIe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAAC0E,KAAK,EAAE;oBAAEuJ,eAAe,EAAE;kBAAU,CAAE;kBAAAhO,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAAC0E,KAAK,EAAE;oBAAEuJ,eAAe,EAAE;kBAAU,CAAE;kBAAAhO,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAAC0E,KAAK,EAAE;oBAAEuJ,eAAe,EAAE;kBAAU,CAAE;kBAAAhO,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL2G,OAAO,gBACN/H,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAACsI,IAAI,EAAC,QAAQ;UAAArI,QAAA,eACxDhB,OAAA;YAAMe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJX,KAAK,gBACPT,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEjDpB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAC0E,KAAK,EAAE;UAAEwJ,MAAM,EAAE,OAAO;UAAEP,KAAK,EAAE,MAAM;UAAE7C,QAAQ,EAAE;QAAW,CAAE;QAAA7K,QAAA,eAC7FhB,OAAA,CAACG,gBAAgB;UAAAa,QAAA,eACfhB,OAAA,CAACf,YAAY;YACbiJ,MAAM,EAAEA,MAAO;YACfC,IAAI,EAAEA,IAAK;YACX1C,KAAK,EAAE;cAAEwJ,MAAM,EAAE,MAAM;cAAEP,KAAK,EAAE;YAAO,CAAE;YACzCQ,eAAe,EAAE,IAAK;YAAAlO,QAAA,gBAItBhB,OAAA,CAACyB,UAAU;cAACC,MAAM,EAAEA;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BpB,OAAA,CAACd,SAAS;cACRiQ,WAAW,EAAC,yFAAyF;cACrGC,GAAG,EAAC;YAAoD;cAAAnO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EAEDsM,eAAe,CAAC9L,GAAG,CAAEuC,MAAM,IAAK;cAAA,IAAAkL,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;cAC/B;cACA,IAAI,CAAC/L,MAAM,IAAI,CAACA,MAAM,CAAC0H,QAAQ,IAAI,CAACL,KAAK,CAACC,OAAO,CAACtH,MAAM,CAAC0H,QAAQ,CAAC,IAAI1H,MAAM,CAAC0H,QAAQ,CAACpI,MAAM,KAAK,CAAC,EAAE;gBAClG5C,OAAO,CAACoB,IAAI,CAAC,sBAAsB,EAAEkC,MAAM,CAAC;gBAC5C,OAAO,IAAI;cACb;;cAEA;cACA,MAAMgM,OAAO,GAAGhM,MAAM,CAACnB,UAAU,KAAK,OAAO,GAAG,GAAGmB,MAAM,CAAC0G,IAAI,QAAQ,GAAG1G,MAAM,CAAC0G,IAAI;cACpF,MAAMuF,YAAY,GAAG9I,KAAK,CAAC6I,OAAO,CAAC,IAAI7I,KAAK,CAACnD,MAAM,CAAC0G,IAAI,CAAC,IAAIvD,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;;cAE/E,oBACAtH,OAAA,CAACb,MAAM;gBAEL0M,QAAQ,EAAE1H,MAAM,CAAC0H,QAAS;gBAC1B1E,IAAI,EAAEiJ,YAAa;gBAAApP,QAAA,eAEnBhB,OAAA,CAACZ,KAAK;kBAAC6G,QAAQ,EAAE,GAAI;kBAAAjF,QAAA,eACnBhB,OAAA;oBAAKe,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BhB,OAAA;sBAAAgB,QAAA,GAAKmD,MAAM,CAAC0G,IAAI,CAACwF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnM,MAAM,CAAC0G,IAAI,CAAClH,KAAK,CAAC,CAAC,CAAC,EAAC,SAAO;oBAAA;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAG3E+C,MAAM,CAACnB,UAAU,KAAK,OAAO,iBAC5BhD,OAAA;sBAAKe,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,GAC9BmD,MAAM,CAAClB,oBAAoB,gBAC1BjD,OAAA,CAAAE,SAAA;wBAAAc,QAAA,gBACEhB,OAAA;0BACE8F,GAAG,EAAE,0BAA0B3B,MAAM,CAAClB,oBAAoB,EAAG;0BAC7D8C,GAAG,EAAC,iBAAiB;0BACrBhF,SAAS,EAAC,oCAAoC;0BAC9C0E,KAAK,EAAE;4BAAEO,SAAS,EAAE,OAAO;4BAAEC,QAAQ,EAAE,MAAM;4BAAEsK,SAAS,EAAE;0BAAQ,CAAE;0BACpErK,OAAO,EAAGkI,CAAC,IAAK;4BACdvN,OAAO,CAACoB,IAAI,CAAC,iDAAiDkC,MAAM,CAACF,QAAQ,EAAE,CAAC;4BAChFmK,CAAC,CAACC,MAAM,CAAC5I,KAAK,CAAC+K,OAAO,GAAG,MAAM;4BAC/B;4BACA,MAAMC,QAAQ,GAAGrC,CAAC,CAACC,MAAM,CAACqC,kBAAkB;4BAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,CAACC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;8BACvEH,QAAQ,CAAChL,KAAK,CAAC+K,OAAO,GAAG,OAAO;4BAClC;0BACF;wBAAE;0BAAAvP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFpB,OAAA;0BAAKe,SAAS,EAAC,uEAAuE;0BAAC0E,KAAK,EAAE;4BAAE+K,OAAO,EAAE;0BAAO,CAAE;0BAAAxP,QAAA,gBAChHhB,OAAA;4BAAGe,SAAS,EAAC;0BAAc;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,gCAClC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACN,CAAC,gBAEHpB,OAAA;wBAAKe,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,gBAC3DhB,OAAA;0BAAGe,SAAS,EAAC;wBAAyB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3CpB,OAAA;0BAAAgB,QAAA,EAAK;wBAA6B;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACN,eACDpB,OAAA;wBAAKe,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBhB,OAAA;0BAAOe,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAAC;wBAAkB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EAC9D+C,MAAM,CAAC0M,QAAQ,iBACd7Q,OAAA;0BAAAgB,QAAA,eACEhB,OAAA;4BAAOe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAAC,YAAU,EAACmD,MAAM,CAAC0M,QAAQ;0BAAA;4BAAA5P,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtE,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,EAGA+C,MAAM,CAACnB,UAAU,KAAK,OAAO,iBAC5BhD,OAAA,CAACkE,uBAAuB;sBAACC,MAAM,EAAEA;oBAAO;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC3C,eAGDpB,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnDpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,gBACjChB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC2H,YAAY;wBAAA;0BAAA7K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC4H,SAAS;wBAAA;0BAAA9K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAY;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACiF,QAAQ;wBAAA;0BAAAnI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAW;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACnB,UAAU;wBAAA;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC0H,QAAQ,CAAC,CAAC,CAAC,CAACiF,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAC3M,MAAM,CAAC0H,QAAQ,CAAC,CAAC,CAAC,CAACiF,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAA7P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,EAGL+C,MAAM,CAAC0G,IAAI,KAAK,OAAO,IAAI1G,MAAM,CAAC+H,WAAW,iBAC5ClM,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7CpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAChC+P,MAAM,CAACC,OAAO,CAAC7M,MAAM,CAAC+H,WAAW,CAAC,CAACtK,GAAG,CAAC,CAAC,CAACiJ,IAAI,EAAEoG,KAAK,CAAC,kBACpDjR,OAAA;0BAAAgB,QAAA,gBAAehB,OAAA;4BAAAgB,QAAA,GAAS6J,IAAI,EAAC,GAAC;0BAAA;4BAAA5J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC6P,KAAK;wBAAA,GAArCpG,IAAI;0BAAA5J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAsC,CACpD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN,EAEA+C,MAAM,CAAC0G,IAAI,KAAK,MAAM,IAAI1G,MAAM,CAACgI,gBAAgB,iBAChDnM,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjDpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAChC+P,MAAM,CAACC,OAAO,CAAC7M,MAAM,CAACgI,gBAAgB,CAAC,CAACvK,GAAG,CAAC,CAAC,CAACsP,SAAS,EAAED,KAAK,CAAC,kBAC9DjR,OAAA;0BAAAgB,QAAA,gBAAoBhB,OAAA;4BAAAgB,QAAA,GAASkQ,SAAS,EAAC,GAAC;0BAAA;4BAAAjQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC6P,KAAK;wBAAA,GAA/CC,SAAS;0BAAAjQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAA2C,CAC9D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN,EAGA,CAAC+C,MAAM,CAAC8G,SAAS,IAAI9G,MAAM,CAACiI,QAAQ,kBACnCpM,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtDpB,OAAA;wBAAKe,SAAS,EAAC,OAAO;wBAAAC,QAAA,GAEnB,EAAAqO,iBAAA,GAAAlL,MAAM,CAAC8G,SAAS,cAAAoE,iBAAA,uBAAhBA,iBAAA,CAAkBnE,eAAe,kBAChClL,OAAA;0BAAKe,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxChB,OAAA;4BAAQe,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAAC;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACxDpB,OAAA;4BAAAgB,QAAA,GAAK,OAAK,GAAAsO,qBAAA,GAACnL,MAAM,CAAC8G,SAAS,CAACC,eAAe,CAACC,QAAQ,cAAAmE,qBAAA,uBAAzCA,qBAAA,CAA2CwB,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAA7P,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACvEpB,OAAA;4BAAAgB,QAAA,GAAK,OAAK,GAAAuO,sBAAA,GAACpL,MAAM,CAAC8G,SAAS,CAACC,eAAe,CAACE,SAAS,cAAAmE,sBAAA,uBAA1CA,sBAAA,CAA4CuB,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAA7P,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrE,CACN,EAGA,EAAAoO,kBAAA,GAAArL,MAAM,CAAC8G,SAAS,cAAAuE,kBAAA,uBAAhBA,kBAAA,CAAkB2B,WAAW,KAAIJ,MAAM,CAACK,IAAI,CAACjN,MAAM,CAAC8G,SAAS,CAACkG,WAAW,CAAC,CAAC1N,MAAM,GAAG,CAAC,iBACpFzD,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAU;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC3BpB,OAAA;4BAAIe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAC/BmD,MAAM,CAAC8G,SAAS,CAACkG,WAAW,CAACE,WAAW,iBACvCrR,OAAA;8BAAAgB,QAAA,GAAI,QAAM,EAACmD,MAAM,CAAC8G,SAAS,CAACkG,WAAW,CAACE,WAAW;4BAAA;8BAAApQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACzD,EACA+C,MAAM,CAAC8G,SAAS,CAACkG,WAAW,CAACG,YAAY,iBACxCtR,OAAA;8BAAAgB,QAAA,GAAI,SAAO,EAACmD,MAAM,CAAC8G,SAAS,CAACkG,WAAW,CAACG,YAAY;4BAAA;8BAAArQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC3D;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN,EAGA,EAAAqO,kBAAA,GAAAtL,MAAM,CAAC8G,SAAS,cAAAwE,kBAAA,uBAAhBA,kBAAA,CAAkB8B,cAAc,KAAIR,MAAM,CAACK,IAAI,CAACjN,MAAM,CAAC8G,SAAS,CAACsG,cAAc,CAAC,CAAC9N,MAAM,GAAG,CAAC,iBAC1FzD,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAa;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC9BpB,OAAA;4BAAIe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAC/BmD,MAAM,CAAC8G,SAAS,CAACsG,cAAc,CAACC,GAAG,iBAClCxR,OAAA;8BAAAgB,QAAA,GAAI,OAAK,EAACmD,MAAM,CAAC8G,SAAS,CAACsG,cAAc,CAACC,GAAG;4BAAA;8BAAAvQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACnD,EACA+C,MAAM,CAAC8G,SAAS,CAACsG,cAAc,CAACE,aAAa,iBAC5CzR,OAAA;8BAAAgB,QAAA,GAAI,YAAU,EAACmD,MAAM,CAAC8G,SAAS,CAACsG,cAAc,CAACE,aAAa;4BAAA;8BAAAxQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAClE;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN,EAGA,EAAAsO,kBAAA,GAAAvL,MAAM,CAAC8G,SAAS,cAAAyE,kBAAA,uBAAhBA,kBAAA,CAAkBgC,UAAU,kBAC3B1R,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC8G,SAAS,CAACyG,UAAU,CAAChD,KAAK,EAAC,QAAG,EAACvK,MAAM,CAAC8G,SAAS,CAACyG,UAAU,CAACzC,MAAM,EACxG9K,MAAM,CAAC8G,SAAS,CAACyG,UAAU,CAACC,MAAM,iBACjC3R,OAAA;4BAAAgB,QAAA,GAAM,IAAE,EAACmD,MAAM,CAAC8G,SAAS,CAACyG,UAAU,CAACC,MAAM,EAAC,GAAC;0BAAA;4BAAA1Q,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACpD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN,EAGA+C,MAAM,CAACnB,UAAU,KAAK,OAAO,iBAC5BhD,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAIe,SAAS,EAAC,WAAW;4BAAAC,QAAA,EAAC;0BAAoB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnDpB,OAAA;4BAAIe,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,GAChC,EAAA2O,gBAAA,GAAAxL,MAAM,CAACiI,QAAQ,cAAAuD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBiC,WAAW,cAAAhC,qBAAA,uBAA5BA,qBAAA,CAA8BiC,QAAQ,kBACrC7R,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAACwI,IAAI,CAACkI,KAAK,CAAC3N,MAAM,CAACiI,QAAQ,CAACwF,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;4BAAA;8BAAA5Q,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CACvF,EACA,EAAAyO,iBAAA,GAAA1L,MAAM,CAACiI,QAAQ,cAAAyD,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB6B,UAAU,cAAA5B,qBAAA,uBAA3BA,qBAAA,CAA6BpB,KAAK,OAAAqB,iBAAA,GAAI5L,MAAM,CAACiI,QAAQ,cAAA2D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB2B,UAAU,cAAA1B,qBAAA,uBAA3BA,qBAAA,CAA6Bf,MAAM,kBACxEjP,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAW;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACiI,QAAQ,CAACsF,UAAU,CAAChD,KAAK,EAAC,GAAC,EAACvK,MAAM,CAACiI,QAAQ,CAACsF,UAAU,CAACzC,MAAM;4BAAA;8BAAAhO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC5G,EACA,EAAA6O,iBAAA,GAAA9L,MAAM,CAACiI,QAAQ,cAAA6D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB2B,WAAW,cAAA1B,qBAAA,uBAA5BA,qBAAA,CAA8B6B,WAAW,kBACxC/R,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAO;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAACiI,QAAQ,CAACwF,WAAW,CAACG,WAAW,CAACzB,WAAW,CAAC,CAAC;4BAAA;8BAAArP,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACzF,EACA+C,MAAM,CAAC0M,QAAQ,iBACd7Q,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC0M,QAAQ;4BAAA;8BAAA5P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACrD,EAEA+C,MAAM,CAAC6N,aAAa,iBACnBhS,OAAA,CAAAE,SAAA;8BAAAc,QAAA,GACGmD,MAAM,CAAC6N,aAAa,CAACC,QAAQ,IAAI9N,MAAM,CAAC6N,aAAa,CAACC,QAAQ,CAACxO,MAAM,GAAG,CAAC,iBACxEzD,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAkB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC6N,aAAa,CAACC,QAAQ,CAACxO,MAAM;8BAAA;gCAAAxC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CACnF,EACA+C,MAAM,CAAC6N,aAAa,CAACE,MAAM,IAAI/N,MAAM,CAAC6N,aAAa,CAACE,MAAM,CAACzO,MAAM,GAAG,CAAC,iBACpEzD,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAgB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC6N,aAAa,CAACE,MAAM,CAACzO,MAAM;8BAAA;gCAAAxC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAC/E,EACA+C,MAAM,CAAC6N,aAAa,CAACG,KAAK,IAAIhO,MAAM,CAAC6N,aAAa,CAACG,KAAK,CAAC1O,MAAM,GAAG,CAAC,iBAClEzD,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAe;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAAC+C,MAAM,CAAC6N,aAAa,CAACG,KAAK,CAAC1O,MAAM;8BAAA;gCAAAxC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAC7E;4BAAA,eACD,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,eAGDpB,OAAA;sBAAKe,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BhB,OAAA,CAACF,IAAI;wBACHsS,EAAE,EAAE,SAASjO,MAAM,CAACF,QAAQ,EAAG;wBAC/BlD,SAAS,EAAC,wBAAwB;wBAClCM,OAAO,EAAG+M,CAAC,IAAKA,CAAC,CAACiE,eAAe,CAAC,CAAE;wBAAArR,QAAA,EACrC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAEN+C,MAAM,CAACnB,UAAU,KAAK,OAAO,IAAImB,MAAM,CAACkI,uBAAuB,iBAC9DrM,OAAA;wBACEsS,IAAI,EAAEnO,MAAM,CAACkI,uBAAwB;wBACrCgC,MAAM,EAAC,QAAQ;wBACfkE,GAAG,EAAC,qBAAqB;wBACzBxR,SAAS,EAAC,kCAAkC;wBAC5CM,OAAO,EAAG+M,CAAC,IAAKA,CAAC,CAACiE,eAAe,CAAC,CAAE;wBAAArR,QAAA,EACrC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC,GA5MH+C,MAAM,CAACwH,EAAE,IAAI,UAAU/B,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;gBAAA5I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6MrC,CAAC;YAEX,CAAC,CAAC;UAAA,GArOG,OAAO8G,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIC,IAAI,EAAE;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsOhC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX;AAACwG,GAAA,CA7oBQF,SAAS;AAAA8K,GAAA,GAAT9K,SAAS;AA+oBlB,eAAeA,SAAS;AAAC,IAAA/E,EAAA,EAAA0D,GAAA,EAAAmM,GAAA;AAAAC,YAAA,CAAA9P,EAAA;AAAA8P,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}