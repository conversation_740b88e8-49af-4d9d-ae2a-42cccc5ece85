#!/usr/bin/env python3
"""
Test script to isolate PDF generation issues
"""

import sys
import os
import io
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_reportlab_import():
    """Test if ReportLab can be imported successfully"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT
        print("✅ ReportLab imports successful")
        return True
    except ImportError as e:
        print(f"❌ ReportLab import failed: {e}")
        return False

def test_simple_pdf_generation():
    """Test basic PDF generation"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Add a simple paragraph
        story.append(Paragraph("Test PDF Generation", styles['Title']))
        story.append(Paragraph("This is a test PDF document.", styles['Normal']))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        
        pdf_data = buffer.getvalue()
        print(f"✅ Simple PDF generated successfully, size: {len(pdf_data)} bytes")
        return True
        
    except Exception as e:
        print(f"❌ Simple PDF generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_defect_pdf_generation():
    """Test the actual defect PDF generation function"""
    try:
        # Import the function from pavement.py
        from routes.pavement import generate_pdf_report
        
        # Create sample defect data
        sample_defect_data = {
            'image_id': 'test_123',
            'timestamp': datetime.now().isoformat(),
            'username': 'test_user',
            'role': 'Supervisor',
            'coordinates': '12.9716, 77.5946',
            'media_type': 'image',
            'potholes': [
                {
                    'pothole_id': 'pothole_1',
                    'area_cm2': 1032.14,
                    'depth_cm': 7.04,
                    'volume': 7271.15,
                    'volume_range': 'High'
                }
            ],
            'cracks': [],
            'kerbs': []
        }
        
        # Generate PDF
        pdf_buffer = generate_pdf_report(sample_defect_data, 'pothole')
        pdf_data = pdf_buffer.getvalue()
        
        print(f"✅ Defect PDF generated successfully, size: {len(pdf_data)} bytes")
        
        # Save to file for testing
        with open('test_defect_report.pdf', 'wb') as f:
            f.write(pdf_data)
        print("✅ Test PDF saved as 'test_defect_report.pdf'")
        
        return True
        
    except Exception as e:
        print(f"❌ Defect PDF generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing PDF Generation Components")
    print("=" * 50)
    
    # Test 1: ReportLab imports
    print("\n1. Testing ReportLab imports...")
    if not test_reportlab_import():
        return False
    
    # Test 2: Simple PDF generation
    print("\n2. Testing simple PDF generation...")
    if not test_simple_pdf_generation():
        return False
    
    # Test 3: Defect PDF generation
    print("\n3. Testing defect PDF generation...")
    if not test_defect_pdf_generation():
        return False
    
    print("\n🎉 All PDF generation tests passed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
