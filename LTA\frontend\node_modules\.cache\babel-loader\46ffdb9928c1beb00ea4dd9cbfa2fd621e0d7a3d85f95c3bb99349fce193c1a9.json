{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\DefectMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, useMap } from 'react-leaflet';\nimport axios from 'axios';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\n// Error Boundary for Map Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass MapErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('Map Error Boundary caught an error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Map Loading Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"There was an issue loading the map. Please refresh the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm\",\n          onClick: () => window.location.reload(),\n          children: \"Refresh Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// Helper component to invalidate map size after mount and on tile load\nfunction MapSizeFix({\n  mapRef\n}) {\n  _s();\n  const map = useMap();\n  useEffect(() => {\n    // Store map reference for parent component\n    if (mapRef) {\n      mapRef.current = map;\n    }\n\n    // Invalidate size shortly after mount with better error handling\n    const t1 = setTimeout(() => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map invalidateSize error (t1):', error);\n      }\n    }, 100);\n    const t2 = setTimeout(() => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map invalidateSize error (t2):', error);\n      }\n    }, 500);\n\n    // Also on window resize with error handling\n    const onResize = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map resize error:', error);\n      }\n    };\n    window.addEventListener('resize', onResize);\n\n    // Invalidate after zoom animations end with error handling\n    const onZoomEnd = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map zoom end error:', error);\n      }\n    };\n    const onMoveEnd = () => {\n      try {\n        if (map && map.invalidateSize) {\n          map.invalidateSize(true);\n        }\n      } catch (error) {\n        console.warn('Map move end error:', error);\n      }\n    };\n    if (map) {\n      map.on('zoomend', onZoomEnd);\n      map.on('moveend', onMoveEnd);\n    }\n    return () => {\n      clearTimeout(t1);\n      clearTimeout(t2);\n      window.removeEventListener('resize', onResize);\n      if (map) {\n        try {\n          map.off('zoomend', onZoomEnd);\n          map.off('moveend', onMoveEnd);\n        } catch (error) {\n          console.warn('Map cleanup error:', error);\n        }\n      }\n    };\n  }, [map, mapRef]);\n  return null;\n}\n\n/**\r\n * Enhanced Image Display Component for Map Popups\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\n_s(MapSizeFix, \"IoceErwr5KVGS9kN4RQ1bOkYMAg=\", false, function () {\n  return [useMap];\n});\n_c = MapSizeFix;\nconst EnhancedMapImageDisplay = ({\n  defect\n}) => {\n  _s2();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n\n  // Generate comprehensive image URL with multiple fallback options\n  const generateImageUrl = defectData => {\n    if (!defectData) return null;\n    console.log('🔍 Generating map image URL for:', defectData.image_id);\n\n    // Priority 1: Try pre-signed URL (most secure and reliable)\n    if (defectData.original_image_presigned_url) {\n      console.log('🔗 Using pre-signed URL for map image');\n      return defectData.original_image_presigned_url;\n    }\n\n    // Priority 2: Try S3 full URL with proxy\n    if (defectData.original_image_full_url) {\n      console.log('🔗 Using full URL field');\n      // Extract S3 key from full URL and use proxy endpoint\n      const urlParts = defectData.original_image_full_url.split('/');\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n        const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n        console.log('✅ Generated proxy URL from full URL:', proxyUrl);\n        return proxyUrl;\n      }\n    }\n\n    // Priority 3: Try S3 key with proxy endpoint\n    if (defectData.original_image_s3_url) {\n      console.log('🔗 Using S3 key field');\n      const s3Key = defectData.original_image_s3_url;\n      const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\n      console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\n      return proxyUrl;\n    }\n\n    // Priority 4: Try direct S3 URL (fallback)\n    if (defectData.original_image_full_url) {\n      console.log('🔗 Using direct S3 URL as fallback');\n      return defectData.original_image_full_url;\n    }\n\n    // Priority 5: Try GridFS endpoint\n    if (defectData.original_image_id) {\n      console.log('🗄️ Using GridFS endpoint');\n      return `/api/pavement/get-image/${defectData.original_image_id}`;\n    }\n    console.warn('❌ No valid image URL found for defect:', defectData.image_id);\n    return null;\n  };\n\n  // Initialize image URL\n  useEffect(() => {\n    const url = generateImageUrl(defect);\n    console.log('🖼️ Setting initial map image URL:', url);\n    setCurrentImageUrl(url);\n    setHasError(false);\n    setIsLoading(true);\n    setFallbackAttempts(0);\n  }, [defect]);\n\n  // Handle image load error with fallback attempts\n  const handleImageError = e => {\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\n    if (fallbackAttempts === 0) {\n      // First fallback: Try GridFS if we haven't already\n      const gridfsId = defect.original_image_id;\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\n        setCurrentImageUrl(gridfsUrl);\n        setFallbackAttempts(1);\n        return;\n      }\n    }\n\n    // All fallbacks exhausted\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\n    setHasError(true);\n    setIsLoading(false);\n  };\n  const handleImageLoad = () => {\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\n    setIsLoading(false);\n    setHasError(false);\n  };\n\n  // Don't render anything if no URL available\n  if (!currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), \" Image not available\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          minHeight: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          variant: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ms-2 small\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render error state\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted small p-2 border rounded bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), \" Image unavailable\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render successful image\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-3 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: \"Defect image\",\n      className: \"img-fluid border rounded\",\n      style: {\n        maxHeight: '150px',\n        maxWidth: '100%'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-primary fw-bold\",\n        children: \"\\uD83D\\uDCF7 Original Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), fallbackAttempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-warning\",\n          children: \"(Fallback source)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n};\n\n// Fix for the Leaflet default icon issue\n_s2(EnhancedMapImageDisplay, \"PL1c4k1ZSbH8d+OQcD09kxd231s=\");\n_c2 = EnhancedMapImageDisplay;\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Custom marker icons for different defect types using location icons\nconst createCustomIcon = (color, isVideo = false) => {\n  const iconSize = isVideo ? [36, 36] : [32, 32];\n\n  // Create different SVG content for video vs image markers\n  const svgContent = isVideo ?\n  // Video marker with camera icon made from SVG shapes (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\n    </svg>` :\n  // Image marker with standard location pin (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>`;\n  return L.icon({\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\n    iconSize: iconSize,\n    iconAnchor: [iconSize[0] / 2, iconSize[1]],\n    popupAnchor: [0, -iconSize[1]]\n  });\n};\nconst icons = {\n  pothole: createCustomIcon('#FF0000'),\n  // Red for potholes\n  crack: createCustomIcon('#FFCC00'),\n  // Yellow for cracks (alligator cracks)\n  kerb: createCustomIcon('#0066FF'),\n  // Blue for kerb defects\n  // Video variants\n  'pothole-video': createCustomIcon('#FF0000', true),\n  // Red for pothole videos\n  'crack-video': createCustomIcon('#FFCC00', true),\n  // Yellow for crack videos\n  'kerb-video': createCustomIcon('#0066FF', true) // Blue for kerb videos\n};\nfunction DefectMap({\n  user\n}) {\n  _s3();\n  const [defects, setDefects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [center] = useState([20.5937, 78.9629]); // India center\n  const [zoom] = useState(6); // Country-wide zoom for India\n  const mapRef = useRef(null);\n\n  // Filters for the map\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [usersList, setUsersList] = useState([]);\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\n    pothole: true,\n    crack: true,\n    kerb: true\n  });\n\n  // Fetch all images with defects and coordinates\n  const fetchDefectData = async (forceRefresh = false) => {\n    try {\n      var _response$data$images;\n      setLoading(true);\n      setError(null); // Clear any previous errors\n\n      // Prepare query parameters\n      let params = {};\n      if (startDate) params.start_date = startDate;\n      if (endDate) params.end_date = endDate;\n      if (selectedUser) params.username = selectedUser;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Always add cache-busting parameter to ensure latest data\n      params._t = Date.now();\n\n      // Add additional cache-busting for force refresh\n      if (forceRefresh) {\n        params._force = 'true';\n        params._refresh = Math.random().toString(36).substring(7);\n      }\n      console.log('🔄 Fetching defect data with params:', params);\n      const response = await axios.get('/api/dashboard/image-stats', {\n        params,\n        // Disable axios caching\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      console.log('📊 API response received:', {\n        success: response.data.success,\n        totalImages: response.data.total_images,\n        imageCount: (_response$data$images = response.data.images) === null || _response$data$images === void 0 ? void 0 : _response$data$images.length\n      });\n      if (response.data.success) {\n        // Process the data to extract defect locations with type information\n        const processedDefects = [];\n        response.data.images.forEach(image => {\n          try {\n            // Only process images with valid coordinates\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\n            if (image.coordinates && image.coordinates !== 'Not Available') {\n              var _image$exif_data;\n              let lat, lng;\n\n              // First, try to use EXIF GPS coordinates if available (most accurate)\n              if ((_image$exif_data = image.exif_data) !== null && _image$exif_data !== void 0 && _image$exif_data.gps_coordinates) {\n                lat = image.exif_data.gps_coordinates.latitude;\n                lng = image.exif_data.gps_coordinates.longitude;\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              } else {\n                // Fallback to stored coordinates\n                // Handle different coordinate formats\n                if (typeof image.coordinates === 'string') {\n                  // Parse coordinates (expected format: \"lat,lng\")\n                  const coords = image.coordinates.split(',');\n                  if (coords.length === 2) {\n                    lat = parseFloat(coords[0].trim());\n                    lng = parseFloat(coords[1].trim());\n                  }\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\n                  // Handle array format [lat, lng]\n                  lat = parseFloat(image.coordinates[0]);\n                  lng = parseFloat(image.coordinates[1]);\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\n                  // Handle object format {lat: x, lng: y}\n                  lat = parseFloat(image.coordinates.lat);\n                  lng = parseFloat(image.coordinates.lng);\n                }\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\n              }\n\n              // Validate coordinates are within reasonable bounds\n              if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\n                processedDefects.push({\n                  id: image.id,\n                  image_id: image.image_id,\n                  type: image.type,\n                  position: [lat, lng],\n                  defect_count: image.defect_count,\n                  timestamp: new Date(image.timestamp).toLocaleString(),\n                  username: image.username,\n                  original_image_id: image.original_image_id,\n                  // For cracks, include type information if available\n                  type_counts: image.type_counts,\n                  // For kerbs, include condition information if available\n                  condition_counts: image.condition_counts,\n                  // EXIF and metadata information\n                  exif_data: image.exif_data || {},\n                  metadata: image.metadata || {},\n                  media_type: image.media_type || 'image',\n                  original_image_full_url: image.original_image_full_url\n                });\n              } else {\n                console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\n              }\n            } else {\n              console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\n            }\n          } catch (coordError) {\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\n          }\n        });\n        console.log('Processed defects:', processedDefects.length);\n        setDefects(processedDefects);\n\n        // If no defects were processed, show a helpful message\n        if (processedDefects.length === 0) {\n          setError('No defects found with valid coordinates for the selected date range');\n        }\n      } else {\n        console.error('API returned success: false', response.data);\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\n      }\n      setLoading(false);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error fetching defect data:', err);\n      setError('Failed to load defect data: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n      setLoading(false);\n    }\n  };\n\n  // Fetch the list of users for the filter dropdown\n  const fetchUsers = async () => {\n    try {\n      const params = {};\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n      const response = await axios.get('/api/users/all', {\n        params\n      });\n      if (response.data.success) {\n        setUsersList(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  // Initialize default dates for the last 30 days\n  useEffect(() => {\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    console.log('DefectMap component initialized');\n  }, []);\n\n  // Fetch data when component mounts and when filters change\n  useEffect(() => {\n    fetchDefectData();\n    fetchUsers();\n  }, [user]);\n\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\n  useEffect(() => {\n    const interval = setInterval(() => {\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\n      fetchDefectData(true); // Force refresh to get latest EXIF data\n    }, 30000); // 30 seconds\n\n    return () => {\n      console.log('🛑 Clearing auto-refresh interval');\n      clearInterval(interval);\n    };\n  }, [startDate, endDate, selectedUser, user === null || user === void 0 ? void 0 : user.role]);\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    fetchDefectData(true);\n  };\n\n  // Handle filter application\n  const handleApplyFilters = () => {\n    fetchDefectData();\n  };\n\n  // Handle resetting filters\n  const handleResetFilters = () => {\n    // Reset date filters to last 30 days\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    setSelectedUser('');\n    setDefectTypeFilters({\n      pothole: true,\n      crack: true,\n      kerb: true\n    });\n\n    // Refetch data with reset filters\n    fetchDefectData();\n  };\n\n  // Handle defect type filter changes\n  const handleDefectTypeFilterChange = type => {\n    setDefectTypeFilters(prevFilters => ({\n      ...prevFilters,\n      [type]: !prevFilters[type]\n    }));\n  };\n\n  // Filter defects based on applied filters\n  const filteredDefects = defects.filter(defect => defectTypeFilters[defect.type]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"shadow-sm dashboard-card mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-0\",\n        children: \"Defect Map View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Date Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: startDate,\n                    onChange: e => setStartDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: endDate,\n                    onChange: e => setEndDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"User Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Select User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedUser,\n                    onChange: e => setSelectedUser(e.target.value),\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 23\n                    }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: user.username,\n                      children: [user.username, \" (\", user.role, \")\"]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Defect Type Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls defect-type-filters\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"defect-type-filter-options\",\n              style: {\n                marginTop: \"0\",\n                paddingLeft: \"0\",\n                width: \"100%\",\n                minWidth: \"150px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"pothole-filter\",\n                label: \"Potholes\",\n                checked: defectTypeFilters.pothole,\n                onChange: () => handleDefectTypeFilterChange('pothole'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"crack-filter\",\n                label: \"Cracks\",\n                checked: defectTypeFilters.crack,\n                onChange: () => handleDefectTypeFilterChange('crack'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"kerb-filter\",\n                label: \"Kerbs\",\n                checked: defectTypeFilters.kerb,\n                onChange: () => handleDefectTypeFilterChange('kerb'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-actions-container\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              onClick: handleApplyFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Apply Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: handleResetFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              onClick: handleRefresh,\n              disabled: loading,\n              className: \"filter-btn\",\n              children: loading ? '🔄 Refreshing...' : '🔄 Refresh Map'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF7 Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Defects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF9 Videos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pothole Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Crack Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"map-container\",\n        style: {\n          height: '500px',\n          width: '100%',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(MapErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(MapContainer, {\n            center: center,\n            zoom: zoom,\n            style: {\n              height: '100%',\n              width: '100%'\n            },\n            scrollWheelZoom: true,\n            children: [/*#__PURE__*/_jsxDEV(MapSizeFix, {\n              mapRef: mapRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TileLayer, {\n              attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n              url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this), filteredDefects.map(defect => {\n              var _defect$exif_data, _defect$exif_data$gps, _defect$exif_data$gps2, _defect$exif_data2, _defect$exif_data3, _defect$exif_data4, _defect$metadata, _defect$metadata$form, _defect$metadata2, _defect$metadata2$bas, _defect$metadata3, _defect$metadata3$bas, _defect$metadata4, _defect$metadata4$for;\n              // Determine icon based on media type and defect type\n              const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\n              const selectedIcon = icons[iconKey] || icons[defect.type];\n              return /*#__PURE__*/_jsxDEV(Marker, {\n                position: defect.position,\n                icon: selectedIcon,\n                children: /*#__PURE__*/_jsxDEV(Popup, {\n                  maxWidth: 400,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"defect-popup\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: [defect.type.charAt(0).toUpperCase() + defect.type.slice(1), \" Defect\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 23\n                    }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 text-center\",\n                      children: [defect.representative_frame ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: `data:image/jpeg;base64,${defect.representative_frame}`,\n                          alt: \"Video thumbnail\",\n                          className: \"img-fluid border rounded shadow-sm\",\n                          style: {\n                            maxHeight: '150px',\n                            maxWidth: '100%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            console.warn(`Failed to load representative frame for video ${defect.image_id}`);\n                            e.target.style.display = 'none';\n                            // Show fallback message\n                            const fallback = e.target.nextElementSibling;\n                            if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\n                              fallback.style.display = 'block';\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 780,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\",\n                          style: {\n                            display: 'none'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-video\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 796,\n                            columnNumber: 33\n                          }, this), \" Video thumbnail unavailable\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 795,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted small p-3 border rounded bg-light\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-video fa-2x mb-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 801,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: \"Video thumbnail not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 802,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 800,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-info fw-bold\",\n                          children: \"\\uD83D\\uDCF9 Video Thumbnail\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 806,\n                          columnNumber: 29\n                        }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted d-block\",\n                            children: [\"Video ID: \", defect.video_id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 809,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 808,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 777,\n                      columnNumber: 25\n                    }, this), defect.media_type !== 'video' && /*#__PURE__*/_jsxDEV(EnhancedMapImageDisplay, {\n                      defect: defect\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 818,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Basic Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 823,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Count:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 31\n                          }, this), \" \", defect.defect_count]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 825,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Date:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 826,\n                            columnNumber: 31\n                          }, this), \" \", defect.timestamp]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 826,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Reported by:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 827,\n                            columnNumber: 31\n                          }, this), \" \", defect.username]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 827,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Media Type:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 31\n                          }, this), \" \", defect.media_type]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 828,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"GPS:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 829,\n                            columnNumber: 31\n                          }, this), \" \", defect.position[0].toFixed(6), \", \", defect.position[1].toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 829,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 824,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 822,\n                      columnNumber: 23\n                    }, this), defect.type === 'crack' && defect.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Crack Types\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 836,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: Object.entries(defect.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [type, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 839,\n                            columnNumber: 46\n                          }, this), \" \", count]\n                        }, type, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 839,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 25\n                    }, this), defect.type === 'kerb' && defect.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"Kerb Conditions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 847,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"list-unstyled small\",\n                        children: Object.entries(defect.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [condition, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 850,\n                            columnNumber: 51\n                          }, this), \" \", count]\n                        }, condition, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 846,\n                      columnNumber: 25\n                    }, this), (defect.exif_data || defect.metadata) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"text-primary\",\n                        children: \"\\uD83D\\uDCCA Media Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 859,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"small\",\n                        children: [((_defect$exif_data = defect.exif_data) === null || _defect$exif_data === void 0 ? void 0 : _defect$exif_data.gps_coordinates) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2 p-2 bg-light rounded\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"text-success\",\n                            children: \"\\uD83C\\uDF0D GPS (EXIF):\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 864,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Lat: \", (_defect$exif_data$gps = defect.exif_data.gps_coordinates.latitude) === null || _defect$exif_data$gps === void 0 ? void 0 : _defect$exif_data$gps.toFixed(6)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 865,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Lng: \", (_defect$exif_data$gps2 = defect.exif_data.gps_coordinates.longitude) === null || _defect$exif_data$gps2 === void 0 ? void 0 : _defect$exif_data$gps2.toFixed(6)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 866,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 863,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data2 = defect.exif_data) === null || _defect$exif_data2 === void 0 ? void 0 : _defect$exif_data2.camera_info) && Object.keys(defect.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\uD83D\\uDCF7 Camera:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 873,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled ms-2\",\n                            children: [defect.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Make: \", defect.exif_data.camera_info.camera_make]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 876,\n                              columnNumber: 37\n                            }, this), defect.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Model: \", defect.exif_data.camera_info.camera_model]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 879,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 874,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 872,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data3 = defect.exif_data) === null || _defect$exif_data3 === void 0 ? void 0 : _defect$exif_data3.technical_info) && Object.keys(defect.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u2699\\uFE0F Technical:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 888,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled ms-2\",\n                            children: [defect.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"ISO: \", defect.exif_data.technical_info.iso]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 891,\n                              columnNumber: 37\n                            }, this), defect.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [\"Exposure: \", defect.exif_data.technical_info.exposure_time]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 894,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 889,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 887,\n                          columnNumber: 31\n                        }, this), ((_defect$exif_data4 = defect.exif_data) === null || _defect$exif_data4 === void 0 ? void 0 : _defect$exif_data4.basic_info) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\uD83D\\uDCD0 Dimensions:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 903,\n                            columnNumber: 33\n                          }, this), \" \", defect.exif_data.basic_info.width, \" \\xD7 \", defect.exif_data.basic_info.height, defect.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [\" (\", defect.exif_data.basic_info.format, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 905,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 902,\n                          columnNumber: 31\n                        }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"text-info\",\n                            children: \"\\uD83D\\uDCF9 Video Information\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 913,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"list-unstyled small\",\n                            children: [((_defect$metadata = defect.metadata) === null || _defect$metadata === void 0 ? void 0 : (_defect$metadata$form = _defect$metadata.format_info) === null || _defect$metadata$form === void 0 ? void 0 : _defect$metadata$form.duration) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Duration:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 916,\n                                columnNumber: 41\n                              }, this), \" \", Math.round(defect.metadata.format_info.duration), \"s\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 916,\n                              columnNumber: 37\n                            }, this), ((_defect$metadata2 = defect.metadata) === null || _defect$metadata2 === void 0 ? void 0 : (_defect$metadata2$bas = _defect$metadata2.basic_info) === null || _defect$metadata2$bas === void 0 ? void 0 : _defect$metadata2$bas.width) && ((_defect$metadata3 = defect.metadata) === null || _defect$metadata3 === void 0 ? void 0 : (_defect$metadata3$bas = _defect$metadata3.basic_info) === null || _defect$metadata3$bas === void 0 ? void 0 : _defect$metadata3$bas.height) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Resolution:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 919,\n                                columnNumber: 41\n                              }, this), \" \", defect.metadata.basic_info.width, \"x\", defect.metadata.basic_info.height]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 919,\n                              columnNumber: 37\n                            }, this), ((_defect$metadata4 = defect.metadata) === null || _defect$metadata4 === void 0 ? void 0 : (_defect$metadata4$for = _defect$metadata4.format_info) === null || _defect$metadata4$for === void 0 ? void 0 : _defect$metadata4$for.format_name) && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Format:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 922,\n                                columnNumber: 41\n                              }, this), \" \", defect.metadata.format_info.format_name.toUpperCase()]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 922,\n                              columnNumber: 37\n                            }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Video ID:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 925,\n                                columnNumber: 41\n                              }, this), \" \", defect.video_id]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 925,\n                              columnNumber: 37\n                            }, this), defect.model_outputs && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Potholes Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 931,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.potholes.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 931,\n                                columnNumber: 41\n                              }, this), defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Cracks Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 934,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.cracks.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 934,\n                                columnNumber: 41\n                              }, this), defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Kerbs Detected:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 937,\n                                  columnNumber: 45\n                                }, this), \" \", defect.model_outputs.kerbs.length]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 937,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 914,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 912,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 860,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        to: `/view/${defect.image_id}`,\n                        className: \"btn btn-sm btn-primary\",\n                        onClick: e => e.stopPropagation(),\n                        children: \"View Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 950,\n                        columnNumber: 25\n                      }, this), defect.media_type !== 'video' && defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: defect.original_image_full_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"btn btn-sm btn-outline-secondary\",\n                        onClick: e => e.stopPropagation(),\n                        children: \"View Original\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 959,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 19\n                }, this)\n              }, defect.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 17\n              }, this);\n            })]\n          }, `map-${center[0]}-${center[1]}-${zoom}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 574,\n    columnNumber: 5\n  }, this);\n}\n_s3(DefectMap, \"va2KsqkWO8jHrIyKplfdw8m/S68=\");\n_c3 = DefectMap;\nexport default DefectMap;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MapSizeFix\");\n$RefreshReg$(_c2, \"EnhancedMapImageDisplay\");\n$RefreshReg$(_c3, \"DefectMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "useMap", "axios", "L", "Card", "Row", "Col", "Form", "<PERSON><PERSON>", "Spinner", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MapErrorBoundary", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "MapSizeFix", "mapRef", "_s", "map", "current", "t1", "setTimeout", "invalidateSize", "warn", "t2", "onResize", "addEventListener", "onZoomEnd", "onMoveEnd", "on", "clearTimeout", "removeEventListener", "off", "_c", "EnhancedMapImageDisplay", "defect", "_s2", "currentImageUrl", "setCurrentImageUrl", "setHasError", "isLoading", "setIsLoading", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "generateImageUrl", "defectData", "log", "image_id", "original_image_presigned_url", "original_image_full_url", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "length", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "original_image_s3_url", "<PERSON><PERSON><PERSON>", "original_image_id", "url", "handleImageError", "e", "gridfsId", "gridfsUrl", "handleImageLoad", "style", "minHeight", "animation", "size", "variant", "src", "alt", "maxHeight", "max<PERSON><PERSON><PERSON>", "onError", "onLoad", "_c2", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createCustomIcon", "color", "isVideo", "iconSize", "svgContent", "icon", "iconAnchor", "popupAnchor", "icons", "pothole", "crack", "kerb", "DefectMap", "user", "_s3", "defects", "setDefects", "loading", "setLoading", "setError", "center", "zoom", "startDate", "setStartDate", "endDate", "setEndDate", "selected<PERSON>ser", "setSelectedUser", "usersList", "setUsersList", "defectTypeFilters", "setDefectTypeFilters", "fetchDefectData", "forceRefresh", "_response$data$images", "params", "start_date", "end_date", "username", "role", "user_role", "_t", "Date", "now", "_force", "_refresh", "Math", "random", "toString", "substring", "response", "get", "headers", "success", "data", "totalImages", "total_images", "imageCount", "images", "processedDefects", "for<PERSON>ach", "image", "media_type", "coordinates", "type", "_image$exif_data", "lat", "lng", "exif_data", "gps_coordinates", "latitude", "longitude", "coords", "parseFloat", "trim", "Array", "isArray", "isNaN", "id", "push", "position", "defect_count", "timestamp", "toLocaleString", "type_counts", "condition_counts", "metadata", "coordError", "message", "err", "_err$response", "_err$response$data", "fetchUsers", "users", "currentDate", "thirtyDaysAgo", "setDate", "getDate", "toISOString", "interval", "setInterval", "clearInterval", "handleRefresh", "handleApplyFilters", "handleResetFilters", "handleDefectTypeFilterChange", "prevFilters", "filteredDefects", "filter", "Header", "Body", "lg", "Group", "Label", "Control", "value", "onChange", "target", "Select", "index", "marginTop", "paddingLeft", "width", "min<PERSON><PERSON><PERSON>", "Check", "label", "checked", "disabled", "backgroundColor", "height", "scrollWheelZoom", "attribution", "_defect$exif_data", "_defect$exif_data$gps", "_defect$exif_data$gps2", "_defect$exif_data2", "_defect$exif_data3", "_defect$exif_data4", "_defect$metadata", "_defect$metadata$form", "_defect$metadata2", "_defect$metadata2$bas", "_defect$metadata3", "_defect$metadata3$bas", "_defect$metadata4", "_defect$metadata4$for", "<PERSON><PERSON><PERSON>", "selectedIcon", "char<PERSON>t", "toUpperCase", "representative_frame", "objectFit", "display", "fallback", "nextElement<PERSON><PERSON>ling", "classList", "contains", "video_id", "toFixed", "Object", "entries", "count", "condition", "camera_info", "keys", "camera_make", "camera_model", "technical_info", "iso", "exposure_time", "basic_info", "format", "format_info", "duration", "round", "format_name", "model_outputs", "potholes", "cracks", "kerbs", "to", "stopPropagation", "href", "rel", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/DefectMap.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';\r\nimport axios from 'axios';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport { Card, Row, Col, Form, Button, Spinner } from 'react-bootstrap';\r\nimport { Link } from 'react-router-dom';\r\n\r\n// Error Boundary for Map Component\r\nclass MapErrorBoundary extends React.Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = { hasError: false, error: null };\r\n  }\r\n\r\n  static getDerivedStateFromError(error) {\r\n    return { hasError: true, error };\r\n  }\r\n\r\n  componentDidCatch(error, errorInfo) {\r\n    console.error('Map Error Boundary caught an error:', error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      return (\r\n        <div className=\"alert alert-danger\">\r\n          <h6>Map Loading Error</h6>\r\n          <p>There was an issue loading the map. Please refresh the page.</p>\r\n          <button\r\n            className=\"btn btn-primary btn-sm\"\r\n            onClick={() => window.location.reload()}\r\n          >\r\n            Refresh Page\r\n          </button>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\n// Helper component to invalidate map size after mount and on tile load\r\nfunction MapSizeFix({ mapRef }) {\r\n  const map = useMap();\r\n\r\n  useEffect(() => {\r\n    // Store map reference for parent component\r\n    if (mapRef) {\r\n      mapRef.current = map;\r\n    }\r\n\r\n    // Invalidate size shortly after mount with better error handling\r\n    const t1 = setTimeout(() => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map invalidateSize error (t1):', error);\r\n      }\r\n    }, 100);\r\n\r\n    const t2 = setTimeout(() => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map invalidateSize error (t2):', error);\r\n      }\r\n    }, 500);\r\n\r\n    // Also on window resize with error handling\r\n    const onResize = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map resize error:', error);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', onResize);\r\n\r\n    // Invalidate after zoom animations end with error handling\r\n    const onZoomEnd = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map zoom end error:', error);\r\n      }\r\n    };\r\n\r\n    const onMoveEnd = () => {\r\n      try {\r\n        if (map && map.invalidateSize) {\r\n          map.invalidateSize(true);\r\n        }\r\n      } catch (error) {\r\n        console.warn('Map move end error:', error);\r\n      }\r\n    };\r\n\r\n    if (map) {\r\n      map.on('zoomend', onZoomEnd);\r\n      map.on('moveend', onMoveEnd);\r\n    }\r\n\r\n    return () => {\r\n      clearTimeout(t1);\r\n      clearTimeout(t2);\r\n      window.removeEventListener('resize', onResize);\r\n      if (map) {\r\n        try {\r\n          map.off('zoomend', onZoomEnd);\r\n          map.off('moveend', onMoveEnd);\r\n        } catch (error) {\r\n          console.warn('Map cleanup error:', error);\r\n        }\r\n      }\r\n    };\r\n  }, [map, mapRef]);\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Enhanced Image Display Component for Map Popups\r\n * Handles comprehensive URL resolution with multiple fallback mechanisms\r\n */\r\nconst EnhancedMapImageDisplay = ({ defect }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n\r\n  // Generate comprehensive image URL with multiple fallback options\r\n  const generateImageUrl = (defectData) => {\r\n    if (!defectData) return null;\r\n\r\n    console.log('🔍 Generating map image URL for:', defectData.image_id);\r\n\r\n    // Priority 1: Try pre-signed URL (most secure and reliable)\r\n    if (defectData.original_image_presigned_url) {\r\n      console.log('🔗 Using pre-signed URL for map image');\r\n      return defectData.original_image_presigned_url;\r\n    }\r\n\r\n    // Priority 2: Try S3 full URL with proxy\r\n    if (defectData.original_image_full_url) {\r\n      console.log('🔗 Using full URL field');\r\n      // Extract S3 key from full URL and use proxy endpoint\r\n      const urlParts = defectData.original_image_full_url.split('/');\r\n      const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n        const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n        const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n        console.log('✅ Generated proxy URL from full URL:', proxyUrl);\r\n        return proxyUrl;\r\n      }\r\n    }\r\n\r\n    // Priority 3: Try S3 key with proxy endpoint\r\n    if (defectData.original_image_s3_url) {\r\n      console.log('🔗 Using S3 key field');\r\n      const s3Key = defectData.original_image_s3_url;\r\n      const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodedKey}`;\r\n      console.log('✅ Generated proxy URL from S3 key:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n\r\n    // Priority 4: Try direct S3 URL (fallback)\r\n    if (defectData.original_image_full_url) {\r\n      console.log('🔗 Using direct S3 URL as fallback');\r\n      return defectData.original_image_full_url;\r\n    }\r\n\r\n    // Priority 5: Try GridFS endpoint\r\n    if (defectData.original_image_id) {\r\n      console.log('🗄️ Using GridFS endpoint');\r\n      return `/api/pavement/get-image/${defectData.original_image_id}`;\r\n    }\r\n\r\n    console.warn('❌ No valid image URL found for defect:', defectData.image_id);\r\n    return null;\r\n  };\r\n\r\n  // Initialize image URL\r\n  useEffect(() => {\r\n    const url = generateImageUrl(defect);\r\n    console.log('🖼️ Setting initial map image URL:', url);\r\n    setCurrentImageUrl(url);\r\n    setHasError(false);\r\n    setIsLoading(true);\r\n    setFallbackAttempts(0);\r\n  }, [defect]);\r\n\r\n  // Handle image load error with fallback attempts\r\n  const handleImageError = (e) => {\r\n    console.warn(`❌ Map image load failed (attempt ${fallbackAttempts + 1}):`, currentImageUrl);\r\n\r\n    if (fallbackAttempts === 0) {\r\n      // First fallback: Try GridFS if we haven't already\r\n      const gridfsId = defect.original_image_id;\r\n      if (gridfsId && !currentImageUrl.includes('get-image')) {\r\n        const gridfsUrl = `/api/pavement/get-image/${gridfsId}`;\r\n        console.log('🔄 Trying GridFS fallback:', gridfsUrl);\r\n        setCurrentImageUrl(gridfsUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks exhausted\r\n    console.error('❌ All map image fallbacks exhausted for:', defect.image_id);\r\n    setHasError(true);\r\n    setIsLoading(false);\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    console.log('✅ Map image loaded successfully:', currentImageUrl);\r\n    setIsLoading(false);\r\n    setHasError(false);\r\n  };\r\n\r\n  // Don't render anything if no URL available\r\n  if (!currentImageUrl) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small\">\r\n          <i className=\"fas fa-image\"></i> Image not available\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '100px' }}>\r\n          <Spinner animation=\"border\" size=\"sm\" variant=\"primary\" />\r\n          <span className=\"ms-2 small\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (hasError) {\r\n    return (\r\n      <div className=\"mb-3 text-center\">\r\n        <div className=\"text-muted small p-2 border rounded bg-light\">\r\n          <i className=\"fas fa-exclamation-triangle\"></i> Image unavailable\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render successful image\r\n  return (\r\n    <div className=\"mb-3 text-center\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt=\"Defect image\"\r\n        className=\"img-fluid border rounded\"\r\n        style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n        onError={handleImageError}\r\n        onLoad={handleImageLoad}\r\n      />\r\n      <div className=\"mt-1\">\r\n        <small className=\"text-primary fw-bold\">📷 Original Image</small>\r\n        {fallbackAttempts > 0 && (\r\n          <div>\r\n            <small className=\"text-warning\">(Fallback source)</small>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Fix for the Leaflet default icon issue\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\r\n});\r\n\r\n// Custom marker icons for different defect types using location icons\r\nconst createCustomIcon = (color, isVideo = false) => {\r\n  const iconSize = isVideo ? [36, 36] : [32, 32];\r\n\r\n  // Create different SVG content for video vs image markers\r\n  const svgContent = isVideo ?\r\n    // Video marker with camera icon made from SVG shapes (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\r\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\r\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\r\n    </svg>` :\r\n    // Image marker with standard location pin (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n    </svg>`;\r\n\r\n  return L.icon({\r\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\r\n    iconSize: iconSize,\r\n    iconAnchor: [iconSize[0]/2, iconSize[1]],\r\n    popupAnchor: [0, -iconSize[1]],\r\n  });\r\n};\r\n\r\nconst icons = {\r\n  pothole: createCustomIcon('#FF0000'), // Red for potholes\r\n  crack: createCustomIcon('#FFCC00'),   // Yellow for cracks (alligator cracks)\r\n  kerb: createCustomIcon('#0066FF'),    // Blue for kerb defects\r\n  // Video variants\r\n  'pothole-video': createCustomIcon('#FF0000', true), // Red for pothole videos\r\n  'crack-video': createCustomIcon('#FFCC00', true),   // Yellow for crack videos\r\n  'kerb-video': createCustomIcon('#0066FF', true),    // Blue for kerb videos\r\n};\r\n\r\nfunction DefectMap({ user }) {\r\n  const [defects, setDefects] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [center] = useState([20.5937, 78.9629]); // India center\r\n  const [zoom] = useState(6); // Country-wide zoom for India\r\n  const mapRef = useRef(null);\r\n  \r\n  // Filters for the map\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\r\n    pothole: true,\r\n    crack: true,\r\n    kerb: true,\r\n  });\r\n\r\n  // Fetch all images with defects and coordinates\r\n  const fetchDefectData = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Prepare query parameters\r\n      let params = {};\r\n      if (startDate) params.start_date = startDate;\r\n      if (endDate) params.end_date = endDate;\r\n      if (selectedUser) params.username = selectedUser;\r\n      if (user?.role) params.user_role = user.role;\r\n\r\n      // Always add cache-busting parameter to ensure latest data\r\n      params._t = Date.now();\r\n\r\n      // Add additional cache-busting for force refresh\r\n      if (forceRefresh) {\r\n        params._force = 'true';\r\n        params._refresh = Math.random().toString(36).substring(7);\r\n      }\r\n\r\n      console.log('🔄 Fetching defect data with params:', params);\r\n      const response = await axios.get('/api/dashboard/image-stats', {\r\n        params,\r\n        // Disable axios caching\r\n        headers: {\r\n          'Cache-Control': 'no-cache',\r\n          'Pragma': 'no-cache'\r\n        }\r\n      });\r\n      console.log('📊 API response received:', {\r\n        success: response.data.success,\r\n        totalImages: response.data.total_images,\r\n        imageCount: response.data.images?.length\r\n      });\r\n\r\n      if (response.data.success) {\r\n        // Process the data to extract defect locations with type information\r\n        const processedDefects = [];\r\n        \r\n        response.data.images.forEach(image => {\r\n          try {\r\n            // Only process images with valid coordinates\r\n            console.log(`🔍 Processing ${image.media_type || 'image'} ${image.image_id}: coordinates=${image.coordinates}, type=${image.type}`);\r\n            if (image.coordinates && image.coordinates !== 'Not Available') {\r\n              let lat, lng;\r\n\r\n              // First, try to use EXIF GPS coordinates if available (most accurate)\r\n              if (image.exif_data?.gps_coordinates) {\r\n                lat = image.exif_data.gps_coordinates.latitude;\r\n                lng = image.exif_data.gps_coordinates.longitude;\r\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              } else {\r\n                // Fallback to stored coordinates\r\n                // Handle different coordinate formats\r\n                if (typeof image.coordinates === 'string') {\r\n                  // Parse coordinates (expected format: \"lat,lng\")\r\n                  const coords = image.coordinates.split(',');\r\n                  if (coords.length === 2) {\r\n                    lat = parseFloat(coords[0].trim());\r\n                    lng = parseFloat(coords[1].trim());\r\n                  }\r\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\r\n                  // Handle array format [lat, lng]\r\n                  lat = parseFloat(image.coordinates[0]);\r\n                  lng = parseFloat(image.coordinates[1]);\r\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\r\n                  // Handle object format {lat: x, lng: y}\r\n                  lat = parseFloat(image.coordinates.lat);\r\n                  lng = parseFloat(image.coordinates.lng);\r\n                }\r\n                console.log(`📍 Using stored coordinates for ${image.media_type || 'image'} ${image.image_id}: [${lat}, ${lng}]`);\r\n              }\r\n\r\n            // Validate coordinates are within reasonable bounds\r\n            if (!isNaN(lat) && !isNaN(lng) &&\r\n                lat >= -90 && lat <= 90 &&\r\n                lng >= -180 && lng <= 180) {\r\n              console.log(`✅ Valid coordinates for ${image.media_type || 'image'} ${image.id}: [${lat}, ${lng}] - Adding to map`);\r\n              processedDefects.push({\r\n                id: image.id,\r\n                image_id: image.image_id,\r\n                type: image.type,\r\n                position: [lat, lng],\r\n                defect_count: image.defect_count,\r\n                timestamp: new Date(image.timestamp).toLocaleString(),\r\n                username: image.username,\r\n                original_image_id: image.original_image_id,\r\n                // For cracks, include type information if available\r\n                type_counts: image.type_counts,\r\n                // For kerbs, include condition information if available\r\n                condition_counts: image.condition_counts,\r\n                // EXIF and metadata information\r\n                exif_data: image.exif_data || {},\r\n                metadata: image.metadata || {},\r\n                media_type: image.media_type || 'image',\r\n                original_image_full_url: image.original_image_full_url\r\n              });\r\n            } else {\r\n              console.warn(`❌ Invalid coordinates for ${image.media_type || 'image'} ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Skipping ${image.media_type || 'image'} ${image.id}: coordinates=${image.coordinates}`);\r\n          }\r\n          } catch (coordError) {\r\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\r\n          }\r\n        });\r\n\r\n        console.log('Processed defects:', processedDefects.length);\r\n        setDefects(processedDefects);\r\n\r\n        // If no defects were processed, show a helpful message\r\n        if (processedDefects.length === 0) {\r\n          setError('No defects found with valid coordinates for the selected date range');\r\n        }\r\n      } else {\r\n        console.error('API returned success: false', response.data);\r\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching defect data:', err);\r\n      setError('Failed to load defect data: ' + (err.response?.data?.message || err.message));\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Fetch the list of users for the filter dropdown\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const params = {};\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      const response = await axios.get('/api/users/all', { params });\r\n      if (response.data.success) {\r\n        setUsersList(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  };\r\n\r\n  // Initialize default dates for the last 30 days\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n\r\n    console.log('DefectMap component initialized');\r\n  }, []);\r\n\r\n  // Fetch data when component mounts and when filters change\r\n  useEffect(() => {\r\n    fetchDefectData();\r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Auto-refresh every 30 seconds to catch new uploads with EXIF data\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      console.log('🔄 Auto-refreshing defect data for latest EXIF coordinates...');\r\n      fetchDefectData(true); // Force refresh to get latest EXIF data\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => {\r\n      console.log('🛑 Clearing auto-refresh interval');\r\n      clearInterval(interval);\r\n    };\r\n  }, [startDate, endDate, selectedUser, user?.role]);\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    fetchDefectData(true);\r\n  };\r\n\r\n  // Handle filter application\r\n  const handleApplyFilters = () => {\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle resetting filters\r\n  const handleResetFilters = () => {\r\n    // Reset date filters to last 30 days\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n    \r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n    setSelectedUser('');\r\n    setDefectTypeFilters({\r\n      pothole: true,\r\n      crack: true,\r\n      kerb: true,\r\n    });\r\n    \r\n    // Refetch data with reset filters\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle defect type filter changes\r\n  const handleDefectTypeFilterChange = (type) => {\r\n    setDefectTypeFilters(prevFilters => ({\r\n      ...prevFilters,\r\n      [type]: !prevFilters[type]\r\n    }));\r\n  };\r\n\r\n  // Filter defects based on applied filters\r\n  const filteredDefects = defects.filter(defect => \r\n    defectTypeFilters[defect.type]\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-sm dashboard-card mb-4\">\r\n      <Card.Header className=\"bg-primary text-white\">\r\n        <h5 className=\"mb-0\">Defect Map View</h5>\r\n      </Card.Header>\r\n      <Card.Body>\r\n        <Row className=\"mb-3\">\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Date Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Start Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={startDate}\r\n                      onChange={(e) => setStartDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">End Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={endDate}\r\n                      onChange={(e) => setEndDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>User Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Select User</Form.Label>\r\n                    <Form.Select\r\n                      value={selectedUser}\r\n                      onChange={(e) => setSelectedUser(e.target.value)}\r\n                      size=\"sm\"\r\n                    >\r\n                      <option value=\"\">All Users</option>\r\n                      {usersList.map((user, index) => (\r\n                        <option key={index} value={user.username}>\r\n                          {user.username} ({user.role})\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Defect Type Filter</h6>\r\n            <div className=\"filter-controls defect-type-filters\">\r\n              <div className=\"defect-type-filter-options\" style={{ marginTop: \"0\", paddingLeft: \"0\", width: \"100%\", minWidth: \"150px\" }}>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"pothole-filter\"\r\n                  label=\"Potholes\"\r\n                  checked={defectTypeFilters.pothole}\r\n                  onChange={() => handleDefectTypeFilterChange('pothole')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"crack-filter\"\r\n                  label=\"Cracks\"\r\n                  checked={defectTypeFilters.crack}\r\n                  onChange={() => handleDefectTypeFilterChange('crack')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"kerb-filter\"\r\n                  label=\"Kerbs\"\r\n                  checked={defectTypeFilters.kerb}\r\n                  onChange={() => handleDefectTypeFilterChange('kerb')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        <Row className=\"mb-3\">\r\n          <Col className=\"text-center\">\r\n            <div className=\"filter-actions-container\">\r\n              <Button \r\n                variant=\"primary\" \r\n                size=\"sm\" \r\n                onClick={handleApplyFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Apply Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={handleResetFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Reset Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading}\r\n                className=\"filter-btn\"\r\n              >\r\n                {loading ? '🔄 Refreshing...' : '🔄 Refresh Map'}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Row>\r\n          <Col>\r\n            <div className=\"map-legend mb-3\">\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📷 Images</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FF0000' }}></div>\r\n                  <span>Potholes</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FFCC00' }}></div>\r\n                  <span>Cracks</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#0066FF' }}></div>\r\n                  <span>Kerb Defects</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📹 Videos</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FF0000' }}>📹</div>\r\n                  <span>Pothole Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FFCC00' }}>📹</div>\r\n                  <span>Crack Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#0066FF' }}>📹</div>\r\n                  <span>Kerb Videos</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center p-5\">\r\n            <div className=\"spinner-border text-primary\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"alert alert-danger\">{error}</div>\r\n        ) : (\r\n          <div className=\"map-container\" style={{ height: '500px', width: '100%', position: 'relative' }}>\r\n            <MapErrorBoundary>\r\n              <MapContainer\r\n              center={center}\r\n              zoom={zoom}\r\n              style={{ height: '100%', width: '100%' }}\r\n              scrollWheelZoom={true}\r\n              key={`map-${center[0]}-${center[1]}-${zoom}`} // Force remount on center/zoom change\r\n            >\r\n              {/* Ensure Leaflet recalculates size after mount/visibility */}\r\n              <MapSizeFix mapRef={mapRef} />\r\n              <TileLayer\r\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              />\r\n              \r\n              {filteredDefects.map((defect) => {\r\n                // Determine icon based on media type and defect type\r\n                const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\r\n                const selectedIcon = icons[iconKey] || icons[defect.type];\r\n\r\n                return (\r\n                <Marker\r\n                  key={defect.id}\r\n                  position={defect.position}\r\n                  icon={selectedIcon}\r\n                >\r\n                  <Popup maxWidth={400}>\r\n                    <div className=\"defect-popup\">\r\n                      <h6>{defect.type.charAt(0).toUpperCase() + defect.type.slice(1)} Defect</h6>\r\n\r\n                      {/* Video Thumbnail */}\r\n                      {defect.media_type === 'video' && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          {defect.representative_frame ? (\r\n                            <>\r\n                              <img\r\n                                src={`data:image/jpeg;base64,${defect.representative_frame}`}\r\n                                alt=\"Video thumbnail\"\r\n                                className=\"img-fluid border rounded shadow-sm\"\r\n                                style={{ maxHeight: '150px', maxWidth: '100%', objectFit: 'cover' }}\r\n                                onError={(e) => {\r\n                                  console.warn(`Failed to load representative frame for video ${defect.image_id}`);\r\n                                  e.target.style.display = 'none';\r\n                                  // Show fallback message\r\n                                  const fallback = e.target.nextElementSibling;\r\n                                  if (fallback && fallback.classList.contains('video-thumbnail-fallback')) {\r\n                                    fallback.style.display = 'block';\r\n                                  }\r\n                                }}\r\n                              />\r\n                              <div className=\"video-thumbnail-fallback text-muted small p-2 border rounded bg-light\" style={{ display: 'none' }}>\r\n                                <i className=\"fas fa-video\"></i> Video thumbnail unavailable\r\n                              </div>\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"text-muted small p-3 border rounded bg-light\">\r\n                              <i className=\"fas fa-video fa-2x mb-2\"></i>\r\n                              <div>Video thumbnail not available</div>\r\n                            </div>\r\n                          )}\r\n                          <div className=\"mt-2\">\r\n                            <small className=\"text-info fw-bold\">📹 Video Thumbnail</small>\r\n                            {defect.video_id && (\r\n                              <div>\r\n                                <small className=\"text-muted d-block\">Video ID: {defect.video_id}</small>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Enhanced Image Display with Fallbacks */}\r\n                      {defect.media_type !== 'video' && (\r\n                        <EnhancedMapImageDisplay defect={defect} />\r\n                      )}\r\n\r\n                      {/* Basic Information */}\r\n                      <div className=\"mb-3\">\r\n                        <h6 className=\"text-primary\">Basic Information</h6>\r\n                        <ul className=\"list-unstyled small\">\r\n                          <li><strong>Count:</strong> {defect.defect_count}</li>\r\n                          <li><strong>Date:</strong> {defect.timestamp}</li>\r\n                          <li><strong>Reported by:</strong> {defect.username}</li>\r\n                          <li><strong>Media Type:</strong> {defect.media_type}</li>\r\n                          <li><strong>GPS:</strong> {defect.position[0].toFixed(6)}, {defect.position[1].toFixed(6)}</li>\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Defect-specific Information */}\r\n                      {defect.type === 'crack' && defect.type_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Crack Types</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.type_counts).map(([type, count]) => (\r\n                              <li key={type}><strong>{type}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {defect.type === 'kerb' && defect.condition_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Kerb Conditions</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.condition_counts).map(([condition, count]) => (\r\n                              <li key={condition}><strong>{condition}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* EXIF/Metadata Information */}\r\n                      {(defect.exif_data || defect.metadata) && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">📊 Media Information</h6>\r\n                          <div className=\"small\">\r\n                            {/* GPS Information - Show first as it's most important for mapping */}\r\n                            {defect.exif_data?.gps_coordinates && (\r\n                              <div className=\"mb-2 p-2 bg-light rounded\">\r\n                                <strong className=\"text-success\">🌍 GPS (EXIF):</strong>\r\n                                <div>Lat: {defect.exif_data.gps_coordinates.latitude?.toFixed(6)}</div>\r\n                                <div>Lng: {defect.exif_data.gps_coordinates.longitude?.toFixed(6)}</div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Camera Information */}\r\n                            {defect.exif_data?.camera_info && Object.keys(defect.exif_data.camera_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📷 Camera:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.camera_info.camera_make && (\r\n                                    <li>Make: {defect.exif_data.camera_info.camera_make}</li>\r\n                                  )}\r\n                                  {defect.exif_data.camera_info.camera_model && (\r\n                                    <li>Model: {defect.exif_data.camera_info.camera_model}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Technical Information */}\r\n                            {defect.exif_data?.technical_info && Object.keys(defect.exif_data.technical_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>⚙️ Technical:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.technical_info.iso && (\r\n                                    <li>ISO: {defect.exif_data.technical_info.iso}</li>\r\n                                  )}\r\n                                  {defect.exif_data.technical_info.exposure_time && (\r\n                                    <li>Exposure: {defect.exif_data.technical_info.exposure_time}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Basic Media Info */}\r\n                            {defect.exif_data?.basic_info && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📐 Dimensions:</strong> {defect.exif_data.basic_info.width} × {defect.exif_data.basic_info.height}\r\n                                {defect.exif_data.basic_info.format && (\r\n                                  <span> ({defect.exif_data.basic_info.format})</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Video-specific metadata */}\r\n                            {defect.media_type === 'video' && (\r\n                              <div className=\"mb-3\">\r\n                                <h6 className=\"text-info\">📹 Video Information</h6>\r\n                                <ul className=\"list-unstyled small\">\r\n                                  {defect.metadata?.format_info?.duration && (\r\n                                    <li><strong>Duration:</strong> {Math.round(defect.metadata.format_info.duration)}s</li>\r\n                                  )}\r\n                                  {defect.metadata?.basic_info?.width && defect.metadata?.basic_info?.height && (\r\n                                    <li><strong>Resolution:</strong> {defect.metadata.basic_info.width}x{defect.metadata.basic_info.height}</li>\r\n                                  )}\r\n                                  {defect.metadata?.format_info?.format_name && (\r\n                                    <li><strong>Format:</strong> {defect.metadata.format_info.format_name.toUpperCase()}</li>\r\n                                  )}\r\n                                  {defect.video_id && (\r\n                                    <li><strong>Video ID:</strong> {defect.video_id}</li>\r\n                                  )}\r\n                                  {/* Show detection counts for videos */}\r\n                                  {defect.model_outputs && (\r\n                                    <>\r\n                                      {defect.model_outputs.potholes && defect.model_outputs.potholes.length > 0 && (\r\n                                        <li><strong>Potholes Detected:</strong> {defect.model_outputs.potholes.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.cracks && defect.model_outputs.cracks.length > 0 && (\r\n                                        <li><strong>Cracks Detected:</strong> {defect.model_outputs.cracks.length}</li>\r\n                                      )}\r\n                                      {defect.model_outputs.kerbs && defect.model_outputs.kerbs.length > 0 && (\r\n                                        <li><strong>Kerbs Detected:</strong> {defect.model_outputs.kerbs.length}</li>\r\n                                      )}\r\n                                    </>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Action Buttons */}\r\n                      <div className=\"d-flex gap-2\">\r\n                        <Link\r\n                          to={`/view/${defect.image_id}`}\r\n                          className=\"btn btn-sm btn-primary\"\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                        {/* Only show 'View Original' button for non-video entries */}\r\n                        {defect.media_type !== 'video' && defect.original_image_full_url && (\r\n                          <a\r\n                            href={defect.original_image_full_url}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"btn btn-sm btn-outline-secondary\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            View Original\r\n                          </a>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Popup>\r\n                </Marker>\r\n                );\r\n              })}\r\n            </MapContainer>\r\n            </MapErrorBoundary>\r\n          </div>\r\n        )}\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DefectMap; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACvE,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,SAAStB,KAAK,CAACuB,SAAS,CAAC;EAC7CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC;EAC/C;EAEA,OAAOC,wBAAwBA,CAACD,KAAK,EAAE;IACrC,OAAO;MAAED,QAAQ,EAAE,IAAI;MAAEC;IAAM,CAAC;EAClC;EAEAE,iBAAiBA,CAACF,KAAK,EAAEG,SAAS,EAAE;IAClCC,OAAO,CAACJ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,EAAEG,SAAS,CAAC;EACxE;EAEAE,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACER,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChB,OAAA;UAAAgB,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BpB,OAAA;UAAAgB,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnEpB,OAAA;UACEe,SAAS,EAAC,wBAAwB;UAClCM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAR,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEV;IAEA,OAAO,IAAI,CAACd,KAAK,CAACU,QAAQ;EAC5B;AACF;;AAEA;AACA,SAASS,UAAUA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC9B,MAAMC,GAAG,GAAGvC,MAAM,CAAC,CAAC;EAEpBP,SAAS,CAAC,MAAM;IACd;IACA,IAAI4C,MAAM,EAAE;MACVA,MAAM,CAACG,OAAO,GAAGD,GAAG;IACtB;;IAEA;IACA,MAAME,EAAE,GAAGC,UAAU,CAAC,MAAM;MAC1B,IAAI;QACF,IAAIH,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,gCAAgC,EAAExB,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,MAAMyB,EAAE,GAAGH,UAAU,CAAC,MAAM;MAC1B,IAAI;QACF,IAAIH,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,gCAAgC,EAAExB,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA,MAAM0B,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI;QACF,IAAIP,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,mBAAmB,EAAExB,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDa,MAAM,CAACc,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;;IAE3C;IACA,MAAME,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIT,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,qBAAqB,EAAExB,KAAK,CAAC;MAC5C;IACF,CAAC;IAED,MAAM6B,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIV,GAAG,IAAIA,GAAG,CAACI,cAAc,EAAE;UAC7BJ,GAAG,CAACI,cAAc,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdI,OAAO,CAACoB,IAAI,CAAC,qBAAqB,EAAExB,KAAK,CAAC;MAC5C;IACF,CAAC;IAED,IAAImB,GAAG,EAAE;MACPA,GAAG,CAACW,EAAE,CAAC,SAAS,EAAEF,SAAS,CAAC;MAC5BT,GAAG,CAACW,EAAE,CAAC,SAAS,EAAED,SAAS,CAAC;IAC9B;IAEA,OAAO,MAAM;MACXE,YAAY,CAACV,EAAE,CAAC;MAChBU,YAAY,CAACN,EAAE,CAAC;MAChBZ,MAAM,CAACmB,mBAAmB,CAAC,QAAQ,EAAEN,QAAQ,CAAC;MAC9C,IAAIP,GAAG,EAAE;QACP,IAAI;UACFA,GAAG,CAACc,GAAG,CAAC,SAAS,EAAEL,SAAS,CAAC;UAC7BT,GAAG,CAACc,GAAG,CAAC,SAAS,EAAEJ,SAAS,CAAC;QAC/B,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdI,OAAO,CAACoB,IAAI,CAAC,oBAAoB,EAAExB,KAAK,CAAC;QAC3C;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACmB,GAAG,EAAEF,MAAM,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AAHAC,EAAA,CAvFSF,UAAU;EAAA,QACLpC,MAAM;AAAA;AAAAsD,EAAA,GADXlB,UAAU;AA2FnB,MAAMmB,uBAAuB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyB,QAAQ,EAAEyC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAMuE,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAE5B1C,OAAO,CAAC2C,GAAG,CAAC,kCAAkC,EAAED,UAAU,CAACE,QAAQ,CAAC;;IAEpE;IACA,IAAIF,UAAU,CAACG,4BAA4B,EAAE;MAC3C7C,OAAO,CAAC2C,GAAG,CAAC,uCAAuC,CAAC;MACpD,OAAOD,UAAU,CAACG,4BAA4B;IAChD;;IAEA;IACA,IAAIH,UAAU,CAACI,uBAAuB,EAAE;MACtC9C,OAAO,CAAC2C,GAAG,CAAC,yBAAyB,CAAC;MACtC;MACA,MAAMI,QAAQ,GAAGL,UAAU,CAACI,uBAAuB,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9D,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;MACrE,IAAIH,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAE;QAC3D,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,KAAK,CAACN,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;QACvD,MAAMC,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;QAC1EtD,OAAO,CAAC2C,GAAG,CAAC,sCAAsC,EAAEc,QAAQ,CAAC;QAC7D,OAAOA,QAAQ;MACjB;IACF;;IAEA;IACA,IAAIf,UAAU,CAACiB,qBAAqB,EAAE;MACpC3D,OAAO,CAAC2C,GAAG,CAAC,uBAAuB,CAAC;MACpC,MAAMW,KAAK,GAAGZ,UAAU,CAACiB,qBAAqB;MAC9C,MAAMC,UAAU,GAAGN,KAAK,CAACN,KAAK,CAAC,GAAG,CAAC,CAACjC,GAAG,CAACoC,IAAI,IAAIO,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;MACnF,MAAMC,QAAQ,GAAG,8BAA8BG,UAAU,EAAE;MAC3D5D,OAAO,CAAC2C,GAAG,CAAC,oCAAoC,EAAEc,QAAQ,CAAC;MAC3D,OAAOA,QAAQ;IACjB;;IAEA;IACA,IAAIf,UAAU,CAACI,uBAAuB,EAAE;MACtC9C,OAAO,CAAC2C,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAOD,UAAU,CAACI,uBAAuB;IAC3C;;IAEA;IACA,IAAIJ,UAAU,CAACmB,iBAAiB,EAAE;MAChC7D,OAAO,CAAC2C,GAAG,CAAC,2BAA2B,CAAC;MACxC,OAAO,2BAA2BD,UAAU,CAACmB,iBAAiB,EAAE;IAClE;IAEA7D,OAAO,CAACoB,IAAI,CAAC,wCAAwC,EAAEsB,UAAU,CAACE,QAAQ,CAAC;IAC3E,OAAO,IAAI;EACb,CAAC;;EAED;EACA3E,SAAS,CAAC,MAAM;IACd,MAAM6F,GAAG,GAAGrB,gBAAgB,CAACT,MAAM,CAAC;IACpChC,OAAO,CAAC2C,GAAG,CAAC,oCAAoC,EAAEmB,GAAG,CAAC;IACtD3B,kBAAkB,CAAC2B,GAAG,CAAC;IACvB1B,WAAW,CAAC,KAAK,CAAC;IAClBE,YAAY,CAAC,IAAI,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAM+B,gBAAgB,GAAIC,CAAC,IAAK;IAC9BhE,OAAO,CAACoB,IAAI,CAAC,oCAAoCmB,gBAAgB,GAAG,CAAC,IAAI,EAAEL,eAAe,CAAC;IAE3F,IAAIK,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAM0B,QAAQ,GAAGjC,MAAM,CAAC6B,iBAAiB;MACzC,IAAII,QAAQ,IAAI,CAAC/B,eAAe,CAACkB,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtD,MAAMc,SAAS,GAAG,2BAA2BD,QAAQ,EAAE;QACvDjE,OAAO,CAAC2C,GAAG,CAAC,4BAA4B,EAAEuB,SAAS,CAAC;QACpD/B,kBAAkB,CAAC+B,SAAS,CAAC;QAC7B1B,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA;IACAxC,OAAO,CAACJ,KAAK,CAAC,0CAA0C,EAAEoC,MAAM,CAACY,QAAQ,CAAC;IAC1ER,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BnE,OAAO,CAAC2C,GAAG,CAAC,kCAAkC,EAAET,eAAe,CAAC;IAChEI,YAAY,CAAC,KAAK,CAAC;IACnBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,IAAI,CAACF,eAAe,EAAE;IACpB,oBACE/C,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhB,OAAA;UAAGe,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,wBAClC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI8B,SAAS,EAAE;IACb,oBACElD,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,kDAAkD;QAACkE,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAlE,QAAA,gBAC9FhB,OAAA,CAACH,OAAO;UAACsF,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,IAAI;UAACC,OAAO,EAAC;QAAS;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DpB,OAAA;UAAMe,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIZ,QAAQ,EAAE;IACZ,oBACER,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhB,OAAA;QAAKe,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DhB,OAAA;UAAGe,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sBACjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BhB,OAAA;MACEsF,GAAG,EAAEvC,eAAgB;MACrBwC,GAAG,EAAC,cAAc;MAClBxE,SAAS,EAAC,0BAA0B;MACpCkE,KAAK,EAAE;QAAEO,SAAS,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAChDC,OAAO,EAAEd,gBAAiB;MAC1Be,MAAM,EAAEX;IAAgB;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFpB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhB,OAAA;QAAOe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAChEgC,gBAAgB,GAAG,CAAC,iBACnBpD,OAAA;QAAAgB,QAAA,eACEhB,OAAA;UAAOe,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA0B,GAAA,CAzJMF,uBAAuB;AAAAgD,GAAA,GAAvBhD,uBAAuB;AA0J7B,OAAOrD,CAAC,CAACsG,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CzG,CAAC,CAACsG,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,KAAK;EACnD,MAAMC,QAAQ,GAAGD,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAME,UAAU,GAAGF,OAAO;EACxB;EACA,qEAAqED,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA,8CAA8CF,KAAK;AACnD,sDAAsDA,KAAK;AAC3D,WAAW;EACP;EACA,qEAAqEA,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA,WAAW;EAET,OAAOjH,CAAC,CAACmH,IAAI,CAAC;IACZP,OAAO,EAAE,oCAAoC5B,kBAAkB,CAACkC,UAAU,CAAC,EAAE;IAC7ED,QAAQ,EAAEA,QAAQ;IAClBG,UAAU,EAAE,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxCI,WAAW,EAAE,CAAC,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,KAAK,GAAG;EACZC,OAAO,EAAET,gBAAgB,CAAC,SAAS,CAAC;EAAE;EACtCU,KAAK,EAAEV,gBAAgB,CAAC,SAAS,CAAC;EAAI;EACtCW,IAAI,EAAEX,gBAAgB,CAAC,SAAS,CAAC;EAAK;EACtC;EACA,eAAe,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAE;EACpD,aAAa,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAI;EACpD,YAAY,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAK;AACtD,CAAC;AAED,SAASY,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,GAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuI,OAAO,EAAEC,UAAU,CAAC,GAAGxI,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAE+G,QAAQ,CAAC,GAAGzI,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0I,MAAM,CAAC,GAAG1I,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2I,IAAI,CAAC,GAAG3I,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM2C,MAAM,GAAG1C,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAAC2I,SAAS,EAAEC,YAAY,CAAC,GAAG7I,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8I,OAAO,EAAEC,UAAU,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgJ,YAAY,EAAEC,eAAe,CAAC,GAAGjJ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkJ,SAAS,EAAEC,YAAY,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrJ,QAAQ,CAAC;IACzD+H,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMqB,eAAe,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACtD,IAAI;MAAA,IAAAC,qBAAA;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIgB,MAAM,GAAG,CAAC,CAAC;MACf,IAAIb,SAAS,EAAEa,MAAM,CAACC,UAAU,GAAGd,SAAS;MAC5C,IAAIE,OAAO,EAAEW,MAAM,CAACE,QAAQ,GAAGb,OAAO;MACtC,IAAIE,YAAY,EAAES,MAAM,CAACG,QAAQ,GAAGZ,YAAY;MAChD,IAAIb,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;;MAE5C;MACAJ,MAAM,CAACM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIV,YAAY,EAAE;QAChBE,MAAM,CAACS,MAAM,GAAG,MAAM;QACtBT,MAAM,CAACU,QAAQ,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MAC3D;MAEAzI,OAAO,CAAC2C,GAAG,CAAC,sCAAsC,EAAEgF,MAAM,CAAC;MAC3D,MAAMe,QAAQ,GAAG,MAAMjK,KAAK,CAACkK,GAAG,CAAC,4BAA4B,EAAE;QAC7DhB,MAAM;QACN;QACAiB,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACF5I,OAAO,CAAC2C,GAAG,CAAC,2BAA2B,EAAE;QACvCkG,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;QAC9BE,WAAW,EAAEL,QAAQ,CAACI,IAAI,CAACE,YAAY;QACvCC,UAAU,GAAAvB,qBAAA,GAAEgB,QAAQ,CAACI,IAAI,CAACI,MAAM,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsBrE;MACpC,CAAC,CAAC;MAEF,IAAIqF,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzB;QACA,MAAMM,gBAAgB,GAAG,EAAE;QAE3BT,QAAQ,CAACI,IAAI,CAACI,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;UACpC,IAAI;YACF;YACArJ,OAAO,CAAC2C,GAAG,CAAC,iBAAiB0G,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACzG,QAAQ,iBAAiByG,KAAK,CAACE,WAAW,UAAUF,KAAK,CAACG,IAAI,EAAE,CAAC;YACnI,IAAIH,KAAK,CAACE,WAAW,IAAIF,KAAK,CAACE,WAAW,KAAK,eAAe,EAAE;cAAA,IAAAE,gBAAA;cAC9D,IAAIC,GAAG,EAAEC,GAAG;;cAEZ;cACA,KAAAF,gBAAA,GAAIJ,KAAK,CAACO,SAAS,cAAAH,gBAAA,eAAfA,gBAAA,CAAiBI,eAAe,EAAE;gBACpCH,GAAG,GAAGL,KAAK,CAACO,SAAS,CAACC,eAAe,CAACC,QAAQ;gBAC9CH,GAAG,GAAGN,KAAK,CAACO,SAAS,CAACC,eAAe,CAACE,SAAS;gBAC/C/J,OAAO,CAAC2C,GAAG,CAAC,qCAAqC0G,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACzG,QAAQ,MAAM8G,GAAG,KAAKC,GAAG,GAAG,CAAC;cACrH,CAAC,MAAM;gBACL;gBACA;gBACA,IAAI,OAAON,KAAK,CAACE,WAAW,KAAK,QAAQ,EAAE;kBACzC;kBACA,MAAMS,MAAM,GAAGX,KAAK,CAACE,WAAW,CAACvG,KAAK,CAAC,GAAG,CAAC;kBAC3C,IAAIgH,MAAM,CAAC3G,MAAM,KAAK,CAAC,EAAE;oBACvBqG,GAAG,GAAGO,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;oBAClCP,GAAG,GAAGM,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;kBACpC;gBACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACf,KAAK,CAACE,WAAW,CAAC,IAAIF,KAAK,CAACE,WAAW,CAAClG,MAAM,KAAK,CAAC,EAAE;kBAC7E;kBACAqG,GAAG,GAAGO,UAAU,CAACZ,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC;kBACtCI,GAAG,GAAGM,UAAU,CAACZ,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,MAAM,IAAI,OAAOF,KAAK,CAACE,WAAW,KAAK,QAAQ,IAAIF,KAAK,CAACE,WAAW,CAACG,GAAG,IAAIL,KAAK,CAACE,WAAW,CAACI,GAAG,EAAE;kBAClG;kBACAD,GAAG,GAAGO,UAAU,CAACZ,KAAK,CAACE,WAAW,CAACG,GAAG,CAAC;kBACvCC,GAAG,GAAGM,UAAU,CAACZ,KAAK,CAACE,WAAW,CAACI,GAAG,CAAC;gBACzC;gBACA3J,OAAO,CAAC2C,GAAG,CAAC,mCAAmC0G,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACzG,QAAQ,MAAM8G,GAAG,KAAKC,GAAG,GAAG,CAAC;cACnH;;cAEF;cACA,IAAI,CAACU,KAAK,CAACX,GAAG,CAAC,IAAI,CAACW,KAAK,CAACV,GAAG,CAAC,IAC1BD,GAAG,IAAI,CAAC,EAAE,IAAIA,GAAG,IAAI,EAAE,IACvBC,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;gBAC7B3J,OAAO,CAAC2C,GAAG,CAAC,2BAA2B0G,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACiB,EAAE,MAAMZ,GAAG,KAAKC,GAAG,mBAAmB,CAAC;gBACnHR,gBAAgB,CAACoB,IAAI,CAAC;kBACpBD,EAAE,EAAEjB,KAAK,CAACiB,EAAE;kBACZ1H,QAAQ,EAAEyG,KAAK,CAACzG,QAAQ;kBACxB4G,IAAI,EAAEH,KAAK,CAACG,IAAI;kBAChBgB,QAAQ,EAAE,CAACd,GAAG,EAAEC,GAAG,CAAC;kBACpBc,YAAY,EAAEpB,KAAK,CAACoB,YAAY;kBAChCC,SAAS,EAAE,IAAIxC,IAAI,CAACmB,KAAK,CAACqB,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;kBACrD7C,QAAQ,EAAEuB,KAAK,CAACvB,QAAQ;kBACxBjE,iBAAiB,EAAEwF,KAAK,CAACxF,iBAAiB;kBAC1C;kBACA+G,WAAW,EAAEvB,KAAK,CAACuB,WAAW;kBAC9B;kBACAC,gBAAgB,EAAExB,KAAK,CAACwB,gBAAgB;kBACxC;kBACAjB,SAAS,EAAEP,KAAK,CAACO,SAAS,IAAI,CAAC,CAAC;kBAChCkB,QAAQ,EAAEzB,KAAK,CAACyB,QAAQ,IAAI,CAAC,CAAC;kBAC9BxB,UAAU,EAAED,KAAK,CAACC,UAAU,IAAI,OAAO;kBACvCxG,uBAAuB,EAAEuG,KAAK,CAACvG;gBACjC,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL9C,OAAO,CAACoB,IAAI,CAAC,6BAA6BiI,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACiB,EAAE,GAAG,EAAEjB,KAAK,CAACE,WAAW,EAAE,eAAeG,GAAG,SAASC,GAAG,EAAE,CAAC;cAC5I;YACF,CAAC,MAAM;cACL3J,OAAO,CAAC2C,GAAG,CAAC,eAAe0G,KAAK,CAACC,UAAU,IAAI,OAAO,IAAID,KAAK,CAACiB,EAAE,iBAAiBjB,KAAK,CAACE,WAAW,EAAE,CAAC;YACzG;UACA,CAAC,CAAC,OAAOwB,UAAU,EAAE;YACnB/K,OAAO,CAACJ,KAAK,CAAC,4CAA4CyJ,KAAK,CAACiB,EAAE,GAAG,EAAES,UAAU,EAAE1B,KAAK,CAACE,WAAW,CAAC;UACvG;QACF,CAAC,CAAC;QAEFvJ,OAAO,CAAC2C,GAAG,CAAC,oBAAoB,EAAEwG,gBAAgB,CAAC9F,MAAM,CAAC;QAC1DmD,UAAU,CAAC2C,gBAAgB,CAAC;;QAE5B;QACA,IAAIA,gBAAgB,CAAC9F,MAAM,KAAK,CAAC,EAAE;UACjCsD,QAAQ,CAAC,qEAAqE,CAAC;QACjF;MACF,CAAC,MAAM;QACL3G,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAE8I,QAAQ,CAACI,IAAI,CAAC;QAC3DnC,QAAQ,CAAC,8BAA8B,IAAI+B,QAAQ,CAACI,IAAI,CAACkC,OAAO,IAAI,eAAe,CAAC,CAAC;MACvF;MAEAtE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOuE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZnL,OAAO,CAACJ,KAAK,CAAC,6BAA6B,EAAEqL,GAAG,CAAC;MACjDtE,QAAQ,CAAC,8BAA8B,IAAI,EAAAuE,aAAA,GAAAD,GAAG,CAACvC,QAAQ,cAAAwC,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcpC,IAAI,cAAAqC,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;MACvFtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0E,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMzD,MAAM,GAAG,CAAC,CAAC;MACjB,IAAItB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;MAE5C,MAAMW,QAAQ,GAAG,MAAMjK,KAAK,CAACkK,GAAG,CAAC,gBAAgB,EAAE;QAAEhB;MAAO,CAAC,CAAC;MAC9D,IAAIe,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;QACzBxB,YAAY,CAACqB,QAAQ,CAACI,IAAI,CAACuC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC,OAAOzL,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACd,MAAMqN,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC1I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD+D,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAAC1I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvDhD,OAAO,CAAC2C,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1E,SAAS,CAAC,MAAM;IACduJ,eAAe,CAAC,CAAC;IACjB4D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC/E,IAAI,CAAC,CAAC;;EAEV;EACApI,SAAS,CAAC,MAAM;IACd,MAAM0N,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC5L,OAAO,CAAC2C,GAAG,CAAC,+DAA+D,CAAC;MAC5E6E,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM;MACXxH,OAAO,CAAC2C,GAAG,CAAC,mCAAmC,CAAC;MAChDkJ,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAAC7E,SAAS,EAAEE,OAAO,EAAEE,YAAY,EAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC,CAAC;;EAElD;EACA,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvE,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMV,WAAW,GAAG,IAAIpD,IAAI,CAAC,CAAC;IAC9B,MAAMqD,aAAa,GAAG,IAAIrD,IAAI,CAAC,CAAC;IAChCqD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDxE,UAAU,CAACqE,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC1I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD+D,YAAY,CAACwE,aAAa,CAACG,WAAW,CAAC,CAAC,CAAC1I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvDmE,eAAe,CAAC,EAAE,CAAC;IACnBI,oBAAoB,CAAC;MACnBtB,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACAqB,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyE,4BAA4B,GAAIzC,IAAI,IAAK;IAC7CjC,oBAAoB,CAAC2E,WAAW,KAAK;MACnC,GAAGA,WAAW;MACd,CAAC1C,IAAI,GAAG,CAAC0C,WAAW,CAAC1C,IAAI;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM2C,eAAe,GAAG5F,OAAO,CAAC6F,MAAM,CAACpK,MAAM,IAC3CsF,iBAAiB,CAACtF,MAAM,CAACwH,IAAI,CAC/B,CAAC;EAED,oBACErK,OAAA,CAACR,IAAI;IAACuB,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC7ChB,OAAA,CAACR,IAAI,CAAC0N,MAAM;MAACnM,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5ChB,OAAA;QAAIe,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdpB,OAAA,CAACR,IAAI,CAAC2N,IAAI;MAAAnM,QAAA,gBACRhB,OAAA,CAACP,GAAG;QAACsB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhB,OAAA,CAACN,GAAG;UAAC0N,EAAE,EAAE,CAAE;UAACrM,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAKe,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrChB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAAC0N,KAAK;kBAAArM,QAAA,gBACThB,OAAA,CAACL,IAAI,CAAC2N,KAAK;oBAACvM,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrDpB,OAAA,CAACL,IAAI,CAAC4N,OAAO;oBACXlD,IAAI,EAAC,MAAM;oBACXmD,KAAK,EAAE7F,SAAU;oBACjB8F,QAAQ,EAAG5I,CAAC,IAAK+C,YAAY,CAAC/C,CAAC,CAAC6I,MAAM,CAACF,KAAK,CAAE;oBAC9CpI,IAAI,EAAC;kBAAI;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAAC0N,KAAK;kBAAArM,QAAA,gBACThB,OAAA,CAACL,IAAI,CAAC2N,KAAK;oBAACvM,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnDpB,OAAA,CAACL,IAAI,CAAC4N,OAAO;oBACXlD,IAAI,EAAC,MAAM;oBACXmD,KAAK,EAAE3F,OAAQ;oBACf4F,QAAQ,EAAG5I,CAAC,IAAKiD,UAAU,CAACjD,CAAC,CAAC6I,MAAM,CAACF,KAAK,CAAE;oBAC5CpI,IAAI,EAAC;kBAAI;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA,CAACN,GAAG;UAAC0N,EAAE,EAAE,CAAE;UAACrM,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAKe,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrChB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhB,OAAA,CAACL,IAAI,CAAC0N,KAAK;kBAAArM,QAAA,gBACThB,OAAA,CAACL,IAAI,CAAC2N,KAAK;oBAACvM,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDpB,OAAA,CAACL,IAAI,CAACgO,MAAM;oBACVH,KAAK,EAAEzF,YAAa;oBACpB0F,QAAQ,EAAG5I,CAAC,IAAKmD,eAAe,CAACnD,CAAC,CAAC6I,MAAM,CAACF,KAAK,CAAE;oBACjDpI,IAAI,EAAC,IAAI;oBAAApE,QAAA,gBAEThB,OAAA;sBAAQwN,KAAK,EAAC,EAAE;sBAAAxM,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAClC6G,SAAS,CAACrG,GAAG,CAAC,CAACsF,IAAI,EAAE0G,KAAK,kBACzB5N,OAAA;sBAAoBwN,KAAK,EAAEtG,IAAI,CAACyB,QAAS;sBAAA3H,QAAA,GACtCkG,IAAI,CAACyB,QAAQ,EAAC,IAAE,EAACzB,IAAI,CAAC0B,IAAI,EAAC,GAC9B;oBAAA,GAFagF,KAAK;sBAAA3M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpB,OAAA,CAACN,GAAG;UAAC0N,EAAE,EAAE,CAAE;UAACrM,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpChB,OAAA;YAAAgB,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BpB,OAAA;YAAKe,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDhB,OAAA;cAAKe,SAAS,EAAC,4BAA4B;cAACkE,KAAK,EAAE;gBAAE4I,SAAS,EAAE,GAAG;gBAAEC,WAAW,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAhN,QAAA,gBACxHhB,OAAA,CAACL,IAAI,CAACsO,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,gBAAgB;gBACnB+C,KAAK,EAAC,UAAU;gBAChBC,OAAO,EAAEhG,iBAAiB,CAACrB,OAAQ;gBACnC2G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,SAAS,CAAE;gBACxD/L,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFpB,OAAA,CAACL,IAAI,CAACsO,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,cAAc;gBACjB+C,KAAK,EAAC,QAAQ;gBACdC,OAAO,EAAEhG,iBAAiB,CAACpB,KAAM;gBACjC0G,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,OAAO,CAAE;gBACtD/L,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFpB,OAAA,CAACL,IAAI,CAACsO,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfc,EAAE,EAAC,aAAa;gBAChB+C,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAEhG,iBAAiB,CAACnB,IAAK;gBAChCyG,QAAQ,EAAEA,CAAA,KAAMX,4BAA4B,CAAC,MAAM,CAAE;gBACrD/L,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACP,GAAG;QAACsB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhB,OAAA,CAACN,GAAG;UAACqB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BhB,OAAA;YAAKe,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvChB,OAAA,CAACJ,MAAM;cACLyF,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACT/D,OAAO,EAAEuL,kBAAmB;cAC5B7L,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACJ,MAAM;cACLyF,OAAO,EAAC,mBAAmB;cAC3BD,IAAI,EAAC,IAAI;cACT/D,OAAO,EAAEwL,kBAAmB;cAC5B9L,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACJ,MAAM;cACLyF,OAAO,EAAC,SAAS;cACjBD,IAAI,EAAC,IAAI;cACT/D,OAAO,EAAEsL,aAAc;cACvByB,QAAQ,EAAE9G,OAAQ;cAClBvG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAErBsG,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAArG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACP,GAAG;QAAAuB,QAAA,eACFhB,OAAA,CAACN,GAAG;UAAAsB,QAAA,eACFhB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BhB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhB,OAAA;gBAAIe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAACkE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU;gBAAE;kBAAApN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAACkE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU;gBAAE;kBAAApN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,eAAe;kBAACkE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU;gBAAE;kBAAApN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhB,OAAA;gBAAIe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAACkE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU,CAAE;kBAAArN,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAACkE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU,CAAE;kBAAArN,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNpB,OAAA;gBAAKe,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhB,OAAA;kBAAKe,SAAS,EAAC,4BAA4B;kBAACkE,KAAK,EAAE;oBAAEoJ,eAAe,EAAE;kBAAU,CAAE;kBAAArN,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FpB,OAAA;kBAAAgB,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELkG,OAAO,gBACNtH,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhB,OAAA;UAAKe,SAAS,EAAC,6BAA6B;UAAC6H,IAAI,EAAC,QAAQ;UAAA5H,QAAA,eACxDhB,OAAA;YAAMe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJX,KAAK,gBACPT,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEjDpB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAACkE,KAAK,EAAE;UAAEqJ,MAAM,EAAE,OAAO;UAAEP,KAAK,EAAE,MAAM;UAAE1C,QAAQ,EAAE;QAAW,CAAE;QAAArK,QAAA,eAC7FhB,OAAA,CAACG,gBAAgB;UAAAa,QAAA,eACfhB,OAAA,CAACf,YAAY;YACbwI,MAAM,EAAEA,MAAO;YACfC,IAAI,EAAEA,IAAK;YACXzC,KAAK,EAAE;cAAEqJ,MAAM,EAAE,MAAM;cAAEP,KAAK,EAAE;YAAO,CAAE;YACzCQ,eAAe,EAAE,IAAK;YAAAvN,QAAA,gBAItBhB,OAAA,CAACyB,UAAU;cAACC,MAAM,EAAEA;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BpB,OAAA,CAACd,SAAS;cACRsP,WAAW,EAAC,yFAAyF;cACrG7J,GAAG,EAAC;YAAoD;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EAED4L,eAAe,CAACpL,GAAG,CAAEiB,MAAM,IAAK;cAAA,IAAA4L,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;cAC/B;cACA,MAAMC,OAAO,GAAG1M,MAAM,CAACsH,UAAU,KAAK,OAAO,GAAG,GAAGtH,MAAM,CAACwH,IAAI,QAAQ,GAAGxH,MAAM,CAACwH,IAAI;cACpF,MAAMmF,YAAY,GAAG3I,KAAK,CAAC0I,OAAO,CAAC,IAAI1I,KAAK,CAAChE,MAAM,CAACwH,IAAI,CAAC;cAEzD,oBACArK,OAAA,CAACb,MAAM;gBAELkM,QAAQ,EAAExI,MAAM,CAACwI,QAAS;gBAC1B3E,IAAI,EAAE8I,YAAa;gBAAAxO,QAAA,eAEnBhB,OAAA,CAACZ,KAAK;kBAACqG,QAAQ,EAAE,GAAI;kBAAAzE,QAAA,eACnBhB,OAAA;oBAAKe,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BhB,OAAA;sBAAAgB,QAAA,GAAK6B,MAAM,CAACwH,IAAI,CAACoF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG7M,MAAM,CAACwH,IAAI,CAACjG,KAAK,CAAC,CAAC,CAAC,EAAC,SAAO;oBAAA;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAG3EyB,MAAM,CAACsH,UAAU,KAAK,OAAO,iBAC5BnK,OAAA;sBAAKe,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,GAC9B6B,MAAM,CAAC8M,oBAAoB,gBAC1B3P,OAAA,CAAAE,SAAA;wBAAAc,QAAA,gBACEhB,OAAA;0BACEsF,GAAG,EAAE,0BAA0BzC,MAAM,CAAC8M,oBAAoB,EAAG;0BAC7DpK,GAAG,EAAC,iBAAiB;0BACrBxE,SAAS,EAAC,oCAAoC;0BAC9CkE,KAAK,EAAE;4BAAEO,SAAS,EAAE,OAAO;4BAAEC,QAAQ,EAAE,MAAM;4BAAEmK,SAAS,EAAE;0BAAQ,CAAE;0BACpElK,OAAO,EAAGb,CAAC,IAAK;4BACdhE,OAAO,CAACoB,IAAI,CAAC,iDAAiDY,MAAM,CAACY,QAAQ,EAAE,CAAC;4BAChFoB,CAAC,CAAC6I,MAAM,CAACzI,KAAK,CAAC4K,OAAO,GAAG,MAAM;4BAC/B;4BACA,MAAMC,QAAQ,GAAGjL,CAAC,CAAC6I,MAAM,CAACqC,kBAAkB;4BAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,CAACC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;8BACvEH,QAAQ,CAAC7K,KAAK,CAAC4K,OAAO,GAAG,OAAO;4BAClC;0BACF;wBAAE;0BAAA5O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFpB,OAAA;0BAAKe,SAAS,EAAC,uEAAuE;0BAACkE,KAAK,EAAE;4BAAE4K,OAAO,EAAE;0BAAO,CAAE;0BAAA7O,QAAA,gBAChHhB,OAAA;4BAAGe,SAAS,EAAC;0BAAc;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,gCAClC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACN,CAAC,gBAEHpB,OAAA;wBAAKe,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,gBAC3DhB,OAAA;0BAAGe,SAAS,EAAC;wBAAyB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3CpB,OAAA;0BAAAgB,QAAA,EAAK;wBAA6B;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CACN,eACDpB,OAAA;wBAAKe,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBhB,OAAA;0BAAOe,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAAC;wBAAkB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EAC9DyB,MAAM,CAACqN,QAAQ,iBACdlQ,OAAA;0BAAAgB,QAAA,eACEhB,OAAA;4BAAOe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAAC,YAAU,EAAC6B,MAAM,CAACqN,QAAQ;0BAAA;4BAAAjP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtE,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,EAGAyB,MAAM,CAACsH,UAAU,KAAK,OAAO,iBAC5BnK,OAAA,CAAC4C,uBAAuB;sBAACC,MAAM,EAAEA;oBAAO;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC3C,eAGDpB,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnDpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,gBACjChB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACyI,YAAY;wBAAA;0BAAArK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAAC0I,SAAS;wBAAA;0BAAAtK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAY;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAAC8F,QAAQ;wBAAA;0BAAA1H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAW;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACsH,UAAU;wBAAA;0BAAAlJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzDpB,OAAA;0BAAAgB,QAAA,gBAAIhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACwI,QAAQ,CAAC,CAAC,CAAC,CAAC8E,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACtN,MAAM,CAACwI,QAAQ,CAAC,CAAC,CAAC,CAAC8E,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAlP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,EAGLyB,MAAM,CAACwH,IAAI,KAAK,OAAO,IAAIxH,MAAM,CAAC4I,WAAW,iBAC5CzL,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7CpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAChCoP,MAAM,CAACC,OAAO,CAACxN,MAAM,CAAC4I,WAAW,CAAC,CAAC7J,GAAG,CAAC,CAAC,CAACyI,IAAI,EAAEiG,KAAK,CAAC,kBACpDtQ,OAAA;0BAAAgB,QAAA,gBAAehB,OAAA;4BAAAgB,QAAA,GAASqJ,IAAI,EAAC,GAAC;0BAAA;4BAAApJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACkP,KAAK;wBAAA,GAArCjG,IAAI;0BAAApJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAsC,CACpD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN,EAEAyB,MAAM,CAACwH,IAAI,KAAK,MAAM,IAAIxH,MAAM,CAAC6I,gBAAgB,iBAChD1L,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjDpB,OAAA;wBAAIe,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAChCoP,MAAM,CAACC,OAAO,CAACxN,MAAM,CAAC6I,gBAAgB,CAAC,CAAC9J,GAAG,CAAC,CAAC,CAAC2O,SAAS,EAAED,KAAK,CAAC,kBAC9DtQ,OAAA;0BAAAgB,QAAA,gBAAoBhB,OAAA;4BAAAgB,QAAA,GAASuP,SAAS,EAAC,GAAC;0BAAA;4BAAAtP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACkP,KAAK;wBAAA,GAA/CC,SAAS;0BAAAtP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAA2C,CAC9D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN,EAGA,CAACyB,MAAM,CAAC4H,SAAS,IAAI5H,MAAM,CAAC8I,QAAQ,kBACnC3L,OAAA;sBAAKe,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBhB,OAAA;wBAAIe,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtDpB,OAAA;wBAAKe,SAAS,EAAC,OAAO;wBAAAC,QAAA,GAEnB,EAAAyN,iBAAA,GAAA5L,MAAM,CAAC4H,SAAS,cAAAgE,iBAAA,uBAAhBA,iBAAA,CAAkB/D,eAAe,kBAChC1K,OAAA;0BAAKe,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxChB,OAAA;4BAAQe,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAAC;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACxDpB,OAAA;4BAAAgB,QAAA,GAAK,OAAK,GAAA0N,qBAAA,GAAC7L,MAAM,CAAC4H,SAAS,CAACC,eAAe,CAACC,QAAQ,cAAA+D,qBAAA,uBAAzCA,qBAAA,CAA2CyB,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAAlP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACvEpB,OAAA;4BAAAgB,QAAA,GAAK,OAAK,GAAA2N,sBAAA,GAAC9L,MAAM,CAAC4H,SAAS,CAACC,eAAe,CAACE,SAAS,cAAA+D,sBAAA,uBAA1CA,sBAAA,CAA4CwB,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAAlP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrE,CACN,EAGA,EAAAwN,kBAAA,GAAA/L,MAAM,CAAC4H,SAAS,cAAAmE,kBAAA,uBAAhBA,kBAAA,CAAkB4B,WAAW,KAAIJ,MAAM,CAACK,IAAI,CAAC5N,MAAM,CAAC4H,SAAS,CAAC+F,WAAW,CAAC,CAACtM,MAAM,GAAG,CAAC,iBACpFlE,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAU;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC3BpB,OAAA;4BAAIe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAC/B6B,MAAM,CAAC4H,SAAS,CAAC+F,WAAW,CAACE,WAAW,iBACvC1Q,OAAA;8BAAAgB,QAAA,GAAI,QAAM,EAAC6B,MAAM,CAAC4H,SAAS,CAAC+F,WAAW,CAACE,WAAW;4BAAA;8BAAAzP,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACzD,EACAyB,MAAM,CAAC4H,SAAS,CAAC+F,WAAW,CAACG,YAAY,iBACxC3Q,OAAA;8BAAAgB,QAAA,GAAI,SAAO,EAAC6B,MAAM,CAAC4H,SAAS,CAAC+F,WAAW,CAACG,YAAY;4BAAA;8BAAA1P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC3D;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN,EAGA,EAAAyN,kBAAA,GAAAhM,MAAM,CAAC4H,SAAS,cAAAoE,kBAAA,uBAAhBA,kBAAA,CAAkB+B,cAAc,KAAIR,MAAM,CAACK,IAAI,CAAC5N,MAAM,CAAC4H,SAAS,CAACmG,cAAc,CAAC,CAAC1M,MAAM,GAAG,CAAC,iBAC1FlE,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAa;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC9BpB,OAAA;4BAAIe,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GAC/B6B,MAAM,CAAC4H,SAAS,CAACmG,cAAc,CAACC,GAAG,iBAClC7Q,OAAA;8BAAAgB,QAAA,GAAI,OAAK,EAAC6B,MAAM,CAAC4H,SAAS,CAACmG,cAAc,CAACC,GAAG;4BAAA;8BAAA5P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACnD,EACAyB,MAAM,CAAC4H,SAAS,CAACmG,cAAc,CAACE,aAAa,iBAC5C9Q,OAAA;8BAAAgB,QAAA,GAAI,YAAU,EAAC6B,MAAM,CAAC4H,SAAS,CAACmG,cAAc,CAACE,aAAa;4BAAA;8BAAA7P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAClE;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN,EAGA,EAAA0N,kBAAA,GAAAjM,MAAM,CAAC4H,SAAS,cAAAqE,kBAAA,uBAAhBA,kBAAA,CAAkBiC,UAAU,kBAC3B/Q,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAAgB,QAAA,EAAQ;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAAC4H,SAAS,CAACsG,UAAU,CAAChD,KAAK,EAAC,QAAG,EAAClL,MAAM,CAAC4H,SAAS,CAACsG,UAAU,CAACzC,MAAM,EACxGzL,MAAM,CAAC4H,SAAS,CAACsG,UAAU,CAACC,MAAM,iBACjChR,OAAA;4BAAAgB,QAAA,GAAM,IAAE,EAAC6B,MAAM,CAAC4H,SAAS,CAACsG,UAAU,CAACC,MAAM,EAAC,GAAC;0BAAA;4BAAA/P,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACpD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN,EAGAyB,MAAM,CAACsH,UAAU,KAAK,OAAO,iBAC5BnK,OAAA;0BAAKe,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhB,OAAA;4BAAIe,SAAS,EAAC,WAAW;4BAAAC,QAAA,EAAC;0BAAoB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnDpB,OAAA;4BAAIe,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,GAChC,EAAA+N,gBAAA,GAAAlM,MAAM,CAAC8I,QAAQ,cAAAoD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBkC,WAAW,cAAAjC,qBAAA,uBAA5BA,qBAAA,CAA8BkC,QAAQ,kBACrClR,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAAC+H,IAAI,CAACgI,KAAK,CAACtO,MAAM,CAAC8I,QAAQ,CAACsF,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;4BAAA;8BAAAjQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CACvF,EACA,EAAA6N,iBAAA,GAAApM,MAAM,CAAC8I,QAAQ,cAAAsD,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB8B,UAAU,cAAA7B,qBAAA,uBAA3BA,qBAAA,CAA6BnB,KAAK,OAAAoB,iBAAA,GAAItM,MAAM,CAAC8I,QAAQ,cAAAwD,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB4B,UAAU,cAAA3B,qBAAA,uBAA3BA,qBAAA,CAA6Bd,MAAM,kBACxEtO,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAW;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAAC8I,QAAQ,CAACoF,UAAU,CAAChD,KAAK,EAAC,GAAC,EAAClL,MAAM,CAAC8I,QAAQ,CAACoF,UAAU,CAACzC,MAAM;4BAAA;8BAAArN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAC5G,EACA,EAAAiO,iBAAA,GAAAxM,MAAM,CAAC8I,QAAQ,cAAA0D,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB4B,WAAW,cAAA3B,qBAAA,uBAA5BA,qBAAA,CAA8B8B,WAAW,kBACxCpR,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAO;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAAC8I,QAAQ,CAACsF,WAAW,CAACG,WAAW,CAAC1B,WAAW,CAAC,CAAC;4BAAA;8BAAAzO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACzF,EACAyB,MAAM,CAACqN,QAAQ,iBACdlQ,OAAA;8BAAAgB,QAAA,gBAAIhB,OAAA;gCAAAgB,QAAA,EAAQ;8BAAS;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACqN,QAAQ;4BAAA;8BAAAjP,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACrD,EAEAyB,MAAM,CAACwO,aAAa,iBACnBrR,OAAA,CAAAE,SAAA;8BAAAc,QAAA,GACG6B,MAAM,CAACwO,aAAa,CAACC,QAAQ,IAAIzO,MAAM,CAACwO,aAAa,CAACC,QAAQ,CAACpN,MAAM,GAAG,CAAC,iBACxElE,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAkB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACwO,aAAa,CAACC,QAAQ,CAACpN,MAAM;8BAAA;gCAAAjD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CACnF,EACAyB,MAAM,CAACwO,aAAa,CAACE,MAAM,IAAI1O,MAAM,CAACwO,aAAa,CAACE,MAAM,CAACrN,MAAM,GAAG,CAAC,iBACpElE,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAgB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACwO,aAAa,CAACE,MAAM,CAACrN,MAAM;8BAAA;gCAAAjD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAC/E,EACAyB,MAAM,CAACwO,aAAa,CAACG,KAAK,IAAI3O,MAAM,CAACwO,aAAa,CAACG,KAAK,CAACtN,MAAM,GAAG,CAAC,iBAClElE,OAAA;gCAAAgB,QAAA,gBAAIhB,OAAA;kCAAAgB,QAAA,EAAQ;gCAAe;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAACyB,MAAM,CAACwO,aAAa,CAACG,KAAK,CAACtN,MAAM;8BAAA;gCAAAjD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAC7E;4BAAA,eACD,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,eAGDpB,OAAA;sBAAKe,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BhB,OAAA,CAACF,IAAI;wBACH2R,EAAE,EAAE,SAAS5O,MAAM,CAACY,QAAQ,EAAG;wBAC/B1C,SAAS,EAAC,wBAAwB;wBAClCM,OAAO,EAAGwD,CAAC,IAAKA,CAAC,CAAC6M,eAAe,CAAC,CAAE;wBAAA1Q,QAAA,EACrC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAENyB,MAAM,CAACsH,UAAU,KAAK,OAAO,IAAItH,MAAM,CAACc,uBAAuB,iBAC9D3D,OAAA;wBACE2R,IAAI,EAAE9O,MAAM,CAACc,uBAAwB;wBACrC+J,MAAM,EAAC,QAAQ;wBACfkE,GAAG,EAAC,qBAAqB;wBACzB7Q,SAAS,EAAC,kCAAkC;wBAC5CM,OAAO,EAAGwD,CAAC,IAAKA,CAAC,CAAC6M,eAAe,CAAC,CAAE;wBAAA1Q,QAAA,EACrC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC,GA5MHyB,MAAM,CAACsI,EAAE;gBAAAlK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6MR,CAAC;YAEX,CAAC,CAAC;UAAA,GA/NG,OAAOqG,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIC,IAAI,EAAE;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgOhC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX;AAAC+F,GAAA,CAvoBQF,SAAS;AAAA4K,GAAA,GAAT5K,SAAS;AAyoBlB,eAAeA,SAAS;AAAC,IAAAtE,EAAA,EAAAiD,GAAA,EAAAiM,GAAA;AAAAC,YAAA,CAAAnP,EAAA;AAAAmP,YAAA,CAAAlM,GAAA;AAAAkM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}