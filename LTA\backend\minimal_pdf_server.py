#!/usr/bin/env python3
"""
Minimal Flask server to test PDF generation endpoint
"""

from flask import Flask, jsonify, send_file
from flask_cors import CORS
import io
from datetime import datetime
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import PDF generation libraries directly
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT

def generate_pdf_report(defect_data, defect_type):
    """
    Generate a PDF report for defect details (standalone version)
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor=colors.darkblue
    )

    # Title
    story.append(Paragraph("Road Safety Audit - Defect Report", title_style))
    story.append(Spacer(1, 20))

    # Basic Information Section
    story.append(Paragraph("Basic Information", heading_style))

    basic_info_data = [
        ['Field', 'Value'],
        ['Defect ID', defect_data.get('image_id', 'N/A')],
        ['Type', defect_type.title()],
        ['Date Detected', str(defect_data.get('timestamp', 'N/A'))],
        ['Reported By', defect_data.get('username', 'N/A')],
        ['Role', defect_data.get('role', 'N/A')],
        ['Coordinates', str(defect_data.get('coordinates', 'N/A'))],
        ['Media Type', defect_data.get('media_type', 'N/A')],
    ]

    basic_info_table = Table(basic_info_data, colWidths=[2*inch, 4*inch])
    basic_info_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(basic_info_table)
    story.append(Spacer(1, 20))

    # Defect Details Section
    story.append(Paragraph("Defect Details", heading_style))

    # Pothole Details
    if defect_data.get('potholes'):
        story.append(Paragraph("Pothole Details", styles['Heading3']))
        pothole_data = [['ID', 'Area (cm²)', 'Depth (cm)', 'Volume (cm³)', 'Severity']]

        for i, pothole in enumerate(defect_data['potholes'], 1):
            pothole_data.append([
                pothole.get('pothole_id', f'Pothole {i}'),
                str(pothole.get('area_cm2', 'N/A')),
                str(pothole.get('depth_cm', 'N/A')),
                str(pothole.get('volume', 'N/A')),
                pothole.get('volume_range', 'N/A')
            ])

        pothole_table = Table(pothole_data, colWidths=[1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch])
        pothole_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(pothole_table)
        story.append(Spacer(1, 15))

        # Recommended Actions for Potholes
        story.append(Paragraph("Recommended Actions", styles['Heading4']))
        recommendations = [
            "• Clean out loose material",
            "• Apply tack coat",
            "• Fill with hot mix asphalt",
            "• Compact thoroughly"
        ]
        for rec in recommendations:
            story.append(Paragraph(rec, styles['Normal']))
        story.append(Paragraph("Priority: High", styles['Normal']))
        story.append(Spacer(1, 15))

    # Crack Details
    if defect_data.get('cracks'):
        story.append(Paragraph("Crack Details", styles['Heading3']))
        crack_data = [['ID', 'Type', 'Area (cm²)', 'Confidence', 'Severity']]

        for i, crack in enumerate(defect_data['cracks'], 1):
            crack_data.append([
                crack.get('crack_id', f'Crack {i}'),
                crack.get('crack_type', 'N/A'),
                str(crack.get('area_cm2', 'N/A')),
                str(crack.get('confidence', 'N/A')),
                crack.get('area_range', 'N/A')
            ])

        crack_table = Table(crack_data, colWidths=[1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch])
        crack_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightcoral),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(crack_table)
        story.append(Spacer(1, 15))

        # Recommended Actions for Cracks
        story.append(Paragraph("Recommended Actions", styles['Heading4']))
        recommendations = [
            "• Clean crack thoroughly",
            "• Apply crack sealant",
            "• Monitor for expansion",
            "• Consider overlay if extensive"
        ]
        for rec in recommendations:
            story.append(Paragraph(rec, styles['Normal']))
        story.append(Paragraph("Priority: Medium", styles['Normal']))
        story.append(Spacer(1, 15))

    # Summary Section
    story.append(Paragraph("Summary", heading_style))
    total_defects = len(defect_data.get('potholes', [])) + len(defect_data.get('cracks', [])) + len(defect_data.get('kerbs', []))

    summary_data = [
        ['Total Defects Detected', str(total_defects)],
        ['Potholes', str(len(defect_data.get('potholes', [])))],
        ['Cracks', str(len(defect_data.get('cracks', [])))],
        ['Kerbs', str(len(defect_data.get('kerbs', [])))],
    ]

    summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(summary_table)
    story.append(Spacer(1, 20))

    # Footer
    story.append(Paragraph(f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Paragraph("Generated by: Road Safety Audit System", styles['Normal']))

    # Build PDF
    doc.build(story)
    buffer.seek(0)
    return buffer

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    return jsonify({
        "message": "Minimal PDF Test Server",
        "status": "online"
    })

@app.route('/api/pavement/images/<image_id>/report', methods=['GET'])
def generate_defect_report(image_id):
    """
    Generate a PDF report for a specific defect with test data
    """
    try:
        print(f"📄 Generating PDF report for image ID: {image_id}")
        
        # Create sample defect data for testing
        sample_defect_data = {
            'image_id': image_id,
            'timestamp': datetime.now().isoformat(),
            'username': 'test_user',
            'role': 'Supervisor',
            'coordinates': '12.9716, 77.5946',
            'media_type': 'image',
            'potholes': [
                {
                    'pothole_id': 'pothole_1',
                    'area_cm2': 1032.14,
                    'depth_cm': 7.04,
                    'volume': 7271.15,
                    'volume_range': 'High'
                }
            ],
            'cracks': [
                {
                    'crack_id': 'crack_1',
                    'crack_type': 'Longitudinal',
                    'area_cm2': 245.67,
                    'confidence': 0.89,
                    'area_range': 'Medium'
                }
            ],
            'kerbs': []
        }
        
        # Generate PDF report
        pdf_buffer = generate_pdf_report(sample_defect_data, 'pothole')
        
        # Create filename
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"defect_report_{image_id}_{timestamp_str}.pdf"
        
        print(f"✅ PDF generated successfully, filename: {filename}")
        
        return send_file(
            pdf_buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )
        
    except Exception as e:
        print(f"❌ Error generating PDF report: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"Error generating PDF report: {str(e)}"
        }), 500

if __name__ == "__main__":
    print("🚀 Starting Minimal PDF Test Server on http://localhost:5000")
    print("📄 Test PDF endpoint: http://localhost:5000/api/pavement/images/test123/report")
    app.run(debug=True, host='0.0.0.0', port=5000)
